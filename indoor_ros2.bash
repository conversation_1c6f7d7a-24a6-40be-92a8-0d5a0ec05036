#!/bin/bash

# ROS2 Indoor Navigation Launch Script
# Author: Claude Code
# Description: Launch NR_Navigation system for indoor environment

set -e  # Exit on any error

echo "======================================="
echo "Starting Indoor Navigation System..."
echo "======================================="

# Check if we're in the correct directory
if [ ! -f "install/setup.bash" ]; then
    echo "Error: Please build the packages first using ./build_ros2.bash"
    exit 1
fi

# Source the workspace
source install/setup.bash
echo "Sourced workspace environment"

# Set indoor-specific parameters
export INDOOR_MAP_FILE="/root/DC200/nr_navigation/src/rrt_star_global_planner/maps/map.yaml"
#export ROS_DOMAIN_ID=42  # Use domain ID to avoid conflicts

echo "Configuration:"
echo "  - Environment: Indoor"
echo "  - Map file: $INDOOR_MAP_FILE"
echo "  - ROS Domain ID: $ROS_DOMAIN_ID"
echo "======================================="

# Launch the navigation system
echo "Launching indoor navigation system..."
ros2 launch launch/nr_navigation.launch.py \
    map_file:="$INDOOR_MAP_FILE" \
    use_sim_time:=false

echo "======================================="
echo "Indoor navigation system stopped."
echo "======================================="