#!/bin/bash

# ROS2 NR_Navigation System Management Script
# Author: <PERSON>
# Description: Main script for managing the NR_Navigation system

set -e  # Exit on any error

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_usage() {
    echo -e "${BLUE}NR_Navigation ROS2 System Management${NC}"
    echo -e "${BLUE}=====================================${NC}"
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build      - Build all packages"
    echo "  rebuild    - Clean and rebuild all packages"
    echo "  indoor     - Launch indoor navigation system"
    echo "  outdoor    - Launch outdoor navigation system"
    echo "  clean      - Clean build artifacts"
    echo "  test       - Run system tests"
    echo "  help       - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 indoor"
    echo "  $0 outdoor"
}

build_system() {
    echo -e "${GREEN}Building NR_Navigation system...${NC}"
    ./build_ros2.bash
}

rebuild_system() {
    echo -e "${GREEN}Rebuilding NR_Navigation system...${NC}"
    ./rebuild_ros2.bash
}

launch_indoor() {
    echo -e "${GREEN}Launching indoor navigation...${NC}"
    ./indoor_ros2.bash
}

launch_outdoor() {
    echo -e "${GREEN}Launching outdoor navigation...${NC}"
    ./outdoor_ros2.bash
}

clean_system() {
    echo -e "${YELLOW}Cleaning build artifacts...${NC}"
    rm -rf build/ install/ log/
    echo -e "${GREEN}Clean completed.${NC}"
}

test_system() {
    echo -e "${YELLOW}Running system tests...${NC}"
    if [ -f "install/setup.bash" ]; then
        source install/setup.bash
        echo "Testing package availability..."
        ros2 pkg list | grep -E "(yocs_velocity_smoother|terrain_analysis|global_traj_generate|rrt_star_global_planner|local_planner)" || {
            echo -e "${RED}Error: Some packages are missing. Please build first.${NC}"
            exit 1
        }
        echo -e "${GREEN}All packages found successfully.${NC}"
    else
        echo -e "${RED}Error: Packages not built. Please run 'build' first.${NC}"
        exit 1
    fi
}

# Main script logic
case "$1" in
    "build")
        build_system
        ;;
    "rebuild")
        rebuild_system
        ;;
    "indoor")
        launch_indoor
        ;;
    "outdoor")
        launch_outdoor
        ;;
    "clean")
        clean_system
        ;;
    "test")
        test_system
        ;;
    "help"|"-h"|"--help")
        print_usage
        ;;
    "")
        print_usage
        ;;
    *)
        echo -e "${RED}Error: Unknown command '$1'${NC}"
        print_usage
        exit 1
        ;;
esac