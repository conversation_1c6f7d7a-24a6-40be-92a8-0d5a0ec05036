#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch_ros.actions import Node


def generate_launch_description():
    # Get the package directory
    package_dir = get_package_share_directory('terrain_analysis')
    
    # Create the terrain analysis node
    terrain_analysis_node = Node(
        package='terrain_analysis',
        executable='terrain_analysis',
        name='terrain_analysis',
        parameters=[
            os.path.join(package_dir, 'config', 'terrain_analysis.yaml')
        ],
        output='screen'
    )
    
    return LaunchDescription([
        terrain_analysis_node
    ])