#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    # Get the launch directory
    pkg_share = FindPackageShare(package='local_planner').find('local_planner')
    
    # Declare launch arguments
    config_path_arg = DeclareLaunchArgument(
        'config_path',
        default_value=PathJoinSubstitution([pkg_share, 'config']),
        description='Path to config directory'
    )
    
    paths_path_arg = DeclareLaunchArgument(
        'paths_path',
        default_value=PathJoinSubstitution([pkg_share, 'paths']),
        description='Path to paths directory'
    )

    # Load parameter files
    local_planner_params = PathJoinSubstitution([
        LaunchConfiguration('config_path'),
        'local_planner.yaml'
    ])
    
    path_follower_params = PathJoinSubstitution([
        LaunchConfiguration('config_path'),
        'path_follower.yaml'
    ])
    
    calibration_params = PathJoinSubstitution([
        LaunchConfiguration('config_path'),
        'calibration.yaml'
    ])
    
    point_publish_params = PathJoinSubstitution([
        LaunchConfiguration('config_path'),
        'point_publish.yaml'
    ])

    # Node definitions
    local_planner_node = Node(
        package='local_planner',
        executable='localPlanner',
        name='localPlanner',
        output='screen',
        parameters=[
            local_planner_params,
            {'pathFolder': LaunchConfiguration('paths_path')}
        ]
    )

    path_follower_node = Node(
        package='local_planner',
        executable='pathFollower',
        name='pathFollower',
        output='screen',
        parameters=[path_follower_params]
    )

    # Static transform publishers
    vehicle_transform_node = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='vehicleTransPublisher',
        arguments=['0', '0', '0', '0', '0', '0', 'sensor', 'vehicle']
    )

    sensor_transform_node = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='sensorTransPublisher',
        arguments=['0', '0', '0', '-1.5707963', '0', '-1.5707963', 'sensor', 'camera']
    )

    # Optional nodes (commented out in original launch file, can be enabled as needed)
    calibration_node = Node(
        package='local_planner',
        executable='calibration',
        name='calibration',
        output='screen',
        parameters=[calibration_params],
        # Uncomment the line below to enable this node
        # condition=LaunchConfigurationEquals('enable_calibration', 'true')
    )
    
    point_publish_node = Node(
        package='local_planner',
        executable='pointPublish',
        name='pointPublish',
        output='screen',
        parameters=[point_publish_params],
        # Uncomment the line below to enable this node
        # condition=LaunchConfigurationEquals('enable_point_publish', 'true')
    )

    return LaunchDescription([
        config_path_arg,
        paths_path_arg,
        local_planner_node,
        path_follower_node,
        vehicle_transform_node,
        sensor_transform_node,
        # calibration_node,  # Uncomment to enable
        # point_publish_node,  # Uncomment to enable
    ])