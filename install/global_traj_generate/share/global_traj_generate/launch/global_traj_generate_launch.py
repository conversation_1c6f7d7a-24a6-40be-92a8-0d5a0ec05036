#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch_ros.actions import Node


def generate_launch_description():
    # Get the package directory
    package_dir = get_package_share_directory('global_traj_generate')
    
    # Create the global trajectory generator node
    global_traj_generate_node = Node(
        package='global_traj_generate',
        executable='global_traj_generate_node',
        name='global_traj_generate',
        parameters=[
            os.path.join(package_dir, 'config', 'global_traj_generate.yaml')
        ],
        output='screen'
    )
    
    return LaunchDescription([
        global_traj_generate_node
    ])