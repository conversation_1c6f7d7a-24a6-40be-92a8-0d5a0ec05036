# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_global_traj_generate_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED global_traj_generate_FOUND)
    # explicitly set it to FALSE, otherwise <PERSON><PERSON><PERSON> will set it to TRUE
    set(global_traj_generate_FOUND FALSE)
  elseif(NOT global_traj_generate_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(global_traj_generate_FOUND FALSE)
  endif()
  return()
endif()
set(_global_traj_generate_CONFIG_INCLUDED TRUE)

# output package information
if(NOT global_traj_generate_FIND_QUIETLY)
  message(STATUS "Found global_traj_generate: 0.0.0 (${global_traj_generate_DIR})")
endif()

# warn when using a deprecated package
if(NOT "" STREQUAL "")
  set(_msg "Package 'global_traj_generate' is deprecated")
  # append custom deprecation text if available
  if(NOT "" STREQUAL "TRUE")
    set(_msg "${_msg} ()")
  endif()
  # optionally quiet the deprecation message
  if(NOT ${global_traj_generate_DEPRECATED_QUIET})
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(global_traj_generate_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "rosidl_cmake-extras.cmake;ament_cmake_export_dependencies-extras.cmake;ament_cmake_export_include_directories-extras.cmake;ament_cmake_export_libraries-extras.cmake;ament_cmake_export_targets-extras.cmake;rosidl_cmake_export_typesupport_targets-extras.cmake;rosidl_cmake_export_typesupport_libraries-extras.cmake")
foreach(_extra ${_extras})
  include("${global_traj_generate_DIR}/${_extra}")
endforeach()
