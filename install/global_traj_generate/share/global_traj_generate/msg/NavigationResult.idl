// generated from rosidl_adapter/resource/msg.idl.em
// with input from global_traj_generate/msg/NavigationResult.msg
// generated code does not contain a copyright notice


module global_traj_generate {
  module msg {
    struct NavigationResult {
      @verbatim (language="comment", text=
        "点位编号")
      int32 point_id;

      double target_pose_x;

      double target_pose_y;

      @verbatim (language="comment", text=
        "预留")
      double target_pose_z;

      double target_yaw;

      double current_pose_x;

      double current_pose_y;

      @verbatim (language="comment", text=
        "预留")
      double current_pose_z;

      double current_yaw;

      @verbatim (language="comment", text=
        "0：到达目标点; 1：失败；")
      int32 nav_state;
    };
  };
};
