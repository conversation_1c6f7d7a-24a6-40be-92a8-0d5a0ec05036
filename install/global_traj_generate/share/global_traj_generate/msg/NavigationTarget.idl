// generated from rosidl_adapter/resource/msg.idl.em
// with input from global_traj_generate/msg/NavigationTarget.msg
// generated code does not contain a copyright notice


module global_traj_generate {
  module msg {
    struct NavigationTarget {
      @verbatim (language="comment", text=
        "1:导航启动 0：导航停止")
      int32 nav_mode;

      @verbatim (language="comment", text=
        "点位编号")
      int32 point_id;

      double pose_x;

      double pose_y;

      @verbatim (language="comment", text=
        "预留")
      double pose_z;

      double yaw;

      @verbatim (language="comment", text=
        "任务点属性 0: 过渡点 1: 任务点 2: 充电准备点 3: 充电点")
      int32 point_info;

      @verbatim (language="comment", text=
        "到达目标点使用的步态 0: 普通步态 1: 楼梯步态 2: 防滑步态 3: 匍匐步态")
      int32 gait;

      @verbatim (language="comment", text=
        "速度设置 0: 普通速 1: 低速 2: 高速")
      int32 speed;

      @verbatim (language="comment", text=
        "运动方式 0: 正走 1: 逆走")
      int32 manner;

      @verbatim (language="comment", text=
        "障碍处理模式 0: 避障 1: 停障")
      int32 obsmode;

      @verbatim (language="comment", text=
        "导航模式 0: 直线导航 1: 自主导航")
      int32 navmode;
    };
  };
};
