// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from global_traj_generate:msg/NavigationResult.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "global_traj_generate/msg/detail/navigation_result__struct.h"
#include "global_traj_generate/msg/detail/navigation_result__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool global_traj_generate__msg__navigation_result__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[61];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("global_traj_generate.msg._navigation_result.NavigationResult", full_classname_dest, 60) == 0);
  }
  global_traj_generate__msg__NavigationResult * ros_message = _ros_message;
  {  // point_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "point_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->point_id = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // target_pose_x
    PyObject * field = PyObject_GetAttrString(_pymsg, "target_pose_x");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->target_pose_x = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // target_pose_y
    PyObject * field = PyObject_GetAttrString(_pymsg, "target_pose_y");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->target_pose_y = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // target_pose_z
    PyObject * field = PyObject_GetAttrString(_pymsg, "target_pose_z");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->target_pose_z = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // target_yaw
    PyObject * field = PyObject_GetAttrString(_pymsg, "target_yaw");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->target_yaw = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // current_pose_x
    PyObject * field = PyObject_GetAttrString(_pymsg, "current_pose_x");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->current_pose_x = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // current_pose_y
    PyObject * field = PyObject_GetAttrString(_pymsg, "current_pose_y");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->current_pose_y = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // current_pose_z
    PyObject * field = PyObject_GetAttrString(_pymsg, "current_pose_z");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->current_pose_z = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // current_yaw
    PyObject * field = PyObject_GetAttrString(_pymsg, "current_yaw");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->current_yaw = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // nav_state
    PyObject * field = PyObject_GetAttrString(_pymsg, "nav_state");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->nav_state = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * global_traj_generate__msg__navigation_result__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of NavigationResult */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("global_traj_generate.msg._navigation_result");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "NavigationResult");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  global_traj_generate__msg__NavigationResult * ros_message = (global_traj_generate__msg__NavigationResult *)raw_ros_message;
  {  // point_id
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->point_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "point_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // target_pose_x
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->target_pose_x);
    {
      int rc = PyObject_SetAttrString(_pymessage, "target_pose_x", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // target_pose_y
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->target_pose_y);
    {
      int rc = PyObject_SetAttrString(_pymessage, "target_pose_y", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // target_pose_z
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->target_pose_z);
    {
      int rc = PyObject_SetAttrString(_pymessage, "target_pose_z", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // target_yaw
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->target_yaw);
    {
      int rc = PyObject_SetAttrString(_pymessage, "target_yaw", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // current_pose_x
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->current_pose_x);
    {
      int rc = PyObject_SetAttrString(_pymessage, "current_pose_x", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // current_pose_y
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->current_pose_y);
    {
      int rc = PyObject_SetAttrString(_pymessage, "current_pose_y", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // current_pose_z
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->current_pose_z);
    {
      int rc = PyObject_SetAttrString(_pymessage, "current_pose_z", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // current_yaw
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->current_yaw);
    {
      int rc = PyObject_SetAttrString(_pymessage, "current_yaw", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // nav_state
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->nav_state);
    {
      int rc = PyObject_SetAttrString(_pymessage, "nav_state", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
