# generated from rosidl_generator_py/resource/_idl.py.em
# with input from global_traj_generate:msg/NavigationResult.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_NavigationResult(type):
    """Metaclass of message 'NavigationResult'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('global_traj_generate')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'global_traj_generate.msg.NavigationResult')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__navigation_result
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__navigation_result
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__navigation_result
            cls._TYPE_SUPPORT = module.type_support_msg__msg__navigation_result
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__navigation_result

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class NavigationResult(metaclass=Metaclass_NavigationResult):
    """Message class 'NavigationResult'."""

    __slots__ = [
        '_point_id',
        '_target_pose_x',
        '_target_pose_y',
        '_target_pose_z',
        '_target_yaw',
        '_current_pose_x',
        '_current_pose_y',
        '_current_pose_z',
        '_current_yaw',
        '_nav_state',
    ]

    _fields_and_field_types = {
        'point_id': 'int32',
        'target_pose_x': 'double',
        'target_pose_y': 'double',
        'target_pose_z': 'double',
        'target_yaw': 'double',
        'current_pose_x': 'double',
        'current_pose_y': 'double',
        'current_pose_z': 'double',
        'current_yaw': 'double',
        'nav_state': 'int32',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.point_id = kwargs.get('point_id', int())
        self.target_pose_x = kwargs.get('target_pose_x', float())
        self.target_pose_y = kwargs.get('target_pose_y', float())
        self.target_pose_z = kwargs.get('target_pose_z', float())
        self.target_yaw = kwargs.get('target_yaw', float())
        self.current_pose_x = kwargs.get('current_pose_x', float())
        self.current_pose_y = kwargs.get('current_pose_y', float())
        self.current_pose_z = kwargs.get('current_pose_z', float())
        self.current_yaw = kwargs.get('current_yaw', float())
        self.nav_state = kwargs.get('nav_state', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.point_id != other.point_id:
            return False
        if self.target_pose_x != other.target_pose_x:
            return False
        if self.target_pose_y != other.target_pose_y:
            return False
        if self.target_pose_z != other.target_pose_z:
            return False
        if self.target_yaw != other.target_yaw:
            return False
        if self.current_pose_x != other.current_pose_x:
            return False
        if self.current_pose_y != other.current_pose_y:
            return False
        if self.current_pose_z != other.current_pose_z:
            return False
        if self.current_yaw != other.current_yaw:
            return False
        if self.nav_state != other.nav_state:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def point_id(self):
        """Message field 'point_id'."""
        return self._point_id

    @point_id.setter
    def point_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'point_id' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'point_id' field must be an integer in [-2147483648, 2147483647]"
        self._point_id = value

    @builtins.property
    def target_pose_x(self):
        """Message field 'target_pose_x'."""
        return self._target_pose_x

    @target_pose_x.setter
    def target_pose_x(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'target_pose_x' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'target_pose_x' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._target_pose_x = value

    @builtins.property
    def target_pose_y(self):
        """Message field 'target_pose_y'."""
        return self._target_pose_y

    @target_pose_y.setter
    def target_pose_y(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'target_pose_y' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'target_pose_y' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._target_pose_y = value

    @builtins.property
    def target_pose_z(self):
        """Message field 'target_pose_z'."""
        return self._target_pose_z

    @target_pose_z.setter
    def target_pose_z(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'target_pose_z' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'target_pose_z' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._target_pose_z = value

    @builtins.property
    def target_yaw(self):
        """Message field 'target_yaw'."""
        return self._target_yaw

    @target_yaw.setter
    def target_yaw(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'target_yaw' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'target_yaw' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._target_yaw = value

    @builtins.property
    def current_pose_x(self):
        """Message field 'current_pose_x'."""
        return self._current_pose_x

    @current_pose_x.setter
    def current_pose_x(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'current_pose_x' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'current_pose_x' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._current_pose_x = value

    @builtins.property
    def current_pose_y(self):
        """Message field 'current_pose_y'."""
        return self._current_pose_y

    @current_pose_y.setter
    def current_pose_y(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'current_pose_y' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'current_pose_y' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._current_pose_y = value

    @builtins.property
    def current_pose_z(self):
        """Message field 'current_pose_z'."""
        return self._current_pose_z

    @current_pose_z.setter
    def current_pose_z(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'current_pose_z' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'current_pose_z' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._current_pose_z = value

    @builtins.property
    def current_yaw(self):
        """Message field 'current_yaw'."""
        return self._current_yaw

    @current_yaw.setter
    def current_yaw(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'current_yaw' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'current_yaw' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._current_yaw = value

    @builtins.property
    def nav_state(self):
        """Message field 'nav_state'."""
        return self._nav_state

    @nav_state.setter
    def nav_state(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'nav_state' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'nav_state' field must be an integer in [-2147483648, 2147483647]"
        self._nav_state = value
