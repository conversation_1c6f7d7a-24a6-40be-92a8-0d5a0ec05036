// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from global_traj_generate:msg/NavigationTarget.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "global_traj_generate/msg/detail/navigation_target__struct.h"
#include "global_traj_generate/msg/detail/navigation_target__functions.h"


ROSIDL_GENERATOR_C_EXPORT
bool global_traj_generate__msg__navigation_target__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[61];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("global_traj_generate.msg._navigation_target.NavigationTarget", full_classname_dest, 60) == 0);
  }
  global_traj_generate__msg__NavigationTarget * ros_message = _ros_message;
  {  // nav_mode
    PyObject * field = PyObject_GetAttrString(_pymsg, "nav_mode");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->nav_mode = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // point_id
    PyObject * field = PyObject_GetAttrString(_pymsg, "point_id");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->point_id = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // pose_x
    PyObject * field = PyObject_GetAttrString(_pymsg, "pose_x");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->pose_x = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // pose_y
    PyObject * field = PyObject_GetAttrString(_pymsg, "pose_y");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->pose_y = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // pose_z
    PyObject * field = PyObject_GetAttrString(_pymsg, "pose_z");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->pose_z = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // yaw
    PyObject * field = PyObject_GetAttrString(_pymsg, "yaw");
    if (!field) {
      return false;
    }
    assert(PyFloat_Check(field));
    ros_message->yaw = PyFloat_AS_DOUBLE(field);
    Py_DECREF(field);
  }
  {  // point_info
    PyObject * field = PyObject_GetAttrString(_pymsg, "point_info");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->point_info = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // gait
    PyObject * field = PyObject_GetAttrString(_pymsg, "gait");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->gait = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // speed
    PyObject * field = PyObject_GetAttrString(_pymsg, "speed");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->speed = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // manner
    PyObject * field = PyObject_GetAttrString(_pymsg, "manner");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->manner = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // obsmode
    PyObject * field = PyObject_GetAttrString(_pymsg, "obsmode");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->obsmode = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }
  {  // navmode
    PyObject * field = PyObject_GetAttrString(_pymsg, "navmode");
    if (!field) {
      return false;
    }
    assert(PyLong_Check(field));
    ros_message->navmode = (int32_t)PyLong_AsLong(field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * global_traj_generate__msg__navigation_target__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of NavigationTarget */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("global_traj_generate.msg._navigation_target");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "NavigationTarget");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  global_traj_generate__msg__NavigationTarget * ros_message = (global_traj_generate__msg__NavigationTarget *)raw_ros_message;
  {  // nav_mode
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->nav_mode);
    {
      int rc = PyObject_SetAttrString(_pymessage, "nav_mode", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // point_id
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->point_id);
    {
      int rc = PyObject_SetAttrString(_pymessage, "point_id", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pose_x
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->pose_x);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pose_x", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pose_y
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->pose_y);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pose_y", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // pose_z
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->pose_z);
    {
      int rc = PyObject_SetAttrString(_pymessage, "pose_z", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // yaw
    PyObject * field = NULL;
    field = PyFloat_FromDouble(ros_message->yaw);
    {
      int rc = PyObject_SetAttrString(_pymessage, "yaw", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // point_info
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->point_info);
    {
      int rc = PyObject_SetAttrString(_pymessage, "point_info", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // gait
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->gait);
    {
      int rc = PyObject_SetAttrString(_pymessage, "gait", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // speed
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->speed);
    {
      int rc = PyObject_SetAttrString(_pymessage, "speed", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // manner
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->manner);
    {
      int rc = PyObject_SetAttrString(_pymessage, "manner", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // obsmode
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->obsmode);
    {
      int rc = PyObject_SetAttrString(_pymessage, "obsmode", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }
  {  // navmode
    PyObject * field = NULL;
    field = PyLong_FromLong(ros_message->navmode);
    {
      int rc = PyObject_SetAttrString(_pymessage, "navmode", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
