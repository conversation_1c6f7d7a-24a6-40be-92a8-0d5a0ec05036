# generated from rosidl_generator_py/resource/_idl.py.em
# with input from global_traj_generate:msg/NavigationTarget.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import math  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_NavigationTarget(type):
    """Metaclass of message 'NavigationTarget'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('global_traj_generate')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'global_traj_generate.msg.NavigationTarget')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__navigation_target
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__navigation_target
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__navigation_target
            cls._TYPE_SUPPORT = module.type_support_msg__msg__navigation_target
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__navigation_target

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class NavigationTarget(metaclass=Metaclass_NavigationTarget):
    """Message class 'NavigationTarget'."""

    __slots__ = [
        '_nav_mode',
        '_point_id',
        '_pose_x',
        '_pose_y',
        '_pose_z',
        '_yaw',
        '_point_info',
        '_gait',
        '_speed',
        '_manner',
        '_obsmode',
        '_navmode',
    ]

    _fields_and_field_types = {
        'nav_mode': 'int32',
        'point_id': 'int32',
        'pose_x': 'double',
        'pose_y': 'double',
        'pose_z': 'double',
        'yaw': 'double',
        'point_info': 'int32',
        'gait': 'int32',
        'speed': 'int32',
        'manner': 'int32',
        'obsmode': 'int32',
        'navmode': 'int32',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('double'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        self.nav_mode = kwargs.get('nav_mode', int())
        self.point_id = kwargs.get('point_id', int())
        self.pose_x = kwargs.get('pose_x', float())
        self.pose_y = kwargs.get('pose_y', float())
        self.pose_z = kwargs.get('pose_z', float())
        self.yaw = kwargs.get('yaw', float())
        self.point_info = kwargs.get('point_info', int())
        self.gait = kwargs.get('gait', int())
        self.speed = kwargs.get('speed', int())
        self.manner = kwargs.get('manner', int())
        self.obsmode = kwargs.get('obsmode', int())
        self.navmode = kwargs.get('navmode', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.nav_mode != other.nav_mode:
            return False
        if self.point_id != other.point_id:
            return False
        if self.pose_x != other.pose_x:
            return False
        if self.pose_y != other.pose_y:
            return False
        if self.pose_z != other.pose_z:
            return False
        if self.yaw != other.yaw:
            return False
        if self.point_info != other.point_info:
            return False
        if self.gait != other.gait:
            return False
        if self.speed != other.speed:
            return False
        if self.manner != other.manner:
            return False
        if self.obsmode != other.obsmode:
            return False
        if self.navmode != other.navmode:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def nav_mode(self):
        """Message field 'nav_mode'."""
        return self._nav_mode

    @nav_mode.setter
    def nav_mode(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'nav_mode' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'nav_mode' field must be an integer in [-2147483648, 2147483647]"
        self._nav_mode = value

    @builtins.property
    def point_id(self):
        """Message field 'point_id'."""
        return self._point_id

    @point_id.setter
    def point_id(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'point_id' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'point_id' field must be an integer in [-2147483648, 2147483647]"
        self._point_id = value

    @builtins.property
    def pose_x(self):
        """Message field 'pose_x'."""
        return self._pose_x

    @pose_x.setter
    def pose_x(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'pose_x' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'pose_x' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._pose_x = value

    @builtins.property
    def pose_y(self):
        """Message field 'pose_y'."""
        return self._pose_y

    @pose_y.setter
    def pose_y(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'pose_y' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'pose_y' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._pose_y = value

    @builtins.property
    def pose_z(self):
        """Message field 'pose_z'."""
        return self._pose_z

    @pose_z.setter
    def pose_z(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'pose_z' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'pose_z' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._pose_z = value

    @builtins.property
    def yaw(self):
        """Message field 'yaw'."""
        return self._yaw

    @yaw.setter
    def yaw(self, value):
        if __debug__:
            assert \
                isinstance(value, float), \
                "The 'yaw' field must be of type 'float'"
            assert not (value < -1.7976931348623157e+308 or value > 1.7976931348623157e+308) or math.isinf(value), \
                "The 'yaw' field must be a double in [-1.7976931348623157e+308, 1.7976931348623157e+308]"
        self._yaw = value

    @builtins.property
    def point_info(self):
        """Message field 'point_info'."""
        return self._point_info

    @point_info.setter
    def point_info(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'point_info' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'point_info' field must be an integer in [-2147483648, 2147483647]"
        self._point_info = value

    @builtins.property
    def gait(self):
        """Message field 'gait'."""
        return self._gait

    @gait.setter
    def gait(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'gait' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'gait' field must be an integer in [-2147483648, 2147483647]"
        self._gait = value

    @builtins.property
    def speed(self):
        """Message field 'speed'."""
        return self._speed

    @speed.setter
    def speed(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'speed' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'speed' field must be an integer in [-2147483648, 2147483647]"
        self._speed = value

    @builtins.property
    def manner(self):
        """Message field 'manner'."""
        return self._manner

    @manner.setter
    def manner(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'manner' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'manner' field must be an integer in [-2147483648, 2147483647]"
        self._manner = value

    @builtins.property
    def obsmode(self):
        """Message field 'obsmode'."""
        return self._obsmode

    @obsmode.setter
    def obsmode(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'obsmode' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'obsmode' field must be an integer in [-2147483648, 2147483647]"
        self._obsmode = value

    @builtins.property
    def navmode(self):
        """Message field 'navmode'."""
        return self._navmode

    @navmode.setter
    def navmode(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'navmode' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'navmode' field must be an integer in [-2147483648, 2147483647]"
        self._navmode = value
