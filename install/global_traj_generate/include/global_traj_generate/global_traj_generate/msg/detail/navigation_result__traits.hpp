// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from global_traj_generate:msg/NavigationResult.idl
// generated code does not contain a copyright notice

#ifndef GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__TRAITS_HPP_
#define GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "global_traj_generate/msg/detail/navigation_result__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace global_traj_generate
{

namespace msg
{

inline void to_flow_style_yaml(
  const NavigationResult & msg,
  std::ostream & out)
{
  out << "{";
  // member: point_id
  {
    out << "point_id: ";
    rosidl_generator_traits::value_to_yaml(msg.point_id, out);
    out << ", ";
  }

  // member: target_pose_x
  {
    out << "target_pose_x: ";
    rosidl_generator_traits::value_to_yaml(msg.target_pose_x, out);
    out << ", ";
  }

  // member: target_pose_y
  {
    out << "target_pose_y: ";
    rosidl_generator_traits::value_to_yaml(msg.target_pose_y, out);
    out << ", ";
  }

  // member: target_pose_z
  {
    out << "target_pose_z: ";
    rosidl_generator_traits::value_to_yaml(msg.target_pose_z, out);
    out << ", ";
  }

  // member: target_yaw
  {
    out << "target_yaw: ";
    rosidl_generator_traits::value_to_yaml(msg.target_yaw, out);
    out << ", ";
  }

  // member: current_pose_x
  {
    out << "current_pose_x: ";
    rosidl_generator_traits::value_to_yaml(msg.current_pose_x, out);
    out << ", ";
  }

  // member: current_pose_y
  {
    out << "current_pose_y: ";
    rosidl_generator_traits::value_to_yaml(msg.current_pose_y, out);
    out << ", ";
  }

  // member: current_pose_z
  {
    out << "current_pose_z: ";
    rosidl_generator_traits::value_to_yaml(msg.current_pose_z, out);
    out << ", ";
  }

  // member: current_yaw
  {
    out << "current_yaw: ";
    rosidl_generator_traits::value_to_yaml(msg.current_yaw, out);
    out << ", ";
  }

  // member: nav_state
  {
    out << "nav_state: ";
    rosidl_generator_traits::value_to_yaml(msg.nav_state, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const NavigationResult & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: point_id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "point_id: ";
    rosidl_generator_traits::value_to_yaml(msg.point_id, out);
    out << "\n";
  }

  // member: target_pose_x
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "target_pose_x: ";
    rosidl_generator_traits::value_to_yaml(msg.target_pose_x, out);
    out << "\n";
  }

  // member: target_pose_y
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "target_pose_y: ";
    rosidl_generator_traits::value_to_yaml(msg.target_pose_y, out);
    out << "\n";
  }

  // member: target_pose_z
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "target_pose_z: ";
    rosidl_generator_traits::value_to_yaml(msg.target_pose_z, out);
    out << "\n";
  }

  // member: target_yaw
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "target_yaw: ";
    rosidl_generator_traits::value_to_yaml(msg.target_yaw, out);
    out << "\n";
  }

  // member: current_pose_x
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "current_pose_x: ";
    rosidl_generator_traits::value_to_yaml(msg.current_pose_x, out);
    out << "\n";
  }

  // member: current_pose_y
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "current_pose_y: ";
    rosidl_generator_traits::value_to_yaml(msg.current_pose_y, out);
    out << "\n";
  }

  // member: current_pose_z
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "current_pose_z: ";
    rosidl_generator_traits::value_to_yaml(msg.current_pose_z, out);
    out << "\n";
  }

  // member: current_yaw
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "current_yaw: ";
    rosidl_generator_traits::value_to_yaml(msg.current_yaw, out);
    out << "\n";
  }

  // member: nav_state
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "nav_state: ";
    rosidl_generator_traits::value_to_yaml(msg.nav_state, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const NavigationResult & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace global_traj_generate

namespace rosidl_generator_traits
{

[[deprecated("use global_traj_generate::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const global_traj_generate::msg::NavigationResult & msg,
  std::ostream & out, size_t indentation = 0)
{
  global_traj_generate::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use global_traj_generate::msg::to_yaml() instead")]]
inline std::string to_yaml(const global_traj_generate::msg::NavigationResult & msg)
{
  return global_traj_generate::msg::to_yaml(msg);
}

template<>
inline const char * data_type<global_traj_generate::msg::NavigationResult>()
{
  return "global_traj_generate::msg::NavigationResult";
}

template<>
inline const char * name<global_traj_generate::msg::NavigationResult>()
{
  return "global_traj_generate/msg/NavigationResult";
}

template<>
struct has_fixed_size<global_traj_generate::msg::NavigationResult>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<global_traj_generate::msg::NavigationResult>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<global_traj_generate::msg::NavigationResult>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__TRAITS_HPP_
