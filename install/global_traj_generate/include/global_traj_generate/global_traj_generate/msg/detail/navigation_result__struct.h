﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from global_traj_generate:msg/NavigationResult.idl
// generated code does not contain a copyright notice

#ifndef GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__STRUCT_H_
#define GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/NavigationResult in the package global_traj_generate.
typedef struct global_traj_generate__msg__NavigationResult
{
  /// 点位编号
  int32_t point_id;
  double target_pose_x;
  double target_pose_y;
  /// 预留
  double target_pose_z;
  double target_yaw;
  double current_pose_x;
  double current_pose_y;
  /// 预留
  double current_pose_z;
  double current_yaw;
  /// 0：到达目标点; 1：失败；
  int32_t nav_state;
} global_traj_generate__msg__NavigationResult;

// Struct for a sequence of global_traj_generate__msg__NavigationResult.
typedef struct global_traj_generate__msg__NavigationResult__Sequence
{
  global_traj_generate__msg__NavigationResult * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} global_traj_generate__msg__NavigationResult__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__STRUCT_H_
