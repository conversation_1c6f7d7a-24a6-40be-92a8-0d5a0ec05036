// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from global_traj_generate:msg/NavigationResult.idl
// generated code does not contain a copyright notice
#include "global_traj_generate/msg/detail/navigation_result__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
global_traj_generate__msg__NavigationResult__init(global_traj_generate__msg__NavigationResult * msg)
{
  if (!msg) {
    return false;
  }
  // point_id
  // target_pose_x
  // target_pose_y
  // target_pose_z
  // target_yaw
  // current_pose_x
  // current_pose_y
  // current_pose_z
  // current_yaw
  // nav_state
  return true;
}

void
global_traj_generate__msg__NavigationResult__fini(global_traj_generate__msg__NavigationResult * msg)
{
  if (!msg) {
    return;
  }
  // point_id
  // target_pose_x
  // target_pose_y
  // target_pose_z
  // target_yaw
  // current_pose_x
  // current_pose_y
  // current_pose_z
  // current_yaw
  // nav_state
}

bool
global_traj_generate__msg__NavigationResult__are_equal(const global_traj_generate__msg__NavigationResult * lhs, const global_traj_generate__msg__NavigationResult * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // point_id
  if (lhs->point_id != rhs->point_id) {
    return false;
  }
  // target_pose_x
  if (lhs->target_pose_x != rhs->target_pose_x) {
    return false;
  }
  // target_pose_y
  if (lhs->target_pose_y != rhs->target_pose_y) {
    return false;
  }
  // target_pose_z
  if (lhs->target_pose_z != rhs->target_pose_z) {
    return false;
  }
  // target_yaw
  if (lhs->target_yaw != rhs->target_yaw) {
    return false;
  }
  // current_pose_x
  if (lhs->current_pose_x != rhs->current_pose_x) {
    return false;
  }
  // current_pose_y
  if (lhs->current_pose_y != rhs->current_pose_y) {
    return false;
  }
  // current_pose_z
  if (lhs->current_pose_z != rhs->current_pose_z) {
    return false;
  }
  // current_yaw
  if (lhs->current_yaw != rhs->current_yaw) {
    return false;
  }
  // nav_state
  if (lhs->nav_state != rhs->nav_state) {
    return false;
  }
  return true;
}

bool
global_traj_generate__msg__NavigationResult__copy(
  const global_traj_generate__msg__NavigationResult * input,
  global_traj_generate__msg__NavigationResult * output)
{
  if (!input || !output) {
    return false;
  }
  // point_id
  output->point_id = input->point_id;
  // target_pose_x
  output->target_pose_x = input->target_pose_x;
  // target_pose_y
  output->target_pose_y = input->target_pose_y;
  // target_pose_z
  output->target_pose_z = input->target_pose_z;
  // target_yaw
  output->target_yaw = input->target_yaw;
  // current_pose_x
  output->current_pose_x = input->current_pose_x;
  // current_pose_y
  output->current_pose_y = input->current_pose_y;
  // current_pose_z
  output->current_pose_z = input->current_pose_z;
  // current_yaw
  output->current_yaw = input->current_yaw;
  // nav_state
  output->nav_state = input->nav_state;
  return true;
}

global_traj_generate__msg__NavigationResult *
global_traj_generate__msg__NavigationResult__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  global_traj_generate__msg__NavigationResult * msg = (global_traj_generate__msg__NavigationResult *)allocator.allocate(sizeof(global_traj_generate__msg__NavigationResult), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(global_traj_generate__msg__NavigationResult));
  bool success = global_traj_generate__msg__NavigationResult__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
global_traj_generate__msg__NavigationResult__destroy(global_traj_generate__msg__NavigationResult * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    global_traj_generate__msg__NavigationResult__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
global_traj_generate__msg__NavigationResult__Sequence__init(global_traj_generate__msg__NavigationResult__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  global_traj_generate__msg__NavigationResult * data = NULL;

  if (size) {
    data = (global_traj_generate__msg__NavigationResult *)allocator.zero_allocate(size, sizeof(global_traj_generate__msg__NavigationResult), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = global_traj_generate__msg__NavigationResult__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        global_traj_generate__msg__NavigationResult__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
global_traj_generate__msg__NavigationResult__Sequence__fini(global_traj_generate__msg__NavigationResult__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      global_traj_generate__msg__NavigationResult__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

global_traj_generate__msg__NavigationResult__Sequence *
global_traj_generate__msg__NavigationResult__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  global_traj_generate__msg__NavigationResult__Sequence * array = (global_traj_generate__msg__NavigationResult__Sequence *)allocator.allocate(sizeof(global_traj_generate__msg__NavigationResult__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = global_traj_generate__msg__NavigationResult__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
global_traj_generate__msg__NavigationResult__Sequence__destroy(global_traj_generate__msg__NavigationResult__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    global_traj_generate__msg__NavigationResult__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
global_traj_generate__msg__NavigationResult__Sequence__are_equal(const global_traj_generate__msg__NavigationResult__Sequence * lhs, const global_traj_generate__msg__NavigationResult__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!global_traj_generate__msg__NavigationResult__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
global_traj_generate__msg__NavigationResult__Sequence__copy(
  const global_traj_generate__msg__NavigationResult__Sequence * input,
  global_traj_generate__msg__NavigationResult__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(global_traj_generate__msg__NavigationResult);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    global_traj_generate__msg__NavigationResult * data =
      (global_traj_generate__msg__NavigationResult *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!global_traj_generate__msg__NavigationResult__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          global_traj_generate__msg__NavigationResult__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!global_traj_generate__msg__NavigationResult__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
