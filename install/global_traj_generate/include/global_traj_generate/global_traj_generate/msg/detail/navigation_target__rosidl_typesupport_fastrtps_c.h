// generated from rosidl_typesupport_fastrtps_c/resource/idl__rosidl_typesupport_fastrtps_c.h.em
// with input from global_traj_generate:msg/NavigationTarget.idl
// generated code does not contain a copyright notice
#ifndef GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
#define GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_


#include <stddef.h>
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "global_traj_generate/msg/rosidl_typesupport_fastrtps_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_global_traj_generate
size_t get_serialized_size_global_traj_generate__msg__NavigationTarget(
  const void * untyped_ros_message,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_global_traj_generate
size_t max_serialized_size_global_traj_generate__msg__NavigationTarget(
  bool & full_bounded,
  bool & is_plain,
  size_t current_alignment);

ROSIDL_TYPESUPPORT_FASTRTPS_C_PUBLIC_global_traj_generate
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_c, global_traj_generate, msg, NavigationTarget)();

#ifdef __cplusplus
}
#endif

#endif  // GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__ROSIDL_TYPESUPPORT_FASTRTPS_C_H_
