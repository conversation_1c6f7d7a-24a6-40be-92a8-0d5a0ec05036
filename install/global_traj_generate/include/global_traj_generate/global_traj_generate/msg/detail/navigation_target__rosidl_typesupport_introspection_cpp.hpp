// generated from rosidl_typesupport_introspection_cpp/resource/idl__rosidl_typesupport_introspection_cpp.h.em
// with input from global_traj_generate:msg/NavigationTarget.idl
// generated code does not contain a copyright notice

#ifndef GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_HPP_
#define GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_HPP_


#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_interface/macros.h"
#include "rosidl_typesupport_introspection_cpp/visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

// TODO(dirk-thomas) these visibility macros should be message package specific
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, global_traj_generate, msg, NavigationTarget)();

#ifdef __cplusplus
}
#endif

#endif  // GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_HPP_
