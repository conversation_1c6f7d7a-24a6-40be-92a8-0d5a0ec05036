// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from global_traj_generate:msg/NavigationTarget.idl
// generated code does not contain a copyright notice

#ifndef GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__TRAITS_HPP_
#define GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "global_traj_generate/msg/detail/navigation_target__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace global_traj_generate
{

namespace msg
{

inline void to_flow_style_yaml(
  const NavigationTarget & msg,
  std::ostream & out)
{
  out << "{";
  // member: nav_mode
  {
    out << "nav_mode: ";
    rosidl_generator_traits::value_to_yaml(msg.nav_mode, out);
    out << ", ";
  }

  // member: point_id
  {
    out << "point_id: ";
    rosidl_generator_traits::value_to_yaml(msg.point_id, out);
    out << ", ";
  }

  // member: pose_x
  {
    out << "pose_x: ";
    rosidl_generator_traits::value_to_yaml(msg.pose_x, out);
    out << ", ";
  }

  // member: pose_y
  {
    out << "pose_y: ";
    rosidl_generator_traits::value_to_yaml(msg.pose_y, out);
    out << ", ";
  }

  // member: pose_z
  {
    out << "pose_z: ";
    rosidl_generator_traits::value_to_yaml(msg.pose_z, out);
    out << ", ";
  }

  // member: yaw
  {
    out << "yaw: ";
    rosidl_generator_traits::value_to_yaml(msg.yaw, out);
    out << ", ";
  }

  // member: point_info
  {
    out << "point_info: ";
    rosidl_generator_traits::value_to_yaml(msg.point_info, out);
    out << ", ";
  }

  // member: gait
  {
    out << "gait: ";
    rosidl_generator_traits::value_to_yaml(msg.gait, out);
    out << ", ";
  }

  // member: speed
  {
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << ", ";
  }

  // member: manner
  {
    out << "manner: ";
    rosidl_generator_traits::value_to_yaml(msg.manner, out);
    out << ", ";
  }

  // member: obsmode
  {
    out << "obsmode: ";
    rosidl_generator_traits::value_to_yaml(msg.obsmode, out);
    out << ", ";
  }

  // member: navmode
  {
    out << "navmode: ";
    rosidl_generator_traits::value_to_yaml(msg.navmode, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const NavigationTarget & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: nav_mode
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "nav_mode: ";
    rosidl_generator_traits::value_to_yaml(msg.nav_mode, out);
    out << "\n";
  }

  // member: point_id
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "point_id: ";
    rosidl_generator_traits::value_to_yaml(msg.point_id, out);
    out << "\n";
  }

  // member: pose_x
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pose_x: ";
    rosidl_generator_traits::value_to_yaml(msg.pose_x, out);
    out << "\n";
  }

  // member: pose_y
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pose_y: ";
    rosidl_generator_traits::value_to_yaml(msg.pose_y, out);
    out << "\n";
  }

  // member: pose_z
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pose_z: ";
    rosidl_generator_traits::value_to_yaml(msg.pose_z, out);
    out << "\n";
  }

  // member: yaw
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "yaw: ";
    rosidl_generator_traits::value_to_yaml(msg.yaw, out);
    out << "\n";
  }

  // member: point_info
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "point_info: ";
    rosidl_generator_traits::value_to_yaml(msg.point_info, out);
    out << "\n";
  }

  // member: gait
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "gait: ";
    rosidl_generator_traits::value_to_yaml(msg.gait, out);
    out << "\n";
  }

  // member: speed
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "speed: ";
    rosidl_generator_traits::value_to_yaml(msg.speed, out);
    out << "\n";
  }

  // member: manner
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "manner: ";
    rosidl_generator_traits::value_to_yaml(msg.manner, out);
    out << "\n";
  }

  // member: obsmode
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "obsmode: ";
    rosidl_generator_traits::value_to_yaml(msg.obsmode, out);
    out << "\n";
  }

  // member: navmode
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "navmode: ";
    rosidl_generator_traits::value_to_yaml(msg.navmode, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const NavigationTarget & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace global_traj_generate

namespace rosidl_generator_traits
{

[[deprecated("use global_traj_generate::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const global_traj_generate::msg::NavigationTarget & msg,
  std::ostream & out, size_t indentation = 0)
{
  global_traj_generate::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use global_traj_generate::msg::to_yaml() instead")]]
inline std::string to_yaml(const global_traj_generate::msg::NavigationTarget & msg)
{
  return global_traj_generate::msg::to_yaml(msg);
}

template<>
inline const char * data_type<global_traj_generate::msg::NavigationTarget>()
{
  return "global_traj_generate::msg::NavigationTarget";
}

template<>
inline const char * name<global_traj_generate::msg::NavigationTarget>()
{
  return "global_traj_generate/msg/NavigationTarget";
}

template<>
struct has_fixed_size<global_traj_generate::msg::NavigationTarget>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<global_traj_generate::msg::NavigationTarget>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<global_traj_generate::msg::NavigationTarget>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__TRAITS_HPP_
