// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from global_traj_generate:msg/NavigationTarget.idl
// generated code does not contain a copyright notice

#ifndef GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__BUILDER_HPP_
#define GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "global_traj_generate/msg/detail/navigation_target__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace global_traj_generate
{

namespace msg
{

namespace builder
{

class Init_NavigationTarget_navmode
{
public:
  explicit Init_NavigationTarget_navmode(::global_traj_generate::msg::NavigationTarget & msg)
  : msg_(msg)
  {}
  ::global_traj_generate::msg::NavigationTarget navmode(::global_traj_generate::msg::NavigationTarget::_navmode_type arg)
  {
    msg_.navmode = std::move(arg);
    return std::move(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationTarget msg_;
};

class Init_NavigationTarget_obsmode
{
public:
  explicit Init_NavigationTarget_obsmode(::global_traj_generate::msg::NavigationTarget & msg)
  : msg_(msg)
  {}
  Init_NavigationTarget_navmode obsmode(::global_traj_generate::msg::NavigationTarget::_obsmode_type arg)
  {
    msg_.obsmode = std::move(arg);
    return Init_NavigationTarget_navmode(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationTarget msg_;
};

class Init_NavigationTarget_manner
{
public:
  explicit Init_NavigationTarget_manner(::global_traj_generate::msg::NavigationTarget & msg)
  : msg_(msg)
  {}
  Init_NavigationTarget_obsmode manner(::global_traj_generate::msg::NavigationTarget::_manner_type arg)
  {
    msg_.manner = std::move(arg);
    return Init_NavigationTarget_obsmode(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationTarget msg_;
};

class Init_NavigationTarget_speed
{
public:
  explicit Init_NavigationTarget_speed(::global_traj_generate::msg::NavigationTarget & msg)
  : msg_(msg)
  {}
  Init_NavigationTarget_manner speed(::global_traj_generate::msg::NavigationTarget::_speed_type arg)
  {
    msg_.speed = std::move(arg);
    return Init_NavigationTarget_manner(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationTarget msg_;
};

class Init_NavigationTarget_gait
{
public:
  explicit Init_NavigationTarget_gait(::global_traj_generate::msg::NavigationTarget & msg)
  : msg_(msg)
  {}
  Init_NavigationTarget_speed gait(::global_traj_generate::msg::NavigationTarget::_gait_type arg)
  {
    msg_.gait = std::move(arg);
    return Init_NavigationTarget_speed(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationTarget msg_;
};

class Init_NavigationTarget_point_info
{
public:
  explicit Init_NavigationTarget_point_info(::global_traj_generate::msg::NavigationTarget & msg)
  : msg_(msg)
  {}
  Init_NavigationTarget_gait point_info(::global_traj_generate::msg::NavigationTarget::_point_info_type arg)
  {
    msg_.point_info = std::move(arg);
    return Init_NavigationTarget_gait(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationTarget msg_;
};

class Init_NavigationTarget_yaw
{
public:
  explicit Init_NavigationTarget_yaw(::global_traj_generate::msg::NavigationTarget & msg)
  : msg_(msg)
  {}
  Init_NavigationTarget_point_info yaw(::global_traj_generate::msg::NavigationTarget::_yaw_type arg)
  {
    msg_.yaw = std::move(arg);
    return Init_NavigationTarget_point_info(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationTarget msg_;
};

class Init_NavigationTarget_pose_z
{
public:
  explicit Init_NavigationTarget_pose_z(::global_traj_generate::msg::NavigationTarget & msg)
  : msg_(msg)
  {}
  Init_NavigationTarget_yaw pose_z(::global_traj_generate::msg::NavigationTarget::_pose_z_type arg)
  {
    msg_.pose_z = std::move(arg);
    return Init_NavigationTarget_yaw(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationTarget msg_;
};

class Init_NavigationTarget_pose_y
{
public:
  explicit Init_NavigationTarget_pose_y(::global_traj_generate::msg::NavigationTarget & msg)
  : msg_(msg)
  {}
  Init_NavigationTarget_pose_z pose_y(::global_traj_generate::msg::NavigationTarget::_pose_y_type arg)
  {
    msg_.pose_y = std::move(arg);
    return Init_NavigationTarget_pose_z(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationTarget msg_;
};

class Init_NavigationTarget_pose_x
{
public:
  explicit Init_NavigationTarget_pose_x(::global_traj_generate::msg::NavigationTarget & msg)
  : msg_(msg)
  {}
  Init_NavigationTarget_pose_y pose_x(::global_traj_generate::msg::NavigationTarget::_pose_x_type arg)
  {
    msg_.pose_x = std::move(arg);
    return Init_NavigationTarget_pose_y(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationTarget msg_;
};

class Init_NavigationTarget_point_id
{
public:
  explicit Init_NavigationTarget_point_id(::global_traj_generate::msg::NavigationTarget & msg)
  : msg_(msg)
  {}
  Init_NavigationTarget_pose_x point_id(::global_traj_generate::msg::NavigationTarget::_point_id_type arg)
  {
    msg_.point_id = std::move(arg);
    return Init_NavigationTarget_pose_x(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationTarget msg_;
};

class Init_NavigationTarget_nav_mode
{
public:
  Init_NavigationTarget_nav_mode()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_NavigationTarget_point_id nav_mode(::global_traj_generate::msg::NavigationTarget::_nav_mode_type arg)
  {
    msg_.nav_mode = std::move(arg);
    return Init_NavigationTarget_point_id(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationTarget msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::global_traj_generate::msg::NavigationTarget>()
{
  return global_traj_generate::msg::builder::Init_NavigationTarget_nav_mode();
}

}  // namespace global_traj_generate

#endif  // GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__BUILDER_HPP_
