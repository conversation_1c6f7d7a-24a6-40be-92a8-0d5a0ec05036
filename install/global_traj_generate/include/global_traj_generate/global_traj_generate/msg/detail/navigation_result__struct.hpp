// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from global_traj_generate:msg/NavigationResult.idl
// generated code does not contain a copyright notice

#ifndef GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__STRUCT_HPP_
#define GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__global_traj_generate__msg__NavigationResult __attribute__((deprecated))
#else
# define DEPRECATED__global_traj_generate__msg__NavigationResult __declspec(deprecated)
#endif

namespace global_traj_generate
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct NavigationResult_
{
  using Type = NavigationResult_<ContainerAllocator>;

  explicit NavigationResult_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->point_id = 0l;
      this->target_pose_x = 0.0;
      this->target_pose_y = 0.0;
      this->target_pose_z = 0.0;
      this->target_yaw = 0.0;
      this->current_pose_x = 0.0;
      this->current_pose_y = 0.0;
      this->current_pose_z = 0.0;
      this->current_yaw = 0.0;
      this->nav_state = 0l;
    }
  }

  explicit NavigationResult_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->point_id = 0l;
      this->target_pose_x = 0.0;
      this->target_pose_y = 0.0;
      this->target_pose_z = 0.0;
      this->target_yaw = 0.0;
      this->current_pose_x = 0.0;
      this->current_pose_y = 0.0;
      this->current_pose_z = 0.0;
      this->current_yaw = 0.0;
      this->nav_state = 0l;
    }
  }

  // field types and members
  using _point_id_type =
    int32_t;
  _point_id_type point_id;
  using _target_pose_x_type =
    double;
  _target_pose_x_type target_pose_x;
  using _target_pose_y_type =
    double;
  _target_pose_y_type target_pose_y;
  using _target_pose_z_type =
    double;
  _target_pose_z_type target_pose_z;
  using _target_yaw_type =
    double;
  _target_yaw_type target_yaw;
  using _current_pose_x_type =
    double;
  _current_pose_x_type current_pose_x;
  using _current_pose_y_type =
    double;
  _current_pose_y_type current_pose_y;
  using _current_pose_z_type =
    double;
  _current_pose_z_type current_pose_z;
  using _current_yaw_type =
    double;
  _current_yaw_type current_yaw;
  using _nav_state_type =
    int32_t;
  _nav_state_type nav_state;

  // setters for named parameter idiom
  Type & set__point_id(
    const int32_t & _arg)
  {
    this->point_id = _arg;
    return *this;
  }
  Type & set__target_pose_x(
    const double & _arg)
  {
    this->target_pose_x = _arg;
    return *this;
  }
  Type & set__target_pose_y(
    const double & _arg)
  {
    this->target_pose_y = _arg;
    return *this;
  }
  Type & set__target_pose_z(
    const double & _arg)
  {
    this->target_pose_z = _arg;
    return *this;
  }
  Type & set__target_yaw(
    const double & _arg)
  {
    this->target_yaw = _arg;
    return *this;
  }
  Type & set__current_pose_x(
    const double & _arg)
  {
    this->current_pose_x = _arg;
    return *this;
  }
  Type & set__current_pose_y(
    const double & _arg)
  {
    this->current_pose_y = _arg;
    return *this;
  }
  Type & set__current_pose_z(
    const double & _arg)
  {
    this->current_pose_z = _arg;
    return *this;
  }
  Type & set__current_yaw(
    const double & _arg)
  {
    this->current_yaw = _arg;
    return *this;
  }
  Type & set__nav_state(
    const int32_t & _arg)
  {
    this->nav_state = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    global_traj_generate::msg::NavigationResult_<ContainerAllocator> *;
  using ConstRawPtr =
    const global_traj_generate::msg::NavigationResult_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<global_traj_generate::msg::NavigationResult_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<global_traj_generate::msg::NavigationResult_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      global_traj_generate::msg::NavigationResult_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<global_traj_generate::msg::NavigationResult_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      global_traj_generate::msg::NavigationResult_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<global_traj_generate::msg::NavigationResult_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<global_traj_generate::msg::NavigationResult_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<global_traj_generate::msg::NavigationResult_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__global_traj_generate__msg__NavigationResult
    std::shared_ptr<global_traj_generate::msg::NavigationResult_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__global_traj_generate__msg__NavigationResult
    std::shared_ptr<global_traj_generate::msg::NavigationResult_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const NavigationResult_ & other) const
  {
    if (this->point_id != other.point_id) {
      return false;
    }
    if (this->target_pose_x != other.target_pose_x) {
      return false;
    }
    if (this->target_pose_y != other.target_pose_y) {
      return false;
    }
    if (this->target_pose_z != other.target_pose_z) {
      return false;
    }
    if (this->target_yaw != other.target_yaw) {
      return false;
    }
    if (this->current_pose_x != other.current_pose_x) {
      return false;
    }
    if (this->current_pose_y != other.current_pose_y) {
      return false;
    }
    if (this->current_pose_z != other.current_pose_z) {
      return false;
    }
    if (this->current_yaw != other.current_yaw) {
      return false;
    }
    if (this->nav_state != other.nav_state) {
      return false;
    }
    return true;
  }
  bool operator!=(const NavigationResult_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct NavigationResult_

// alias to use template instance with default allocator
using NavigationResult =
  global_traj_generate::msg::NavigationResult_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace global_traj_generate

#endif  // GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__STRUCT_HPP_
