// generated from rosidl_generator_cpp/resource/idl__type_support.hpp.em
// with input from global_traj_generate:msg/NavigationResult.idl
// generated code does not contain a copyright notice

#ifndef GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__TYPE_SUPPORT_HPP_
#define GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__TYPE_SUPPORT_HPP_

#include "rosidl_typesupport_interface/macros.h"

#include "global_traj_generate/msg/rosidl_generator_cpp__visibility_control.hpp"

#include "rosidl_typesupport_cpp/message_type_support.hpp"

#ifdef __cplusplus
extern "C"
{
#endif
// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_CPP_PUBLIC_global_traj_generate
const rosidl_message_type_support_t *
  ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_cpp,
  global_traj_generate,
  msg,
  NavigationResult
)();
#ifdef __cplusplus
}
#endif

#endif  // GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__TYPE_SUPPORT_HPP_
