// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from global_traj_generate:msg/NavigationTarget.idl
// generated code does not contain a copyright notice

#ifndef GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__STRUCT_HPP_
#define GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__global_traj_generate__msg__NavigationTarget __attribute__((deprecated))
#else
# define DEPRECATED__global_traj_generate__msg__NavigationTarget __declspec(deprecated)
#endif

namespace global_traj_generate
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct NavigationTarget_
{
  using Type = NavigationTarget_<ContainerAllocator>;

  explicit NavigationTarget_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->nav_mode = 0l;
      this->point_id = 0l;
      this->pose_x = 0.0;
      this->pose_y = 0.0;
      this->pose_z = 0.0;
      this->yaw = 0.0;
      this->point_info = 0l;
      this->gait = 0l;
      this->speed = 0l;
      this->manner = 0l;
      this->obsmode = 0l;
      this->navmode = 0l;
    }
  }

  explicit NavigationTarget_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->nav_mode = 0l;
      this->point_id = 0l;
      this->pose_x = 0.0;
      this->pose_y = 0.0;
      this->pose_z = 0.0;
      this->yaw = 0.0;
      this->point_info = 0l;
      this->gait = 0l;
      this->speed = 0l;
      this->manner = 0l;
      this->obsmode = 0l;
      this->navmode = 0l;
    }
  }

  // field types and members
  using _nav_mode_type =
    int32_t;
  _nav_mode_type nav_mode;
  using _point_id_type =
    int32_t;
  _point_id_type point_id;
  using _pose_x_type =
    double;
  _pose_x_type pose_x;
  using _pose_y_type =
    double;
  _pose_y_type pose_y;
  using _pose_z_type =
    double;
  _pose_z_type pose_z;
  using _yaw_type =
    double;
  _yaw_type yaw;
  using _point_info_type =
    int32_t;
  _point_info_type point_info;
  using _gait_type =
    int32_t;
  _gait_type gait;
  using _speed_type =
    int32_t;
  _speed_type speed;
  using _manner_type =
    int32_t;
  _manner_type manner;
  using _obsmode_type =
    int32_t;
  _obsmode_type obsmode;
  using _navmode_type =
    int32_t;
  _navmode_type navmode;

  // setters for named parameter idiom
  Type & set__nav_mode(
    const int32_t & _arg)
  {
    this->nav_mode = _arg;
    return *this;
  }
  Type & set__point_id(
    const int32_t & _arg)
  {
    this->point_id = _arg;
    return *this;
  }
  Type & set__pose_x(
    const double & _arg)
  {
    this->pose_x = _arg;
    return *this;
  }
  Type & set__pose_y(
    const double & _arg)
  {
    this->pose_y = _arg;
    return *this;
  }
  Type & set__pose_z(
    const double & _arg)
  {
    this->pose_z = _arg;
    return *this;
  }
  Type & set__yaw(
    const double & _arg)
  {
    this->yaw = _arg;
    return *this;
  }
  Type & set__point_info(
    const int32_t & _arg)
  {
    this->point_info = _arg;
    return *this;
  }
  Type & set__gait(
    const int32_t & _arg)
  {
    this->gait = _arg;
    return *this;
  }
  Type & set__speed(
    const int32_t & _arg)
  {
    this->speed = _arg;
    return *this;
  }
  Type & set__manner(
    const int32_t & _arg)
  {
    this->manner = _arg;
    return *this;
  }
  Type & set__obsmode(
    const int32_t & _arg)
  {
    this->obsmode = _arg;
    return *this;
  }
  Type & set__navmode(
    const int32_t & _arg)
  {
    this->navmode = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    global_traj_generate::msg::NavigationTarget_<ContainerAllocator> *;
  using ConstRawPtr =
    const global_traj_generate::msg::NavigationTarget_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<global_traj_generate::msg::NavigationTarget_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<global_traj_generate::msg::NavigationTarget_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      global_traj_generate::msg::NavigationTarget_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<global_traj_generate::msg::NavigationTarget_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      global_traj_generate::msg::NavigationTarget_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<global_traj_generate::msg::NavigationTarget_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<global_traj_generate::msg::NavigationTarget_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<global_traj_generate::msg::NavigationTarget_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__global_traj_generate__msg__NavigationTarget
    std::shared_ptr<global_traj_generate::msg::NavigationTarget_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__global_traj_generate__msg__NavigationTarget
    std::shared_ptr<global_traj_generate::msg::NavigationTarget_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const NavigationTarget_ & other) const
  {
    if (this->nav_mode != other.nav_mode) {
      return false;
    }
    if (this->point_id != other.point_id) {
      return false;
    }
    if (this->pose_x != other.pose_x) {
      return false;
    }
    if (this->pose_y != other.pose_y) {
      return false;
    }
    if (this->pose_z != other.pose_z) {
      return false;
    }
    if (this->yaw != other.yaw) {
      return false;
    }
    if (this->point_info != other.point_info) {
      return false;
    }
    if (this->gait != other.gait) {
      return false;
    }
    if (this->speed != other.speed) {
      return false;
    }
    if (this->manner != other.manner) {
      return false;
    }
    if (this->obsmode != other.obsmode) {
      return false;
    }
    if (this->navmode != other.navmode) {
      return false;
    }
    return true;
  }
  bool operator!=(const NavigationTarget_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct NavigationTarget_

// alias to use template instance with default allocator
using NavigationTarget =
  global_traj_generate::msg::NavigationTarget_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace global_traj_generate

#endif  // GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__STRUCT_HPP_
