// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from global_traj_generate:msg/NavigationTarget.idl
// generated code does not contain a copyright notice
#include "global_traj_generate/msg/detail/navigation_target__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
global_traj_generate__msg__NavigationTarget__init(global_traj_generate__msg__NavigationTarget * msg)
{
  if (!msg) {
    return false;
  }
  // nav_mode
  // point_id
  // pose_x
  // pose_y
  // pose_z
  // yaw
  // point_info
  // gait
  // speed
  // manner
  // obsmode
  // navmode
  return true;
}

void
global_traj_generate__msg__NavigationTarget__fini(global_traj_generate__msg__NavigationTarget * msg)
{
  if (!msg) {
    return;
  }
  // nav_mode
  // point_id
  // pose_x
  // pose_y
  // pose_z
  // yaw
  // point_info
  // gait
  // speed
  // manner
  // obsmode
  // navmode
}

bool
global_traj_generate__msg__NavigationTarget__are_equal(const global_traj_generate__msg__NavigationTarget * lhs, const global_traj_generate__msg__NavigationTarget * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // nav_mode
  if (lhs->nav_mode != rhs->nav_mode) {
    return false;
  }
  // point_id
  if (lhs->point_id != rhs->point_id) {
    return false;
  }
  // pose_x
  if (lhs->pose_x != rhs->pose_x) {
    return false;
  }
  // pose_y
  if (lhs->pose_y != rhs->pose_y) {
    return false;
  }
  // pose_z
  if (lhs->pose_z != rhs->pose_z) {
    return false;
  }
  // yaw
  if (lhs->yaw != rhs->yaw) {
    return false;
  }
  // point_info
  if (lhs->point_info != rhs->point_info) {
    return false;
  }
  // gait
  if (lhs->gait != rhs->gait) {
    return false;
  }
  // speed
  if (lhs->speed != rhs->speed) {
    return false;
  }
  // manner
  if (lhs->manner != rhs->manner) {
    return false;
  }
  // obsmode
  if (lhs->obsmode != rhs->obsmode) {
    return false;
  }
  // navmode
  if (lhs->navmode != rhs->navmode) {
    return false;
  }
  return true;
}

bool
global_traj_generate__msg__NavigationTarget__copy(
  const global_traj_generate__msg__NavigationTarget * input,
  global_traj_generate__msg__NavigationTarget * output)
{
  if (!input || !output) {
    return false;
  }
  // nav_mode
  output->nav_mode = input->nav_mode;
  // point_id
  output->point_id = input->point_id;
  // pose_x
  output->pose_x = input->pose_x;
  // pose_y
  output->pose_y = input->pose_y;
  // pose_z
  output->pose_z = input->pose_z;
  // yaw
  output->yaw = input->yaw;
  // point_info
  output->point_info = input->point_info;
  // gait
  output->gait = input->gait;
  // speed
  output->speed = input->speed;
  // manner
  output->manner = input->manner;
  // obsmode
  output->obsmode = input->obsmode;
  // navmode
  output->navmode = input->navmode;
  return true;
}

global_traj_generate__msg__NavigationTarget *
global_traj_generate__msg__NavigationTarget__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  global_traj_generate__msg__NavigationTarget * msg = (global_traj_generate__msg__NavigationTarget *)allocator.allocate(sizeof(global_traj_generate__msg__NavigationTarget), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(global_traj_generate__msg__NavigationTarget));
  bool success = global_traj_generate__msg__NavigationTarget__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
global_traj_generate__msg__NavigationTarget__destroy(global_traj_generate__msg__NavigationTarget * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    global_traj_generate__msg__NavigationTarget__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
global_traj_generate__msg__NavigationTarget__Sequence__init(global_traj_generate__msg__NavigationTarget__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  global_traj_generate__msg__NavigationTarget * data = NULL;

  if (size) {
    data = (global_traj_generate__msg__NavigationTarget *)allocator.zero_allocate(size, sizeof(global_traj_generate__msg__NavigationTarget), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = global_traj_generate__msg__NavigationTarget__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        global_traj_generate__msg__NavigationTarget__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
global_traj_generate__msg__NavigationTarget__Sequence__fini(global_traj_generate__msg__NavigationTarget__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      global_traj_generate__msg__NavigationTarget__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

global_traj_generate__msg__NavigationTarget__Sequence *
global_traj_generate__msg__NavigationTarget__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  global_traj_generate__msg__NavigationTarget__Sequence * array = (global_traj_generate__msg__NavigationTarget__Sequence *)allocator.allocate(sizeof(global_traj_generate__msg__NavigationTarget__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = global_traj_generate__msg__NavigationTarget__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
global_traj_generate__msg__NavigationTarget__Sequence__destroy(global_traj_generate__msg__NavigationTarget__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    global_traj_generate__msg__NavigationTarget__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
global_traj_generate__msg__NavigationTarget__Sequence__are_equal(const global_traj_generate__msg__NavigationTarget__Sequence * lhs, const global_traj_generate__msg__NavigationTarget__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!global_traj_generate__msg__NavigationTarget__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
global_traj_generate__msg__NavigationTarget__Sequence__copy(
  const global_traj_generate__msg__NavigationTarget__Sequence * input,
  global_traj_generate__msg__NavigationTarget__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(global_traj_generate__msg__NavigationTarget);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    global_traj_generate__msg__NavigationTarget * data =
      (global_traj_generate__msg__NavigationTarget *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!global_traj_generate__msg__NavigationTarget__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          global_traj_generate__msg__NavigationTarget__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!global_traj_generate__msg__NavigationTarget__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
