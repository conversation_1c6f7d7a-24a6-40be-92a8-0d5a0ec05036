﻿// NOLINT: This file starts with a BOM since it contain non-ASCII characters
// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from global_traj_generate:msg/NavigationTarget.idl
// generated code does not contain a copyright notice

#ifndef GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__STRUCT_H_
#define GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in msg/NavigationTarget in the package global_traj_generate.
typedef struct global_traj_generate__msg__NavigationTarget
{
  /// 1:导航启动 0：导航停止
  int32_t nav_mode;
  /// 点位编号
  int32_t point_id;
  double pose_x;
  double pose_y;
  /// 预留
  double pose_z;
  double yaw;
  /// 任务点属性 0: 过渡点 1: 任务点 2: 充电准备点 3: 充电点
  int32_t point_info;
  /// 到达目标点使用的步态 0: 普通步态 1: 楼梯步态 2: 防滑步态 3: 匍匐步态
  int32_t gait;
  /// 速度设置 0: 普通速 1: 低速 2: 高速
  int32_t speed;
  /// 运动方式 0: 正走 1: 逆走
  int32_t manner;
  /// 障碍处理模式 0: 避障 1: 停障
  int32_t obsmode;
  /// 导航模式 0: 直线导航 1: 自主导航
  int32_t navmode;
} global_traj_generate__msg__NavigationTarget;

// Struct for a sequence of global_traj_generate__msg__NavigationTarget.
typedef struct global_traj_generate__msg__NavigationTarget__Sequence
{
  global_traj_generate__msg__NavigationTarget * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} global_traj_generate__msg__NavigationTarget__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_TARGET__STRUCT_H_
