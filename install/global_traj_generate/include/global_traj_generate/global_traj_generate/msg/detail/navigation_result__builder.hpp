// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from global_traj_generate:msg/NavigationResult.idl
// generated code does not contain a copyright notice

#ifndef GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__BUILDER_HPP_
#define GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "global_traj_generate/msg/detail/navigation_result__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace global_traj_generate
{

namespace msg
{

namespace builder
{

class Init_NavigationResult_nav_state
{
public:
  explicit Init_NavigationResult_nav_state(::global_traj_generate::msg::NavigationResult & msg)
  : msg_(msg)
  {}
  ::global_traj_generate::msg::NavigationResult nav_state(::global_traj_generate::msg::NavigationResult::_nav_state_type arg)
  {
    msg_.nav_state = std::move(arg);
    return std::move(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationResult msg_;
};

class Init_NavigationResult_current_yaw
{
public:
  explicit Init_NavigationResult_current_yaw(::global_traj_generate::msg::NavigationResult & msg)
  : msg_(msg)
  {}
  Init_NavigationResult_nav_state current_yaw(::global_traj_generate::msg::NavigationResult::_current_yaw_type arg)
  {
    msg_.current_yaw = std::move(arg);
    return Init_NavigationResult_nav_state(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationResult msg_;
};

class Init_NavigationResult_current_pose_z
{
public:
  explicit Init_NavigationResult_current_pose_z(::global_traj_generate::msg::NavigationResult & msg)
  : msg_(msg)
  {}
  Init_NavigationResult_current_yaw current_pose_z(::global_traj_generate::msg::NavigationResult::_current_pose_z_type arg)
  {
    msg_.current_pose_z = std::move(arg);
    return Init_NavigationResult_current_yaw(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationResult msg_;
};

class Init_NavigationResult_current_pose_y
{
public:
  explicit Init_NavigationResult_current_pose_y(::global_traj_generate::msg::NavigationResult & msg)
  : msg_(msg)
  {}
  Init_NavigationResult_current_pose_z current_pose_y(::global_traj_generate::msg::NavigationResult::_current_pose_y_type arg)
  {
    msg_.current_pose_y = std::move(arg);
    return Init_NavigationResult_current_pose_z(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationResult msg_;
};

class Init_NavigationResult_current_pose_x
{
public:
  explicit Init_NavigationResult_current_pose_x(::global_traj_generate::msg::NavigationResult & msg)
  : msg_(msg)
  {}
  Init_NavigationResult_current_pose_y current_pose_x(::global_traj_generate::msg::NavigationResult::_current_pose_x_type arg)
  {
    msg_.current_pose_x = std::move(arg);
    return Init_NavigationResult_current_pose_y(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationResult msg_;
};

class Init_NavigationResult_target_yaw
{
public:
  explicit Init_NavigationResult_target_yaw(::global_traj_generate::msg::NavigationResult & msg)
  : msg_(msg)
  {}
  Init_NavigationResult_current_pose_x target_yaw(::global_traj_generate::msg::NavigationResult::_target_yaw_type arg)
  {
    msg_.target_yaw = std::move(arg);
    return Init_NavigationResult_current_pose_x(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationResult msg_;
};

class Init_NavigationResult_target_pose_z
{
public:
  explicit Init_NavigationResult_target_pose_z(::global_traj_generate::msg::NavigationResult & msg)
  : msg_(msg)
  {}
  Init_NavigationResult_target_yaw target_pose_z(::global_traj_generate::msg::NavigationResult::_target_pose_z_type arg)
  {
    msg_.target_pose_z = std::move(arg);
    return Init_NavigationResult_target_yaw(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationResult msg_;
};

class Init_NavigationResult_target_pose_y
{
public:
  explicit Init_NavigationResult_target_pose_y(::global_traj_generate::msg::NavigationResult & msg)
  : msg_(msg)
  {}
  Init_NavigationResult_target_pose_z target_pose_y(::global_traj_generate::msg::NavigationResult::_target_pose_y_type arg)
  {
    msg_.target_pose_y = std::move(arg);
    return Init_NavigationResult_target_pose_z(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationResult msg_;
};

class Init_NavigationResult_target_pose_x
{
public:
  explicit Init_NavigationResult_target_pose_x(::global_traj_generate::msg::NavigationResult & msg)
  : msg_(msg)
  {}
  Init_NavigationResult_target_pose_y target_pose_x(::global_traj_generate::msg::NavigationResult::_target_pose_x_type arg)
  {
    msg_.target_pose_x = std::move(arg);
    return Init_NavigationResult_target_pose_y(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationResult msg_;
};

class Init_NavigationResult_point_id
{
public:
  Init_NavigationResult_point_id()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_NavigationResult_target_pose_x point_id(::global_traj_generate::msg::NavigationResult::_point_id_type arg)
  {
    msg_.point_id = std::move(arg);
    return Init_NavigationResult_target_pose_x(msg_);
  }

private:
  ::global_traj_generate::msg::NavigationResult msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::global_traj_generate::msg::NavigationResult>()
{
  return global_traj_generate::msg::builder::Init_NavigationResult_point_id();
}

}  // namespace global_traj_generate

#endif  // GLOBAL_TRAJ_GENERATE__MSG__DETAIL__NAVIGATION_RESULT__BUILDER_HPP_
