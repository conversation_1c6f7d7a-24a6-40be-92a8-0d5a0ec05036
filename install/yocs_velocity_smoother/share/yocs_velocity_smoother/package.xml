<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>yocs_velocity_smoother</name>
  <version>0.12.1</version>
  <description>
     Bound incoming velocity messages according to robot velocity and acceleration limits.
  </description>
  <author><PERSON></author>
  <maintainer email="<EMAIL>"><PERSON><PERSON><PERSON> <PERSON></maintainer>
  <license>BSD</license>
  <url type="website">http://ros.org/wiki/yocs_velocity_smoother</url>
  <url type="repository">https://github.com/yujinrobot/yujin_ocs</url>
  <url type="bugtracker">https://github.com/yujinrobot/yujin_ocs/issues</url>
  
  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>pluginlib</depend>
  <depend>rclcpp_components</depend>
  <depend>geometry_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>rcl_interfaces</depend>
  <depend>std_msgs</depend>
  
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  
  <export>
    <build_type>ament_cmake</build_type>
    <rclcpp_components plugin="plugins/velocity_smoother_component.xml"/>
  </export>
</package>
