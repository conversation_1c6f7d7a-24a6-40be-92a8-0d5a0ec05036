#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node


def generate_launch_description():
    # Get the package directory
    package_dir = get_package_share_directory('yocs_velocity_smoother')
    
    # Declare launch arguments
    node_name_arg = DeclareLaunchArgument(
        'node_name',
        default_value='velocity_smoother',
        description='Name of the velocity smoother node'
    )
    
    config_file_arg = DeclareLaunchArgument(
        'config_file',
        default_value=os.path.join(package_dir, 'param', 'standalone_ros2.yaml'),
        description='Path to the configuration file'
    )
    
    raw_cmd_vel_topic_arg = DeclareLaunchArgument(
        'raw_cmd_vel_topic',
        default_value='raw_cmd_vel',
        description='Topic name for raw velocity commands'
    )
    
    smooth_cmd_vel_topic_arg = DeclareLaunchArgument(
        'smooth_cmd_vel_topic',
        default_value='smooth_cmd_vel',
        description='Topic name for smoothed velocity commands'
    )
    
    robot_cmd_vel_topic_arg = DeclareLaunchArgument(
        'robot_cmd_vel_topic',
        default_value='robot_cmd_vel',
        description='Topic name for robot velocity feedback'
    )
    
    odom_topic_arg = DeclareLaunchArgument(
        'odom_topic',
        default_value='odom',
        description='Topic name for odometry feedback'
    )
    
    # Create the velocity smoother node
    velocity_smoother_node = Node(
        package='yocs_velocity_smoother',
        executable='velocity_smoother_node',
        name=LaunchConfiguration('node_name'),
        parameters=[LaunchConfiguration('config_file')],
        remappings=[
            ('raw_cmd_vel', LaunchConfiguration('raw_cmd_vel_topic')),
            ('smooth_cmd_vel', LaunchConfiguration('smooth_cmd_vel_topic')),
            ('robot_cmd_vel', LaunchConfiguration('robot_cmd_vel_topic')),
            ('odometry', LaunchConfiguration('odom_topic')),
        ],
        output='screen'
    )
    
    return LaunchDescription([
        node_name_arg,
        config_file_arg,
        raw_cmd_vel_topic_arg,
        smooth_cmd_vel_topic_arg,
        robot_cmd_vel_topic_arg,
        odom_topic_arg,
        velocity_smoother_node
    ])