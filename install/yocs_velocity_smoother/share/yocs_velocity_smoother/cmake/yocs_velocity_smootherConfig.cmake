# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_yocs_velocity_smoother_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED yocs_velocity_smoother_FOUND)
    # explicitly set it to FALSE, otherwise <PERSON><PERSON><PERSON> will set it to TRUE
    set(yocs_velocity_smoother_FOUND FALSE)
  elseif(NOT yocs_velocity_smoother_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(yocs_velocity_smoother_FOUND FALSE)
  endif()
  return()
endif()
set(_yocs_velocity_smoother_CONFIG_INCLUDED TRUE)

# output package information
if(NOT yocs_velocity_smoother_FIND_QUIETLY)
  message(STATUS "Found yocs_velocity_smoother: 0.12.1 (${yocs_velocity_smoother_DIR})")
endif()

# warn when using a deprecated package
if(NOT "" STREQUAL "")
  set(_msg "Package 'yocs_velocity_smoother' is deprecated")
  # append custom deprecation text if available
  if(NOT "" STREQUAL "TRUE")
    set(_msg "${_msg} ()")
  endif()
  # optionally quiet the deprecation message
  if(NOT ${yocs_velocity_smoother_DEPRECATED_QUIET})
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(yocs_velocity_smoother_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "ament_cmake_export_include_directories-extras.cmake;ament_cmake_export_libraries-extras.cmake;ament_cmake_export_dependencies-extras.cmake;ament_cmake_export_targets-extras.cmake")
foreach(_extra ${_extras})
  include("${yocs_velocity_smoother_DIR}/${_extra}")
endforeach()
