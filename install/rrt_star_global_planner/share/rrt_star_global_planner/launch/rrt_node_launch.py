#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, GroupAction
from launch.conditions import IfCondition
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    # Get the launch directory
    pkg_share = FindPackageShare(package='rrt_star_global_planner').find('rrt_star_global_planner')
    
    # Declare launch arguments
    rviz_arg = DeclareLaunchArgument(
        'rviz',
        default_value='false',
        description='Launch RViz'
    )
    
    map_file_arg = DeclareLaunchArgument(
        'map_file',
        default_value=PathJoinSubstitution([pkg_share, 'maps', 'map.yaml']),
        description='Path to map file'
    )
    
    params_file_arg = DeclareLaunchArgument(
        'params_file',
        default_value=PathJoinSubstitution([pkg_share, 'params', 'test_rrt_star_planner_ros2.yaml']),
        description='Path to parameters file'
    )

    # Map server node
    map_server_node = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[{'yaml_filename': LaunchConfiguration('map_file')}]
    )
    
    # Lifecycle manager for map server
    lifecycle_manager_node = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_localization',
        output='screen',
        parameters=[{'use_sim_time': False},
                   {'autostart': True},
                   {'node_names': ['map_server']}]
    )

    # Static transform publishers
    map_odom_transform_node = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='map_odom_broadcaster',
        arguments=['0', '0', '0', '0', '0', '0', 'map', 'odom']
    )
    
    map_base_transform_node = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='map_base_transform',
        arguments=['0', '0', '0', '0', '0', '0', 'map', 'base_footprint']
    )

    # RRT* global planner node
    rrt_planner_node = Node(
        package='rrt_star_global_planner',
        executable='rrt_star_planner',
        name='rrt_star_planner',
        output='screen',
        parameters=[LaunchConfiguration('params_file')]
    )

    # RViz node (conditional)
    rviz_node = Node(
        package='rviz2',
        executable='rviz2',
        name='rviznavi',
        arguments=['-d', PathJoinSubstitution([pkg_share, 'rviz', 'rrt_star_global_planner.rviz'])],
        condition=IfCondition(LaunchConfiguration('rviz'))
    )

    return LaunchDescription([
        rviz_arg,
        map_file_arg,
        params_file_arg,
        map_server_node,
        lifecycle_manager_node,
        map_odom_transform_node,
        map_base_transform_node,
        rrt_planner_node,
        rviz_node
    ])