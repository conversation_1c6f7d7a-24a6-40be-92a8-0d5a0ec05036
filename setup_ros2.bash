#!/bin/bash

# ROS2 Environment Setup Script for NR_Navigation
# Author: Claude Code
# Description: Source all necessary ROS2 environments

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}Setting up ROS2 environment for NR_Navigation...${NC}"

# Source ROS2 base environment
if [ -f "/opt/ros/humble/setup.bash" ]; then
    source /opt/ros/humble/setup.bash
    echo -e "${GREEN}✓ Sourced ROS2 Humble environment${NC}"
elif [ -f "/opt/ros/foxy/setup.bash" ]; then
    source /opt/ros/foxy/setup.bash
    echo -e "${GREEN}✓ Sourced ROS2 Foxy environment${NC}"
else
    echo -e "${YELLOW}⚠ No ROS2 environment found in /opt/ros/${NC}"
fi

# Source workspace if built
if [ -f "install/setup.bash" ]; then
    source install/setup.bash
    echo -e "${GREEN}✓ Sourced NR_Navigation workspace${NC}"
else
    echo -e "${YELLOW}⚠ NR_Navigation workspace not built yet${NC}"
    echo -e "${YELLOW}  Run './nr_navigation.bash build' to build the packages${NC}"
fi

# Set useful environment variables
export ROS_DOMAIN_ID=42
export RCUTILS_LOGGING_BUFFERED_STREAM=1
export RCUTILS_LOGGING_USE_STDOUT=1

echo -e "${GREEN}✓ Environment variables set:${NC}"
echo -e "  ROS_DOMAIN_ID=${ROS_DOMAIN_ID}"
echo -e "  RCUTILS_LOGGING_BUFFERED_STREAM=${RCUTILS_LOGGING_BUFFERED_STREAM}"

# Display available commands
echo -e "\n${GREEN}Available commands:${NC}"
echo -e "  ./nr_navigation.bash build    - Build all packages"
echo -e "  ./nr_navigation.bash indoor   - Launch indoor navigation"
echo -e "  ./nr_navigation.bash outdoor  - Launch outdoor navigation"
echo -e "  ./nr_navigation.bash help     - Show all available commands"

echo -e "\n${GREEN}ROS2 environment setup complete!${NC}"