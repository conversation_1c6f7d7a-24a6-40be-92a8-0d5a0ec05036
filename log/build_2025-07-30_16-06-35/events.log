[0.000000] (-) TimerEvent: {}
[0.000583] (global_traj_generate) JobQueued: {'identifier': 'global_traj_generate', 'dependencies': OrderedDict()}
[0.000629] (local_planner) JobQueued: {'identifier': 'local_planner', 'dependencies': OrderedDict()}
[0.000711] (rrt_star_global_planner) JobQueued: {'identifier': 'rrt_star_global_planner', 'dependencies': OrderedDict()}
[0.000739] (terrain_analysis) JobQueued: {'identifier': 'terrain_analysis', 'dependencies': OrderedDict()}
[0.000766] (yocs_velocity_smoother) JobQueued: {'identifier': 'yocs_velocity_smoother', 'dependencies': OrderedDict()}
[0.000792] (global_traj_generate) JobStarted: {'identifier': 'global_traj_generate'}
[0.004061] (local_planner) JobStarted: {'identifier': 'local_planner'}
[0.006225] (rrt_star_global_planner) JobStarted: {'identifier': 'rrt_star_global_planner'}
[0.008398] (terrain_analysis) JobStarted: {'identifier': 'terrain_analysis'}
[0.012008] (global_traj_generate) JobProgress: {'identifier': 'global_traj_generate', 'progress': 'cmake'}
[0.012320] (global_traj_generate) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/nr_navigation/src/global_traj_generate', '-DCMAKE_BUILD_TYPE=Release', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/global_traj_generate'], 'cwd': '/home/<USER>/nr_navigation/build/global_traj_generate', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/global_traj_generate'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.015034] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'cmake'}
[0.015402] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/nr_navigation/src/local_planner', '-DCMAKE_BUILD_TYPE=Release', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/local_planner'], 'cwd': '/home/<USER>/nr_navigation/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/local_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.017556] (rrt_star_global_planner) JobProgress: {'identifier': 'rrt_star_global_planner', 'progress': 'cmake'}
[0.017829] (rrt_star_global_planner) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/nr_navigation/src/rrt_star_global_planner', '-DCMAKE_BUILD_TYPE=Release', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/rrt_star_global_planner'], 'cwd': '/home/<USER>/nr_navigation/build/rrt_star_global_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/rrt_star_global_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.019380] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'cmake'}
[0.019798] (terrain_analysis) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/nr_navigation/src/terrain_analysis', '-DCMAKE_BUILD_TYPE=Release', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/terrain_analysis'], 'cwd': '/home/<USER>/nr_navigation/build/terrain_analysis', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/terrain_analysis'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.099666] (-) TimerEvent: {}
[0.138326] (global_traj_generate) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.138674] (terrain_analysis) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.138812] (local_planner) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.139364] (rrt_star_global_planner) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.199832] (-) TimerEvent: {}
[0.258198] (local_planner) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.258567] (rrt_star_global_planner) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.258703] (terrain_analysis) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.259662] (global_traj_generate) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.269455] (local_planner) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.269708] (terrain_analysis) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.269889] (global_traj_generate) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.269943] (rrt_star_global_planner) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.299938] (-) TimerEvent: {}
[0.377636] (terrain_analysis) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.381561] (global_traj_generate) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.384201] (terrain_analysis) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.385411] (terrain_analysis) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.385821] (terrain_analysis) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.386230] (local_planner) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.387459] (global_traj_generate) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.387654] (global_traj_generate) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.387934] (global_traj_generate) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.388451] (terrain_analysis) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.388880] (rrt_star_global_planner) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.390482] (global_traj_generate) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.394530] (local_planner) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.394866] (local_planner) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.395425] (local_planner) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.396821] (rrt_star_global_planner) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.396923] (rrt_star_global_planner) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.397179] (rrt_star_global_planner) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.399490] (local_planner) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.399990] (-) TimerEvent: {}
[0.400182] (rrt_star_global_planner) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.487710] (global_traj_generate) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.495043] (global_traj_generate) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.495257] (global_traj_generate) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.495567] (global_traj_generate) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.497229] (terrain_analysis) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.498126] (local_planner) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.498257] (rrt_star_global_planner) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.499525] (global_traj_generate) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.500086] (-) TimerEvent: {}
[0.502639] (terrain_analysis) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.502852] (terrain_analysis) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.503169] (terrain_analysis) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.503613] (rrt_star_global_planner) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.503833] (rrt_star_global_planner) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.503978] (local_planner) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.504175] (rrt_star_global_planner) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.504323] (local_planner) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.504481] (local_planner) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.504831] (terrain_analysis) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.505724] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.506062] (local_planner) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.600235] (-) TimerEvent: {}
[0.634280] (terrain_analysis) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.636588] (global_traj_generate) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.638388] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.638790] (local_planner) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.700289] (-) TimerEvent: {}
[0.712361] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.713236] (terrain_analysis) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.714944] (global_traj_generate) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.716434] (local_planner) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.746588] (terrain_analysis) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.746803] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.746882] (local_planner) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.746946] (global_traj_generate) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.750317] (local_planner) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.750516] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.750575] (terrain_analysis) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.750644] (global_traj_generate) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.757284] (global_traj_generate) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.757472] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.757525] (terrain_analysis) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.757563] (local_planner) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.767234] (terrain_analysis) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.767376] (global_traj_generate) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.767436] (local_planner) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.767485] (rrt_star_global_planner) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.779897] (terrain_analysis) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.780022] (global_traj_generate) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.780112] (local_planner) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.780346] (rrt_star_global_planner) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.800350] (-) TimerEvent: {}
[0.814293] (terrain_analysis) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.814466] (global_traj_generate) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.814555] (local_planner) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.815017] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.816355] (global_traj_generate) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.816478] (terrain_analysis) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.816526] (local_planner) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.816709] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.900464] (-) TimerEvent: {}
[0.946905] (local_planner) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[0.966834] (global_traj_generate) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[0.970659] (terrain_analysis) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[0.976974] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[0.989180] (local_planner) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[0.998304] (global_traj_generate) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[0.999989] (terrain_analysis) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[1.000523] (-) TimerEvent: {}
[1.005884] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[1.028059] (local_planner) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.035544] (terrain_analysis) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.036234] (global_traj_generate) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.038128] (local_planner) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[1.041776] (terrain_analysis) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[1.042334] (global_traj_generate) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[1.042796] (rrt_star_global_planner) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.052432] (rrt_star_global_planner) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[1.100653] (-) TimerEvent: {}
[1.138572] (global_traj_generate) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[1.138921] (global_traj_generate) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[1.143122] (local_planner) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[1.143866] (local_planner) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[1.144399] (terrain_analysis) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[1.145159] (terrain_analysis) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[1.157366] (rrt_star_global_planner) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[1.157756] (rrt_star_global_planner) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[1.200740] (-) TimerEvent: {}
[1.253511] (local_planner) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[1.254752] (local_planner) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[1.257548] (rrt_star_global_planner) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[1.258512] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[1.258756] (global_traj_generate) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[1.259096] (terrain_analysis) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[1.260966] (global_traj_generate) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[1.261612] (terrain_analysis) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[1.300869] (-) TimerEvent: {}
[1.306254] (global_traj_generate) StdoutLine: {'line': b'-- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)\n'}
[1.306503] (local_planner) StdoutLine: {'line': b'-- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)\n'}
[1.306610] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)\n'}
[1.306711] (terrain_analysis) StdoutLine: {'line': b'-- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)\n'}
[1.307437] (global_traj_generate) StdoutLine: {'line': b'-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[1.307646] (local_planner) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[1.307755] (terrain_analysis) StdoutLine: {'line': b'-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)\n'}
[1.318049] (global_traj_generate) StdoutLine: {'line': b'-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)\n'}
[1.323894] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)\n'}
[1.325099] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[1.331316] (global_traj_generate) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[1.331522] (terrain_analysis) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[1.331654] (local_planner) StdoutLine: {'line': b'-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)\n'}
[1.334639] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)\n'}
[1.345179] (global_traj_generate) StdoutLine: {'line': b'-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)\n'}
[1.345407] (local_planner) StdoutLine: {'line': b'-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)\n'}
[1.345509] (terrain_analysis) StdoutLine: {'line': b'-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)\n'}
[1.346426] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)\n'}
[1.354388] (local_planner) StdoutLine: {'line': b'-- Found tf2_ros: 0.25.15 (/opt/ros/humble/share/tf2_ros/cmake)\n'}
[1.354532] (global_traj_generate) StdoutLine: {'line': b'-- Found tf2_ros: 0.25.15 (/opt/ros/humble/share/tf2_ros/cmake)\n'}
[1.354627] (terrain_analysis) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)\n'}
[1.358797] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)\n'}
[1.366952] (terrain_analysis) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[1.368004] (terrain_analysis) StdoutLine: {'line': b'-- Found Eigen3: TRUE (found version "3.4.0") \n'}
[1.368132] (terrain_analysis) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[1.375514] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)\n'}
[1.383526] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found tf2_ros: 0.25.15 (/opt/ros/humble/share/tf2_ros/cmake)\n'}
[1.400987] (-) TimerEvent: {}
[1.432997] (local_planner) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)\n'}
[1.433526] (terrain_analysis) StdoutLine: {'line': b'-- Found tf2_eigen: 0.25.15 (/opt/ros/humble/share/tf2_eigen/cmake)\n'}
[1.433654] (global_traj_generate) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)\n'}
[1.436465] (terrain_analysis) StdoutLine: {'line': b'-- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)\n'}
[1.443238] (local_planner) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[1.444234] (local_planner) StdoutLine: {'line': b'-- Found Eigen3: TRUE (found version "3.4.0") \n'}
[1.444335] (local_planner) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[1.445850] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)\n'}
[1.446053] (local_planner) StdoutLine: {'line': b'-- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)\n'}
[1.449244] (global_traj_generate) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[1.450372] (global_traj_generate) StdoutLine: {'line': b'-- Found Eigen3: TRUE (found version "3.4.0") \n'}
[1.450457] (global_traj_generate) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[1.452609] (global_traj_generate) StdoutLine: {'line': b'-- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)\n'}
[1.456425] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[1.457406] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found Eigen3: TRUE (found version "3.4.0") \n'}
[1.457514] (rrt_star_global_planner) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[1.459674] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found nav2_core: 1.1.18 (/opt/ros/humble/share/nav2_core/cmake)\n'}
[1.469992] (terrain_analysis) StdoutLine: {'line': b"-- Checking for module 'eigen3'\n"}
[1.473497] (local_planner) StdoutLine: {'line': b"-- Checking for module 'eigen3'\n"}
[1.482637] (global_traj_generate) StdoutLine: {'line': b"-- Checking for module 'eigen3'\n"}
[1.491083] (terrain_analysis) StdoutLine: {'line': b'--   Found eigen3, version 3.4.0\n'}
[1.492771] (local_planner) StdoutLine: {'line': b'--   Found eigen3, version 3.4.0\n'}
[1.501074] (-) TimerEvent: {}
[1.506748] (global_traj_generate) StdoutLine: {'line': b'--   Found eigen3, version 3.4.0\n'}
[1.550960] (local_planner) StdoutLine: {'line': b'-- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") \n'}
[1.551149] (local_planner) StdoutLine: {'line': b'-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)\n'}
[1.556173] (terrain_analysis) StdoutLine: {'line': b'-- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") \n'}
[1.556317] (terrain_analysis) StdoutLine: {'line': b'-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)\n'}
[1.568878] (global_traj_generate) StdoutLine: {'line': b'-- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") \n'}
[1.569033] (global_traj_generate) StdoutLine: {'line': b'-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)\n'}
[1.585630] (local_planner) StdoutLine: {'line': b"-- Checking for module 'flann'\n"}
[1.589137] (terrain_analysis) StdoutLine: {'line': b"-- Checking for module 'flann'\n"}
[1.594112] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found nav2_common: 1.1.18 (/opt/ros/humble/share/nav2_common/cmake)\n'}
[1.597556] (global_traj_generate) StdoutLine: {'line': b"-- Checking for module 'flann'\n"}
[1.601149] (-) TimerEvent: {}
[1.608924] (local_planner) StdoutLine: {'line': b'--   Found flann, version 1.9.1\n'}
[1.615594] (terrain_analysis) StdoutLine: {'line': b'--   Found flann, version 1.9.1\n'}
[1.620034] (global_traj_generate) StdoutLine: {'line': b'--   Found flann, version 1.9.1\n'}
[1.683172] (terrain_analysis) StdoutLine: {'line': b'-- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  \n'}
[1.683341] (terrain_analysis) StdoutLine: {'line': b'-- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)\n'}
[1.683915] (local_planner) StdoutLine: {'line': b'-- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  \n'}
[1.683997] (local_planner) StdoutLine: {'line': b'-- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)\n'}
[1.686036] (global_traj_generate) StdoutLine: {'line': b'-- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  \n'}
[1.686084] (global_traj_generate) StdoutLine: {'line': b'-- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)\n'}
[1.701297] (-) TimerEvent: {}
[1.708248] (rrt_star_global_planner) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.778887] (rrt_star_global_planner) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[1.779850] (rrt_star_global_planner) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[1.780350] (rrt_star_global_planner) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /home/<USER>/nr_navigation/src/rrt_star_global_planner/include\n'}
[1.780444] (rrt_star_global_planner) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[1.781229] (rrt_star_global_planner) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[1.781267] (rrt_star_global_planner) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[1.781586] (rrt_star_global_planner) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[1.782148] (rrt_star_global_planner) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.782466] (rrt_star_global_planner) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[1.783258] (rrt_star_global_planner) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[1.783296] (rrt_star_global_planner) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[1.783694] (rrt_star_global_planner) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.785857] (rrt_star_global_planner) StdoutLine: {'line': b'-- Configuring done\n'}
[1.801388] (-) TimerEvent: {}
[1.864620] (rrt_star_global_planner) StdoutLine: {'line': b'-- Generating done\n'}
[1.873007] (rrt_star_global_planner) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/nr_navigation/build/rrt_star_global_planner\n'}
[1.884662] (rrt_star_global_planner) CommandEnded: {'returncode': 0}
[1.885499] (rrt_star_global_planner) JobProgress: {'identifier': 'rrt_star_global_planner', 'progress': 'build'}
[1.886432] (rrt_star_global_planner) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/nr_navigation/build/rrt_star_global_planner', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/nr_navigation/build/rrt_star_global_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/rrt_star_global_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[1.901484] (-) TimerEvent: {}
[1.962554] (rrt_star_global_planner) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/rrt_star_global_planner_lib.dir/src/rrt_star_ros.cpp.o\x1b[0m\n'}
[2.001605] (-) TimerEvent: {}
[2.101976] (-) TimerEvent: {}
[2.202349] (-) TimerEvent: {}
[2.302722] (-) TimerEvent: {}
[2.403089] (-) TimerEvent: {}
[2.503487] (-) TimerEvent: {}
[2.603855] (-) TimerEvent: {}
[2.704245] (-) TimerEvent: {}
[2.804551] (-) TimerEvent: {}
[2.886273] (terrain_analysis) StdoutLine: {'line': b"-- Checking for module 'libusb-1.0'\n"}
[2.899226] (local_planner) StdoutLine: {'line': b"-- Checking for module 'libusb-1.0'\n"}
[2.903694] (global_traj_generate) StdoutLine: {'line': b"-- Checking for module 'libusb-1.0'\n"}
[2.904627] (-) TimerEvent: {}
[2.914919] (terrain_analysis) StdoutLine: {'line': b'--   Found libusb-1.0, version 1.0.25\n'}
[2.921059] (local_planner) StdoutLine: {'line': b'--   Found libusb-1.0, version 1.0.25\n'}
[2.923779] (global_traj_generate) StdoutLine: {'line': b'--   Found libusb-1.0, version 1.0.25\n'}
[2.972761] (terrain_analysis) StdoutLine: {'line': b'-- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  \n'}
[2.974772] (terrain_analysis) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[2.992640] (local_planner) StdoutLine: {'line': b'-- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  \n'}
[2.993457] (local_planner) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[2.995480] (global_traj_generate) StdoutLine: {'line': b'-- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  \n'}
[2.996270] (global_traj_generate) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[3.004731] (-) TimerEvent: {}
[3.071812] (terrain_analysis) StdoutLine: {'line': b'-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)\n'}
[3.084218] (global_traj_generate) StdoutLine: {'line': b'-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)\n'}
[3.098711] (local_planner) StdoutLine: {'line': b'-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)\n'}
[3.104844] (-) TimerEvent: {}
[3.205196] (-) TimerEvent: {}
[3.216790] (terrain_analysis) StdoutLine: {'line': b'-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)\n'}
[3.222864] (global_traj_generate) StdoutLine: {'line': b'-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)\n'}
[3.224425] (terrain_analysis) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[3.230150] (global_traj_generate) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[3.231500] (terrain_analysis) StdoutLine: {'line': b'-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)\n'}
[3.237144] (global_traj_generate) StdoutLine: {'line': b'-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)\n'}
[3.246383] (local_planner) StdoutLine: {'line': b'-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)\n'}
[3.254324] (local_planner) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[3.262036] (local_planner) StdoutLine: {'line': b'-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)\n'}
[3.305260] (-) TimerEvent: {}
[3.378236] (global_traj_generate) StdoutLine: {'line': b'-- Found Qhull version 8.0.2\n'}
[3.378446] (terrain_analysis) StdoutLine: {'line': b'-- Found Qhull version 8.0.2\n'}
[3.393547] (local_planner) StdoutLine: {'line': b'-- Found Qhull version 8.0.2\n'}
[3.405360] (-) TimerEvent: {}
[3.505655] (-) TimerEvent: {}
[3.508806] (terrain_analysis) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[3.520657] (global_traj_generate) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[3.521854] (local_planner) StdoutLine: {'line': b'-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)\n'}
[3.605759] (-) TimerEvent: {}
[3.634857] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  \n'}
[3.635570] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  \n'}
[3.636262] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  \n'}
[3.636940] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  \n'}
[3.637633] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  \n'}
[3.638273] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  \n'}
[3.638577] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_2D: /usr/include/pcl-1.12  \n'}
[3.638849] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_GEOMETRY: /usr/include/pcl-1.12  \n'}
[3.639593] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  \n'}
[3.640517] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_FEATURES: /usr/lib/x86_64-linux-gnu/libpcl_features.so  \n'}
[3.641411] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_ML: /usr/lib/x86_64-linux-gnu/libpcl_ml.so  \n'}
[3.642052] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_SEGMENTATION: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so  \n'}
[3.642670] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  \n'}
[3.642835] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_VISUALIZATION: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so  \n'}
[3.643429] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  \n'}
[3.643577] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_SURFACE: /usr/lib/x86_64-linux-gnu/libpcl_surface.so  \n'}
[3.644081] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  \n'}
[3.644282] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_REGISTRATION: /usr/lib/x86_64-linux-gnu/libpcl_registration.so  \n'}
[3.644653] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  \n'}
[3.645081] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_KEYPOINTS: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so  \n'}
[3.645299] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  \n'}
[3.645801] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_TRACKING: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so  \n'}
[3.645931] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  \n'}
[3.646171] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_2D: /usr/include/pcl-1.12  \n'}
[3.646427] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_GEOMETRY: /usr/include/pcl-1.12  \n'}
[3.646624] (local_planner) StdoutLine: {'line': b'-- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  \n'}
[3.646736] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_RECOGNITION: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so  \n'}
[3.647021] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  \n'}
[3.647281] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_STEREO: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so  \n'}
[3.647707] (local_planner) StdoutLine: {'line': b'-- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  \n'}
[3.647843] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_FEATURES: /usr/lib/x86_64-linux-gnu/libpcl_features.so  \n'}
[3.648028] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_APPS: /usr/lib/x86_64-linux-gnu/libpcl_apps.so  \n'}
[3.648480] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_ML: /usr/lib/x86_64-linux-gnu/libpcl_ml.so  \n'}
[3.648671] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  \n'}
[3.648789] (local_planner) StdoutLine: {'line': b'-- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  \n'}
[3.649019] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_MODELER: /usr/include/pcl-1.12  \n'}
[3.649125] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_SEGMENTATION: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so  \n'}
[3.649896] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_VISUALIZATION: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so  \n'}
[3.650116] (local_planner) StdoutLine: {'line': b'-- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  \n'}
[3.650677] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_SURFACE: /usr/lib/x86_64-linux-gnu/libpcl_surface.so  \n'}
[3.651110] (local_planner) StdoutLine: {'line': b'-- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  \n'}
[3.651337] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_REGISTRATION: /usr/lib/x86_64-linux-gnu/libpcl_registration.so  \n'}
[3.651903] (local_planner) StdoutLine: {'line': b'-- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  \n'}
[3.652187] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_KEYPOINTS: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so  \n'}
[3.652445] (local_planner) StdoutLine: {'line': b'-- Found PCL_2D: /usr/include/pcl-1.12  \n'}
[3.652519] (local_planner) StdoutLine: {'line': b'-- Found PCL_GEOMETRY: /usr/include/pcl-1.12  \n'}
[3.652993] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_TRACKING: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so  \n'}
[3.653183] (local_planner) StdoutLine: {'line': b'-- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  \n'}
[3.653659] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_RECOGNITION: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so  \n'}
[3.653939] (local_planner) StdoutLine: {'line': b'-- Found PCL_FEATURES: /usr/lib/x86_64-linux-gnu/libpcl_features.so  \n'}
[3.654417] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_STEREO: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so  \n'}
[3.654661] (local_planner) StdoutLine: {'line': b'-- Found PCL_ML: /usr/lib/x86_64-linux-gnu/libpcl_ml.so  \n'}
[3.655113] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_APPS: /usr/lib/x86_64-linux-gnu/libpcl_apps.so  \n'}
[3.655316] (local_planner) StdoutLine: {'line': b'-- Found PCL_SEGMENTATION: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so  \n'}
[3.655713] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  \n'}
[3.655919] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_MODELER: /usr/include/pcl-1.12  \n'}
[3.656174] (local_planner) StdoutLine: {'line': b'-- Found PCL_VISUALIZATION: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so  \n'}
[3.657224] (local_planner) StdoutLine: {'line': b'-- Found PCL_SURFACE: /usr/lib/x86_64-linux-gnu/libpcl_surface.so  \n'}
[3.657932] (local_planner) StdoutLine: {'line': b'-- Found PCL_REGISTRATION: /usr/lib/x86_64-linux-gnu/libpcl_registration.so  \n'}
[3.658735] (local_planner) StdoutLine: {'line': b'-- Found PCL_KEYPOINTS: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so  \n'}
[3.659521] (local_planner) StdoutLine: {'line': b'-- Found PCL_TRACKING: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so  \n'}
[3.659717] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  \n'}
[3.659794] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  \n'}
[3.660273] (local_planner) StdoutLine: {'line': b'-- Found PCL_RECOGNITION: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so  \n'}
[3.660749] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_OUTOFCORE: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so  \n'}
[3.660901] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_OUTOFCORE: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so  \n'}
[3.661091] (local_planner) StdoutLine: {'line': b'-- Found PCL_STEREO: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so  \n'}
[3.661707] (global_traj_generate) StdoutLine: {'line': b'-- Found PCL_PEOPLE: /usr/lib/x86_64-linux-gnu/libpcl_people.so  \n'}
[3.661835] (terrain_analysis) StdoutLine: {'line': b'-- Found PCL_PEOPLE: /usr/lib/x86_64-linux-gnu/libpcl_people.so  \n'}
[3.661930] (local_planner) StdoutLine: {'line': b'-- Found PCL_APPS: /usr/lib/x86_64-linux-gnu/libpcl_apps.so  \n'}
[3.662387] (local_planner) StdoutLine: {'line': b'-- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  \n'}
[3.662621] (local_planner) StdoutLine: {'line': b'-- Found PCL_MODELER: /usr/include/pcl-1.12  \n'}
[3.662863] (local_planner) StdoutLine: {'line': b'-- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  \n'}
[3.663653] (local_planner) StdoutLine: {'line': b'-- Found PCL_OUTOFCORE: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so  \n'}
[3.664193] (terrain_analysis) StdoutLine: {'line': b'-- Found laser_geometry: 2.4.0 (/opt/ros/humble/share/laser_geometry/cmake)\n'}
[3.664266] (local_planner) StdoutLine: {'line': b'-- Found PCL_PEOPLE: /usr/lib/x86_64-linux-gnu/libpcl_people.so  \n'}
[3.664934] (global_traj_generate) StdoutLine: {'line': b'-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)\n'}
[3.665622] (local_planner) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at CMakeLists.txt:27 (find_package):\n'}
[3.665724] (local_planner) StderrLine: {'line': b'  Policy CMP0074 is not set: find_package uses <PackageName>_ROOT variables.\n'}
[3.665766] (local_planner) StderrLine: {'line': b'  Run "cmake --help-policy CMP0074" for policy details.  Use the cmake_policy\n'}
[3.665796] (local_planner) StderrLine: {'line': b'  command to set the policy and suppress this warning.\n'}
[3.665833] (local_planner) StderrLine: {'line': b'\n'}
[3.665865] (local_planner) StderrLine: {'line': b'  CMake variable PCL_ROOT is set to:\n'}
[3.665893] (local_planner) StderrLine: {'line': b'\n'}
[3.665919] (local_planner) StderrLine: {'line': b'    /usr\n'}
[3.665945] (local_planner) StderrLine: {'line': b'\n'}
[3.665970] (local_planner) StderrLine: {'line': b'  For compatibility, CMake is ignoring the variable.\n'}
[3.665996] (local_planner) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[3.666027] (local_planner) StderrLine: {'line': b'\x1b[0m\n'}
[3.668273] (terrain_analysis) StdoutLine: {'line': b'-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)\n'}
[3.672804] (global_traj_generate) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at CMakeLists.txt:27 (find_package):\n'}
[3.672910] (global_traj_generate) StderrLine: {'line': b'  Policy CMP0074 is not set: find_package uses <PackageName>_ROOT variables.\n'}
[3.672957] (global_traj_generate) StderrLine: {'line': b'  Run "cmake --help-policy CMP0074" for policy details.  Use the cmake_policy\n'}
[3.673010] (global_traj_generate) StderrLine: {'line': b'  command to set the policy and suppress this warning.\n'}
[3.673059] (global_traj_generate) StderrLine: {'line': b'\n'}
[3.673113] (global_traj_generate) StderrLine: {'line': b'  CMake variable PCL_ROOT is set to:\n'}
[3.673151] (global_traj_generate) StderrLine: {'line': b'\n'}
[3.673193] (global_traj_generate) StderrLine: {'line': b'    /usr\n'}
[3.673240] (global_traj_generate) StderrLine: {'line': b'\n'}
[3.673277] (global_traj_generate) StderrLine: {'line': b'  For compatibility, CMake is ignoring the variable.\n'}
[3.673315] (global_traj_generate) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[3.673353] (global_traj_generate) StderrLine: {'line': b'\x1b[0m\n'}
[3.683909] (terrain_analysis) StdoutLine: {'line': b'-- Found OpenCV: /usr (found version "4.5.4") \n'}
[3.684177] (terrain_analysis) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at CMakeLists.txt:28 (find_package):\n'}
[3.684265] (terrain_analysis) StderrLine: {'line': b'  Policy CMP0074 is not set: find_package uses <PackageName>_ROOT variables.\n'}
[3.684318] (terrain_analysis) StderrLine: {'line': b'  Run "cmake --help-policy CMP0074" for policy details.  Use the cmake_policy\n'}
[3.684365] (terrain_analysis) StderrLine: {'line': b'  command to set the policy and suppress this warning.\n'}
[3.684411] (terrain_analysis) StderrLine: {'line': b'\n'}
[3.684456] (terrain_analysis) StderrLine: {'line': b'  CMake variable PCL_ROOT is set to:\n'}
[3.684501] (terrain_analysis) StderrLine: {'line': b'\n'}
[3.684547] (terrain_analysis) StderrLine: {'line': b'    /usr\n'}
[3.684592] (terrain_analysis) StderrLine: {'line': b'\n'}
[3.684638] (terrain_analysis) StderrLine: {'line': b'  For compatibility, CMake is ignoring the variable.\n'}
[3.684683] (terrain_analysis) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[3.684729] (terrain_analysis) StderrLine: {'line': b'\x1b[0m\n'}
[3.696450] (local_planner) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[3.700769] (terrain_analysis) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[3.705819] (-) TimerEvent: {}
[3.754664] (local_planner) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[3.755723] (local_planner) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[3.756381] (local_planner) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /home/<USER>/nr_navigation/src/local_planner/include\n'}
[3.756429] (local_planner) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[3.757561] (local_planner) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[3.757613] (local_planner) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[3.757999] (local_planner) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[3.758466] (local_planner) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[3.758842] (local_planner) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[3.759784] (local_planner) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[3.760013] (local_planner) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[3.760164] (local_planner) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[3.761651] (local_planner) StdoutLine: {'line': b'-- Configuring done\n'}
[3.766592] (terrain_analysis) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[3.767365] (terrain_analysis) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[3.767623] (terrain_analysis) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /home/<USER>/nr_navigation/src/terrain_analysis/include\n'}
[3.767717] (terrain_analysis) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[3.768249] (terrain_analysis) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[3.768346] (terrain_analysis) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[3.768564] (terrain_analysis) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[3.768901] (terrain_analysis) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[3.769158] (terrain_analysis) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[3.769738] (terrain_analysis) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[3.769847] (terrain_analysis) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[3.770035] (terrain_analysis) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[3.770884] (terrain_analysis) StdoutLine: {'line': b'-- Configuring done\n'}
[3.800126] (terrain_analysis) StdoutLine: {'line': b'-- Generating done\n'}
[3.805931] (-) TimerEvent: {}
[3.807381] (terrain_analysis) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/nr_navigation/build/terrain_analysis\n'}
[3.826276] (terrain_analysis) CommandEnded: {'returncode': 0}
[3.827230] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'build'}
[3.827798] (terrain_analysis) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/nr_navigation/build/terrain_analysis', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/nr_navigation/build/terrain_analysis', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/terrain_analysis'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[3.834944] (local_planner) StdoutLine: {'line': b'-- Generating done\n'}
[3.854337] (local_planner) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/nr_navigation/build/local_planner\n'}
[3.869450] (local_planner) CommandEnded: {'returncode': 0}
[3.870991] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'build'}
[3.871171] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/nr_navigation/build/local_planner', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/nr_navigation/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/local_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[3.897413] (terrain_analysis) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/terrain_analysis.dir/src/terrain_analysis_ros2.cpp.o\x1b[0m\n'}
[3.906026] (-) TimerEvent: {}
[3.929650] (global_traj_generate) StdoutLine: {'line': b'-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)\n'}
[3.953827] (local_planner) StdoutLine: {'line': b'[ 12%] \x1b[32mBuilding CXX object CMakeFiles/pathFollower.dir/src/pathFollower.cpp.o\x1b[0m\n'}
[3.957009] (local_planner) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/calibration.dir/src/calibration.cpp.o\x1b[0m\n'}
[3.964244] (local_planner) StdoutLine: {'line': b'[ 37%] \x1b[32mBuilding CXX object CMakeFiles/localPlanner.dir/src/localPlanner.cpp.o\x1b[0m\n'}
[3.964474] (local_planner) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/pointPublish.dir/src/pointPublish.cpp.o\x1b[0m\n'}
[4.006140] (-) TimerEvent: {}
[4.106473] (-) TimerEvent: {}
[4.206837] (-) TimerEvent: {}
[4.307150] (-) TimerEvent: {}
[4.352580] (global_traj_generate) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[4.407282] (-) TimerEvent: {}
[4.498598] (global_traj_generate) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[4.507408] (-) TimerEvent: {}
[4.521251] (global_traj_generate) StdoutLine: {'line': b'-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") \n'}
[4.523152] (global_traj_generate) StdoutLine: {'line': b'-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)\n'}
[4.549627] (global_traj_generate) StdoutLine: {'line': b'-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") \n'}
[4.549829] (global_traj_generate) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[4.549886] (global_traj_generate) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10\n'}
[4.549927] (global_traj_generate) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so\n'}
[4.571951] (global_traj_generate) StdoutLine: {'line': b'-- Found PythonExtra: .so  \n'}
[4.607543] (-) TimerEvent: {}
[4.641905] (global_traj_generate) StderrLine: {'line': b'\x1b[0mCMake Deprecation Warning at /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_target_interfaces.cmake:32 (message):\n'}
[4.642134] (global_traj_generate) StderrLine: {'line': b'  Use rosidl_get_typesupport_target() and target_link_libraries() instead of\n'}
[4.642201] (global_traj_generate) StderrLine: {'line': b'  rosidl_target_interfaces()\n'}
[4.642277] (global_traj_generate) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[4.642326] (global_traj_generate) StderrLine: {'line': b'  CMakeLists.txt:60 (rosidl_target_interfaces)\n'}
[4.642371] (global_traj_generate) StderrLine: {'line': b'\n'}
[4.642418] (global_traj_generate) StderrLine: {'line': b'\x1b[0m\n'}
[4.643106] (global_traj_generate) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[4.658580] (global_traj_generate) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[4.659535] (global_traj_generate) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[4.660213] (global_traj_generate) StdoutLine: {'line': b'-- Configured cppcheck include dirs: \n'}
[4.660327] (global_traj_generate) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[4.660780] (global_traj_generate) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[4.660854] (global_traj_generate) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[4.661107] (global_traj_generate) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[4.661444] (global_traj_generate) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[4.661705] (global_traj_generate) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[4.662190] (global_traj_generate) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[4.662308] (global_traj_generate) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[4.662482] (global_traj_generate) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[4.664095] (global_traj_generate) StdoutLine: {'line': b'-- Configuring done\n'}
[4.707676] (-) TimerEvent: {}
[4.710680] (global_traj_generate) StdoutLine: {'line': b'-- Generating done\n'}
[4.725984] (global_traj_generate) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/nr_navigation/build/global_traj_generate\n'}
[4.748837] (global_traj_generate) CommandEnded: {'returncode': 0}
[4.750315] (global_traj_generate) JobProgress: {'identifier': 'global_traj_generate', 'progress': 'build'}
[4.750568] (global_traj_generate) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/nr_navigation/build/global_traj_generate', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/nr_navigation/build/global_traj_generate', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/global_traj_generate'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[4.807802] (-) TimerEvent: {}
[4.814427] (global_traj_generate) StdoutLine: {'line': b'[  2%] \x1b[34m\x1b[1mGenerating C code for ROS interfaces\x1b[0m\n'}
[4.833817] (global_traj_generate) StdoutLine: {'line': b'[  4%] \x1b[34m\x1b[1mGenerating C++ code for ROS interfaces\x1b[0m\n'}
[4.847766] (global_traj_generate) StdoutLine: {'line': b'[  4%] Built target ament_cmake_python_copy_global_traj_generate\n'}
[4.907911] (-) TimerEvent: {}
[5.008227] (-) TimerEvent: {}
[5.036031] (global_traj_generate) StdoutLine: {'line': b'running egg_info\n'}
[5.036884] (global_traj_generate) StdoutLine: {'line': b'creating global_traj_generate.egg-info\n'}
[5.037163] (global_traj_generate) StdoutLine: {'line': b'writing global_traj_generate.egg-info/PKG-INFO\n'}
[5.037411] (global_traj_generate) StdoutLine: {'line': b'writing dependency_links to global_traj_generate.egg-info/dependency_links.txt\n'}
[5.037525] (global_traj_generate) StdoutLine: {'line': b'writing top-level names to global_traj_generate.egg-info/top_level.txt\n'}
[5.037759] (global_traj_generate) StdoutLine: {'line': b"writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'\n"}
[5.040757] (global_traj_generate) StdoutLine: {'line': b"reading manifest file 'global_traj_generate.egg-info/SOURCES.txt'\n"}
[5.041346] (global_traj_generate) StdoutLine: {'line': b"writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'\n"}
[5.077019] (global_traj_generate) StdoutLine: {'line': b'[  4%] Built target ament_cmake_python_build_global_traj_generate_egg\n'}
[5.108318] (-) TimerEvent: {}
[5.191388] (global_traj_generate) StdoutLine: {'line': b'[  4%] Built target global_traj_generate__cpp\n'}
[5.208438] (-) TimerEvent: {}
[5.209640] (global_traj_generate) StdoutLine: {'line': b'[  7%] \x1b[34m\x1b[1mGenerating C++ type support for eProsima Fast-RTPS\x1b[0m\n'}
[5.216620] (global_traj_generate) StdoutLine: {'line': b'[  9%] \x1b[34m\x1b[1mGenerating C++ type support dispatch for ROS interfaces\x1b[0m\n'}
[5.217464] (global_traj_generate) StdoutLine: {'line': b'[ 12%] \x1b[34m\x1b[1mGenerating C++ introspection for ROS interfaces\x1b[0m\n'}
[5.297593] (global_traj_generate) StdoutLine: {'line': b'[ 14%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_c.dir/rosidl_generator_c/global_traj_generate/msg/detail/navigation_result__functions.c.o\x1b[0m\n'}
[5.299114] (global_traj_generate) StdoutLine: {'line': b'[ 17%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_c.dir/rosidl_generator_c/global_traj_generate/msg/detail/navigation_target__functions.c.o\x1b[0m\n'}
[5.308530] (-) TimerEvent: {}
[5.408871] (-) TimerEvent: {}
[5.419131] (global_traj_generate) StdoutLine: {'line': b'[ 19%] \x1b[32m\x1b[1mLinking C shared library libglobal_traj_generate__rosidl_generator_c.so\x1b[0m\n'}
[5.471950] (global_traj_generate) StdoutLine: {'line': b'[ 19%] Built target global_traj_generate__rosidl_generator_c\n'}
[5.493670] (global_traj_generate) StdoutLine: {'line': b'[ 21%] \x1b[34m\x1b[1mGenerating C type support for eProsima Fast-RTPS\x1b[0m\n'}
[5.494079] (global_traj_generate) StdoutLine: {'line': b'[ 24%] \x1b[34m\x1b[1mGenerating C type support dispatch for ROS interfaces\x1b[0m\n'}
[5.497518] (global_traj_generate) StdoutLine: {'line': b'[ 26%] \x1b[34m\x1b[1mGenerating C introspection for ROS interfaces\x1b[0m\n'}
[5.508982] (-) TimerEvent: {}
[5.565639] (global_traj_generate) StdoutLine: {'line': b'[ 29%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/global_traj_generate/msg/detail/navigation_result__type_support.cpp.o\x1b[0m\n'}
[5.567799] (global_traj_generate) StdoutLine: {'line': b'[ 31%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/global_traj_generate/msg/detail/navigation_target__type_support.cpp.o\x1b[0m\n'}
[5.608137] (global_traj_generate) StdoutLine: {'line': b'[ 34%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/global_traj_generate/msg/detail/dds_fastrtps/navigation_result__type_support.cpp.o\x1b[0m\n'}
[5.609409] (-) TimerEvent: {}
[5.610623] (global_traj_generate) StdoutLine: {'line': b'[ 36%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/global_traj_generate/msg/detail/dds_fastrtps/navigation_target__type_support.cpp.o\x1b[0m\n'}
[5.673272] (global_traj_generate) StdoutLine: {'line': b'[ 39%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/global_traj_generate/msg/navigation_result__type_support.cpp.o\x1b[0m\n'}
[5.678077] (global_traj_generate) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/global_traj_generate/msg/navigation_target__type_support.cpp.o\x1b[0m\n'}
[5.709525] (-) TimerEvent: {}
[5.811326] (-) TimerEvent: {}
[5.908885] (global_traj_generate) StdoutLine: {'line': b'[ 46%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_c.dir/rosidl_typesupport_c/global_traj_generate/msg/navigation_target__type_support.cpp.o\x1b[0m\n'}
[5.913359] (global_traj_generate) StdoutLine: {'line': b'[ 46%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_c.dir/rosidl_typesupport_c/global_traj_generate/msg/navigation_result__type_support.cpp.o\x1b[0m\n'}
[5.913703] (-) TimerEvent: {}
[6.008490] (global_traj_generate) StdoutLine: {'line': b'[ 48%] \x1b[32m\x1b[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_c.so\x1b[0m\n'}
[6.013842] (-) TimerEvent: {}
[6.016090] (global_traj_generate) StdoutLine: {'line': b'[ 51%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/global_traj_generate/msg/detail/navigation_target__type_support_c.cpp.o\x1b[0m\n'}
[6.018878] (global_traj_generate) StdoutLine: {'line': b'[ 53%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/global_traj_generate/msg/detail/navigation_result__type_support_c.cpp.o\x1b[0m\n'}
[6.021540] (global_traj_generate) StdoutLine: {'line': b'[ 56%] \x1b[32m\x1b[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_cpp.so\x1b[0m\n'}
[6.083414] (global_traj_generate) StdoutLine: {'line': b'[ 58%] \x1b[32m\x1b[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so\x1b[0m\n'}
[6.103587] (global_traj_generate) StdoutLine: {'line': b'[ 58%] Built target global_traj_generate__rosidl_typesupport_c\n'}
[6.113965] (-) TimerEvent: {}
[6.123119] (global_traj_generate) StdoutLine: {'line': b'[ 58%] Built target global_traj_generate__rosidl_typesupport_cpp\n'}
[6.123404] (global_traj_generate) StdoutLine: {'line': b'[ 60%] \x1b[32m\x1b[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so\x1b[0m\n'}
[6.168372] (global_traj_generate) StdoutLine: {'line': b'[ 60%] Built target global_traj_generate__rosidl_typesupport_introspection_cpp\n'}
[6.175004] (global_traj_generate) StdoutLine: {'line': b'[ 65%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/global_traj_generate/msg/detail/navigation_target__type_support.c.o\x1b[0m\n'}
[6.175308] (global_traj_generate) StdoutLine: {'line': b'[ 65%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/global_traj_generate/msg/detail/navigation_result__type_support.c.o\x1b[0m\n'}
[6.214092] (-) TimerEvent: {}
[6.232047] (global_traj_generate) StdoutLine: {'line': b'[ 68%] \x1b[32m\x1b[1mLinking C shared library libglobal_traj_generate__rosidl_typesupport_introspection_c.so\x1b[0m\n'}
[6.241096] (global_traj_generate) StdoutLine: {'line': b'[ 68%] Built target global_traj_generate__rosidl_typesupport_fastrtps_cpp\n'}
[6.276758] (global_traj_generate) StdoutLine: {'line': b'[ 68%] Built target global_traj_generate__rosidl_typesupport_introspection_c\n'}
[6.314217] (-) TimerEvent: {}
[6.342401] (global_traj_generate) StdoutLine: {'line': b'[ 70%] \x1b[32m\x1b[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so\x1b[0m\n'}
[6.414281] (-) TimerEvent: {}
[6.429340] (global_traj_generate) StdoutLine: {'line': b'[ 70%] Built target global_traj_generate__rosidl_typesupport_fastrtps_c\n'}
[6.466032] (global_traj_generate) StdoutLine: {'line': b'[ 70%] Built target global_traj_generate\n'}
[6.500321] (global_traj_generate) StdoutLine: {'line': b'[ 73%] \x1b[32mBuilding CXX object CMakeFiles/global_traj_generate_node.dir/src/global_traj_generate_ros2.cpp.o\x1b[0m\n'}
[6.505888] (global_traj_generate) StdoutLine: {'line': b'[ 75%] \x1b[34m\x1b[1mGenerating Python code for ROS interfaces\x1b[0m\n'}
[6.514393] (-) TimerEvent: {}
[6.614749] (-) TimerEvent: {}
[6.715184] (-) TimerEvent: {}
[6.815735] (-) TimerEvent: {}
[6.843817] (terrain_analysis) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/terrain_analysis/src/terrain_analysis_ros2.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid TerrainAnalysisNode::callbackCloud(sensor_msgs::msg::PointCloud2_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[6.844036] (terrain_analysis) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/terrain_analysis/src/terrain_analysis_ros2.cpp:72:71:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kcloud_msg\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.844114] (terrain_analysis) StderrLine: {'line': b'   72 |     void callbackCloud(\x1b[01;35m\x1b[Kconst sensor_msgs::msg::PointCloud2::SharedPtr cloud_msg\x1b[m\x1b[K)\n'}
[6.844165] (terrain_analysis) StderrLine: {'line': b'      |                        \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~\x1b[m\x1b[K\n'}
[6.915843] (-) TimerEvent: {}
[7.016199] (-) TimerEvent: {}
[7.043031] (global_traj_generate) StdoutLine: {'line': b'[ 75%] Built target global_traj_generate__py\n'}
[7.076626] (global_traj_generate) StdoutLine: {'line': b'[ 78%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_py.dir/rosidl_generator_py/global_traj_generate/msg/_navigation_target_s.c.o\x1b[0m\n'}
[7.076899] (global_traj_generate) StdoutLine: {'line': b'[ 80%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_py.dir/rosidl_generator_py/global_traj_generate/msg/_navigation_result_s.c.o\x1b[0m\n'}
[7.116289] (-) TimerEvent: {}
[7.216678] (-) TimerEvent: {}
[7.273856] (global_traj_generate) StdoutLine: {'line': b'[ 82%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/global_traj_generate/libglobal_traj_generate__rosidl_generator_py.so\x1b[0m\n'}
[7.316799] (-) TimerEvent: {}
[7.320248] (global_traj_generate) StdoutLine: {'line': b'[ 82%] Built target global_traj_generate__rosidl_generator_py\n'}
[7.361731] (global_traj_generate) StdoutLine: {'line': b'[ 85%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_fastrtps_c.c.o\x1b[0m\n'}
[7.362628] (global_traj_generate) StdoutLine: {'line': b'[ 87%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_introspection_c.c.o\x1b[0m\n'}
[7.368304] (global_traj_generate) StdoutLine: {'line': b'[ 90%] \x1b[32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_c.c.o\x1b[0m\n'}
[7.416913] (-) TimerEvent: {}
[7.470329] (global_traj_generate) StdoutLine: {'line': b'[ 92%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[7.471853] (global_traj_generate) StdoutLine: {'line': b'[ 95%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[7.501059] (global_traj_generate) StdoutLine: {'line': b'[ 97%] \x1b[32m\x1b[1mLinking C shared library rosidl_generator_py/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\x1b[0m\n'}
[7.517045] (-) TimerEvent: {}
[7.526664] (global_traj_generate) StdoutLine: {'line': b'[ 97%] Built target global_traj_generate__rosidl_typesupport_fastrtps_c__pyext\n'}
[7.533969] (global_traj_generate) StdoutLine: {'line': b'[ 97%] Built target global_traj_generate__rosidl_typesupport_c__pyext\n'}
[7.559739] (global_traj_generate) StdoutLine: {'line': b'[ 97%] Built target global_traj_generate__rosidl_typesupport_introspection_c__pyext\n'}
[7.617157] (-) TimerEvent: {}
[7.717536] (-) TimerEvent: {}
[7.817899] (-) TimerEvent: {}
[7.918282] (-) TimerEvent: {}
[8.018641] (-) TimerEvent: {}
[8.118996] (-) TimerEvent: {}
[8.219365] (-) TimerEvent: {}
[8.319717] (-) TimerEvent: {}
[8.420067] (-) TimerEvent: {}
[8.520423] (-) TimerEvent: {}
[8.620799] (-) TimerEvent: {}
[8.721116] (-) TimerEvent: {}
[8.821499] (-) TimerEvent: {}
[8.921878] (-) TimerEvent: {}
[9.022267] (-) TimerEvent: {}
[9.122621] (-) TimerEvent: {}
[9.223033] (-) TimerEvent: {}
[9.282885] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kdouble PIDController::Control(double, double, double, double, double)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[9.283174] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:54:41:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kdt\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[9.283299] (local_planner) StderrLine: {'line': b'   54 |     double Control(double error, \x1b[01;35m\x1b[Kdouble dt\x1b[m\x1b[K, double actual, double err_max, double limit)\n'}
[9.283399] (local_planner) StderrLine: {'line': b'      |                                  \x1b[01;35m\x1b[K~~~~~~~^~\x1b[m\x1b[K\n'}
[9.283490] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:54:52:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kactual\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[9.283585] (local_planner) StderrLine: {'line': b'   54 |     double Control(double error, double dt, \x1b[01;35m\x1b[Kdouble actual\x1b[m\x1b[K, double err_max, double limit)\n'}
[9.283675] (local_planner) StderrLine: {'line': b'      |                                             \x1b[01;35m\x1b[K~~~~~~~^~~~~~\x1b[m\x1b[K\n'}
[9.298108] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[9.298402] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:730:13:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KsinVehicleRoll\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[9.298511] (local_planner) StderrLine: {'line': b'  730 |       float \x1b[01;35m\x1b[KsinVehicleRoll\x1b[m\x1b[K = sin(vehicleRoll);\n'}
[9.298598] (local_planner) StderrLine: {'line': b'      |             \x1b[01;35m\x1b[K^~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[9.298679] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:731:13:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KcosVehicleRoll\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[9.298763] (local_planner) StderrLine: {'line': b'  731 |       float \x1b[01;35m\x1b[KcosVehicleRoll\x1b[m\x1b[K = cos(vehicleRoll);\n'}
[9.298843] (local_planner) StderrLine: {'line': b'      |             \x1b[01;35m\x1b[K^~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[9.298921] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:732:13:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KsinVehiclePitch\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[9.299002] (local_planner) StderrLine: {'line': b'  732 |       float \x1b[01;35m\x1b[KsinVehiclePitch\x1b[m\x1b[K = sin(vehiclePitch);\n'}
[9.299080] (local_planner) StderrLine: {'line': b'      |             \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[9.299159] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:733:13:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KcosVehiclePitch\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[9.299250] (local_planner) StderrLine: {'line': b'  733 |       float \x1b[01;35m\x1b[KcosVehiclePitch\x1b[m\x1b[K = cos(vehiclePitch);\n'}
[9.299331] (local_planner) StderrLine: {'line': b'      |             \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[9.323142] (-) TimerEvent: {}
[9.423519] (-) TimerEvent: {}
[9.523903] (-) TimerEvent: {}
[9.624304] (-) TimerEvent: {}
[9.724791] (-) TimerEvent: {}
[9.825249] (-) TimerEvent: {}
[9.866731] (rrt_star_global_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid rrt_star_global_planner::RRTstarPlannerROS::getPathFromTree(std::vector<Node>&, std::vector<Node>&, Node&, std::vector<geometry_msgs::msg::PoseStamped_<std::allocator<void> >, std::allocator<geometry_msgs::msg::PoseStamped_<std::allocator<void> > > >&, GetPlanMode)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[9.867022] (rrt_star_global_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:405:33:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kcurrent_node.Node::parent_id\x1b[m\x1b[K\xe2\x80\x99 may be used uninitialized in this function [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmaybe-uninitialized\x07-Wmaybe-uninitialized\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[9.867129] (rrt_star_global_planner) StderrLine: {'line': b'  405 |   while (\x1b[01;35m\x1b[Kcurrent_node.parent_id != tree1[0].parent_id\x1b[m\x1b[K)\n'}
[9.867230] (rrt_star_global_planner) StderrLine: {'line': b'      |          \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[9.867321] (rrt_star_global_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:378:8:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kcurrent_node.Node::y\x1b[m\x1b[K\xe2\x80\x99 may be used uninitialized in this function [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmaybe-uninitialized\x07-Wmaybe-uninitialized\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[9.867409] (rrt_star_global_planner) StderrLine: {'line': b'  378 |   Node \x1b[01;35m\x1b[Kcurrent_node\x1b[m\x1b[K;\n'}
[9.867494] (rrt_star_global_planner) StderrLine: {'line': b'      |        \x1b[01;35m\x1b[K^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[9.867576] (rrt_star_global_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:378:8:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kcurrent_node.Node::x\x1b[m\x1b[K\xe2\x80\x99 may be used uninitialized in this function [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmaybe-uninitialized\x07-Wmaybe-uninitialized\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[9.925458] (-) TimerEvent: {}
[10.025875] (-) TimerEvent: {}
[10.126290] (-) TimerEvent: {}
[10.219193] (rrt_star_global_planner) StdoutLine: {'line': b'[ 50%] \x1b[32m\x1b[1mLinking CXX shared library librrt_star_global_planner_lib.so\x1b[0m\n'}
[10.226412] (-) TimerEvent: {}
[10.326829] (-) TimerEvent: {}
[10.427264] (-) TimerEvent: {}
[10.527649] (-) TimerEvent: {}
[10.529420] (rrt_star_global_planner) StdoutLine: {'line': b'[ 50%] Built target rrt_star_global_planner_lib\n'}
[10.568780] (rrt_star_global_planner) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/rrt_star_planner.dir/src/rrt_star_plan_node.cpp.o\x1b[0m\n'}
[10.627769] (-) TimerEvent: {}
[10.728146] (-) TimerEvent: {}
[10.828517] (-) TimerEvent: {}
[10.928947] (-) TimerEvent: {}
[11.029290] (-) TimerEvent: {}
[11.129693] (-) TimerEvent: {}
[11.230093] (-) TimerEvent: {}
[11.330512] (-) TimerEvent: {}
[11.430941] (-) TimerEvent: {}
[11.531372] (-) TimerEvent: {}
[11.631790] (-) TimerEvent: {}
[11.732236] (-) TimerEvent: {}
[11.832666] (-) TimerEvent: {}
[11.933052] (-) TimerEvent: {}
[12.033607] (-) TimerEvent: {}
[12.133999] (-) TimerEvent: {}
[12.234389] (-) TimerEvent: {}
[12.334885] (-) TimerEvent: {}
[12.435277] (-) TimerEvent: {}
[12.535659] (-) TimerEvent: {}
[12.636211] (-) TimerEvent: {}
[12.736508] (-) TimerEvent: {}
[12.836861] (-) TimerEvent: {}
[12.937225] (-) TimerEvent: {}
[13.037610] (-) TimerEvent: {}
[13.138020] (-) TimerEvent: {}
[13.238396] (-) TimerEvent: {}
[13.338767] (-) TimerEvent: {}
[13.439150] (-) TimerEvent: {}
[13.539598] (-) TimerEvent: {}
[13.640040] (-) TimerEvent: {}
[13.740368] (-) TimerEvent: {}
[13.803304] (rrt_star_global_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_configure(const rclcpp_lifecycle::State&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[13.803716] (rrt_star_global_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:358:77:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kstate\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[13.803831] (rrt_star_global_planner) StderrLine: {'line': b'  358 |     RRTstarPlannerWithCostmap::on_configure(\x1b[01;35m\x1b[Kconst rclcpp_lifecycle::State & state\x1b[m\x1b[K)\n'}
[13.803935] (rrt_star_global_planner) StderrLine: {'line': b'      |                                             \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~\x1b[m\x1b[K\n'}
[13.840501] (-) TimerEvent: {}
[13.940909] (-) TimerEvent: {}
[14.041288] (-) TimerEvent: {}
[14.141721] (-) TimerEvent: {}
[14.242161] (-) TimerEvent: {}
[14.312636] (rrt_star_global_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_activate(const rclcpp_lifecycle::State&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[14.312926] (rrt_star_global_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:450:76:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kstate\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[14.313041] (rrt_star_global_planner) StderrLine: {'line': b'  450 |     RRTstarPlannerWithCostmap::on_activate(\x1b[01;35m\x1b[Kconst rclcpp_lifecycle::State & state\x1b[m\x1b[K)\n'}
[14.313377] (rrt_star_global_planner) StderrLine: {'line': b'      |                                            \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~\x1b[m\x1b[K\n'}
[14.313473] (rrt_star_global_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_deactivate(const rclcpp_lifecycle::State&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[14.313561] (rrt_star_global_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:465:78:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kstate\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[14.313647] (rrt_star_global_planner) StderrLine: {'line': b'  465 |     RRTstarPlannerWithCostmap::on_deactivate(\x1b[01;35m\x1b[Kconst rclcpp_lifecycle::State & state\x1b[m\x1b[K)\n'}
[14.313724] (rrt_star_global_planner) StderrLine: {'line': b'      |                                              \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~\x1b[m\x1b[K\n'}
[14.314277] (rrt_star_global_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_cleanup(const rclcpp_lifecycle::State&)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[14.314398] (rrt_star_global_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:479:75:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kstate\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[14.314482] (rrt_star_global_planner) StderrLine: {'line': b'  479 |     RRTstarPlannerWithCostmap::on_cleanup(\x1b[01;35m\x1b[Kconst rclcpp_lifecycle::State & state\x1b[m\x1b[K)\n'}
[14.314559] (rrt_star_global_planner) StderrLine: {'line': b'      |                                           \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~\x1b[m\x1b[K\n'}
[14.342292] (-) TimerEvent: {}
[14.442723] (-) TimerEvent: {}
[14.543198] (-) TimerEvent: {}
[14.643581] (-) TimerEvent: {}
[14.743982] (-) TimerEvent: {}
[14.844481] (-) TimerEvent: {}
[14.944795] (-) TimerEvent: {}
[15.045155] (-) TimerEvent: {}
[15.145543] (-) TimerEvent: {}
[15.245897] (-) TimerEvent: {}
[15.346261] (-) TimerEvent: {}
[15.446599] (-) TimerEvent: {}
[15.546964] (-) TimerEvent: {}
[15.647345] (-) TimerEvent: {}
[15.747737] (-) TimerEvent: {}
[15.848173] (-) TimerEvent: {}
[15.948662] (-) TimerEvent: {}
[16.049056] (-) TimerEvent: {}
[16.149447] (-) TimerEvent: {}
[16.249853] (-) TimerEvent: {}
[16.350280] (-) TimerEvent: {}
[16.450638] (-) TimerEvent: {}
[16.551970] (-) TimerEvent: {}
[16.652417] (-) TimerEvent: {}
[16.752790] (-) TimerEvent: {}
[16.853222] (-) TimerEvent: {}
[16.953593] (-) TimerEvent: {}
[17.053983] (-) TimerEvent: {}
[17.154378] (-) TimerEvent: {}
[17.254745] (-) TimerEvent: {}
[17.324941] (local_planner) StdoutLine: {'line': b'[ 62%] \x1b[32m\x1b[1mLinking CXX executable pointPublish\x1b[0m\n'}
[17.354864] (-) TimerEvent: {}
[17.455341] (-) TimerEvent: {}
[17.555850] (-) TimerEvent: {}
[17.656257] (-) TimerEvent: {}
[17.756696] (-) TimerEvent: {}
[17.857121] (-) TimerEvent: {}
[17.871706] (local_planner) StdoutLine: {'line': b'[ 62%] Built target pointPublish\n'}
[17.957229] (-) TimerEvent: {}
[18.057568] (-) TimerEvent: {}
[18.158119] (-) TimerEvent: {}
[18.258484] (-) TimerEvent: {}
[18.358875] (-) TimerEvent: {}
[18.459268] (-) TimerEvent: {}
[18.559663] (-) TimerEvent: {}
[18.660076] (-) TimerEvent: {}
[18.760502] (-) TimerEvent: {}
[18.860934] (-) TimerEvent: {}
[18.961355] (-) TimerEvent: {}
[19.061773] (-) TimerEvent: {}
[19.162191] (-) TimerEvent: {}
[19.262583] (-) TimerEvent: {}
[19.363056] (-) TimerEvent: {}
[19.463435] (-) TimerEvent: {}
[19.563804] (-) TimerEvent: {}
[19.664290] (-) TimerEvent: {}
[19.764708] (-) TimerEvent: {}
[19.865111] (-) TimerEvent: {}
[19.965484] (-) TimerEvent: {}
[20.065908] (-) TimerEvent: {}
[20.166296] (-) TimerEvent: {}
[20.266724] (-) TimerEvent: {}
[20.367120] (-) TimerEvent: {}
[20.467516] (-) TimerEvent: {}
[20.567901] (-) TimerEvent: {}
[20.668304] (-) TimerEvent: {}
[20.768619] (-) TimerEvent: {}
[20.790573] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid CalibrationNode::odomHandler(nav_msgs::msg::Odometry_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[20.790803] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:271:22:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kpitch\x1b[m\x1b[K\xe2\x80\x99 is used uninitialized [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wuninitialized\x07-Wuninitialized\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[20.790901] (local_planner) StderrLine: {'line': b'  271 |         double roll, \x1b[01;35m\x1b[Kpitch\x1b[m\x1b[K, yaw;\n'}
[20.790939] (local_planner) StderrLine: {'line': b'      |                      \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[20.790971] (local_planner) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:271:16:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kroll\x1b[m\x1b[K\xe2\x80\x99 is used uninitialized [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wuninitialized\x07-Wuninitialized\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[20.791011] (local_planner) StderrLine: {'line': b'  271 |         double \x1b[01;35m\x1b[Kroll\x1b[m\x1b[K, pitch, yaw;\n'}
[20.791042] (local_planner) StderrLine: {'line': b'      |                \x1b[01;35m\x1b[K^~~~\x1b[m\x1b[K\n'}
[20.868732] (-) TimerEvent: {}
[20.969071] (-) TimerEvent: {}
[20.986451] (terrain_analysis) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable terrain_analysis\x1b[0m\n'}
[21.069191] (-) TimerEvent: {}
[21.169573] (-) TimerEvent: {}
[21.269972] (-) TimerEvent: {}
[21.370366] (-) TimerEvent: {}
[21.470745] (-) TimerEvent: {}
[21.496790] (terrain_analysis) StdoutLine: {'line': b'[100%] Built target terrain_analysis\n'}
[21.516421] (terrain_analysis) CommandEnded: {'returncode': 0}
[21.517502] (terrain_analysis) JobProgress: {'identifier': 'terrain_analysis', 'progress': 'install'}
[21.528231] (terrain_analysis) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/nr_navigation/build/terrain_analysis'], 'cwd': '/home/<USER>/nr_navigation/build/terrain_analysis', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/terrain_analysis'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[21.540627] (terrain_analysis) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[21.540951] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/lib/terrain_analysis/terrain_analysis\n'}
[21.542025] (terrain_analysis) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/terrain_analysis/lib/terrain_analysis/terrain_analysis" to ""\n'}
[21.542225] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//launch\n'}
[21.542387] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//launch/terrain_analysis_launch.py\n'}
[21.542530] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config\n'}
[21.542636] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config/dynamic_detector.yaml\n'}
[21.542757] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config/patchworkpp.yaml\n'}
[21.542850] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config/1.rviz\n'}
[21.542958] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config/terrain_analysis_ros2.yaml\n'}
[21.543279] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/ament_index/resource_index/package_run_dependencies/terrain_analysis\n'}
[21.543509] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/ament_index/resource_index/parent_prefix_path/terrain_analysis\n'}
[21.543776] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.sh\n'}
[21.544022] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.dsv\n'}
[21.544231] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/environment/path.sh\n'}
[21.544546] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/environment/path.dsv\n'}
[21.544672] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/local_setup.bash\n'}
[21.544795] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/local_setup.sh\n'}
[21.544902] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/local_setup.zsh\n'}
[21.545007] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/local_setup.dsv\n'}
[21.545118] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.dsv\n'}
[21.545241] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/ament_index/resource_index/packages/terrain_analysis\n'}
[21.545359] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig.cmake\n'}
[21.545440] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig-version.cmake\n'}
[21.545519] (terrain_analysis) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.xml\n'}
[21.548228] (terrain_analysis) CommandEnded: {'returncode': 0}
[21.560926] (terrain_analysis) JobEnded: {'identifier': 'terrain_analysis', 'rc': 0}
[21.561736] (yocs_velocity_smoother) JobStarted: {'identifier': 'yocs_velocity_smoother'}
[21.570871] (yocs_velocity_smoother) JobProgress: {'identifier': 'yocs_velocity_smoother', 'progress': 'cmake'}
[21.570978] (-) TimerEvent: {}
[21.571572] (yocs_velocity_smoother) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/nr_navigation/src/yocs_velocity_smoother', '-DCMAKE_BUILD_TYPE=Release', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/yocs_velocity_smoother'], 'cwd': '/home/<USER>/nr_navigation/build/yocs_velocity_smoother', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/yocs_velocity_smoother'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[21.656890] (yocs_velocity_smoother) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[21.671118] (-) TimerEvent: {}
[21.729500] (yocs_velocity_smoother) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[21.741078] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[21.771255] (-) TimerEvent: {}
[21.839883] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[21.849412] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[21.850691] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[21.851329] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[21.855555] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[21.871372] (-) TimerEvent: {}
[21.968297] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[21.971451] (-) TimerEvent: {}
[21.973290] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[21.973528] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[21.973866] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[21.982420] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[22.071555] (-) TimerEvent: {}
[22.135720] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[22.171656] (-) TimerEvent: {}
[22.230318] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[22.271775] (-) TimerEvent: {}
[22.275077] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[22.281651] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[22.295373] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[22.314928] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[22.331788] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[22.369274] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[22.371425] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[22.371839] (-) TimerEvent: {}
[22.472119] (-) TimerEvent: {}
[22.497496] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[22.540120] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[22.572244] (-) TimerEvent: {}
[22.582547] (yocs_velocity_smoother) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[22.592320] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[22.672464] (-) TimerEvent: {}
[22.701757] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[22.702024] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[22.772574] (-) TimerEvent: {}
[22.818005] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[22.819453] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[22.872687] (-) TimerEvent: {}
[22.899280] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found rclcpp_components: 16.0.14 (/opt/ros/humble/share/rclcpp_components/cmake)\n'}
[22.930877] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)\n'}
[22.956011] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)\n'}
[22.972803] (-) TimerEvent: {}
[22.975211] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)\n'}
[23.006355] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[23.072914] (-) TimerEvent: {}
[23.102022] (yocs_velocity_smoother) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[23.103975] (yocs_velocity_smoother) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[23.104273] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /home/<USER>/nr_navigation/src/yocs_velocity_smoother/include\n'}
[23.104373] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[23.105532] (yocs_velocity_smoother) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[23.105660] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[23.106105] (yocs_velocity_smoother) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[23.106757] (yocs_velocity_smoother) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[23.107275] (yocs_velocity_smoother) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257\n"}
[23.108232] (yocs_velocity_smoother) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[23.108372] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[23.108788] (yocs_velocity_smoother) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[23.112240] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Configuring done\n'}
[23.122317] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Generating done\n'}
[23.126951] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/nr_navigation/build/yocs_velocity_smoother\n'}
[23.135861] (yocs_velocity_smoother) CommandEnded: {'returncode': 0}
[23.136625] (yocs_velocity_smoother) JobProgress: {'identifier': 'yocs_velocity_smoother', 'progress': 'build'}
[23.136951] (yocs_velocity_smoother) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/nr_navigation/build/yocs_velocity_smoother', '--', '-j16', '-l16'], 'cwd': '/home/<USER>/nr_navigation/build/yocs_velocity_smoother', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/yocs_velocity_smoother'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[23.173033] (-) TimerEvent: {}
[23.217506] (yocs_velocity_smoother) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/velocity_smoother_component.dir/src/velocity_smoother_component.cpp.o\x1b[0m\n'}
[23.273151] (-) TimerEvent: {}
[23.373535] (-) TimerEvent: {}
[23.473952] (-) TimerEvent: {}
[23.574494] (-) TimerEvent: {}
[23.674892] (-) TimerEvent: {}
[23.775280] (-) TimerEvent: {}
[23.875571] (-) TimerEvent: {}
[23.975971] (-) TimerEvent: {}
[24.076367] (-) TimerEvent: {}
[24.176756] (-) TimerEvent: {}
[24.277139] (-) TimerEvent: {}
[24.377534] (-) TimerEvent: {}
[24.477950] (-) TimerEvent: {}
[24.578288] (-) TimerEvent: {}
[24.678663] (-) TimerEvent: {}
[24.779038] (-) TimerEvent: {}
[24.879434] (-) TimerEvent: {}
[24.979809] (-) TimerEvent: {}
[25.080237] (-) TimerEvent: {}
[25.180613] (-) TimerEvent: {}
[25.281022] (-) TimerEvent: {}
[25.381410] (-) TimerEvent: {}
[25.481802] (-) TimerEvent: {}
[25.582201] (-) TimerEvent: {}
[25.682603] (-) TimerEvent: {}
[25.782985] (-) TimerEvent: {}
[25.883347] (-) TimerEvent: {}
[25.983719] (-) TimerEvent: {}
[26.084080] (-) TimerEvent: {}
[26.184466] (-) TimerEvent: {}
[26.284873] (-) TimerEvent: {}
[26.385287] (-) TimerEvent: {}
[26.485677] (-) TimerEvent: {}
[26.586128] (-) TimerEvent: {}
[26.686515] (-) TimerEvent: {}
[26.786883] (-) TimerEvent: {}
[26.887296] (-) TimerEvent: {}
[26.987665] (-) TimerEvent: {}
[27.088050] (-) TimerEvent: {}
[27.128324] (local_planner) StdoutLine: {'line': b'[ 75%] \x1b[32m\x1b[1mLinking CXX executable pathFollower\x1b[0m\n'}
[27.188211] (-) TimerEvent: {}
[27.288603] (-) TimerEvent: {}
[27.388971] (-) TimerEvent: {}
[27.489359] (-) TimerEvent: {}
[27.571939] (local_planner) StdoutLine: {'line': b'[ 75%] Built target pathFollower\n'}
[27.589478] (-) TimerEvent: {}
[27.689850] (-) TimerEvent: {}
[27.790235] (-) TimerEvent: {}
[27.890647] (-) TimerEvent: {}
[27.991044] (-) TimerEvent: {}
[28.026298] (local_planner) StdoutLine: {'line': b'[ 87%] \x1b[32m\x1b[1mLinking CXX executable calibration\x1b[0m\n'}
[28.091173] (-) TimerEvent: {}
[28.191583] (-) TimerEvent: {}
[28.292020] (-) TimerEvent: {}
[28.392467] (-) TimerEvent: {}
[28.449355] (local_planner) StdoutLine: {'line': b'[ 87%] Built target calibration\n'}
[28.492584] (-) TimerEvent: {}
[28.592962] (-) TimerEvent: {}
[28.693347] (-) TimerEvent: {}
[28.793711] (-) TimerEvent: {}
[28.894120] (-) TimerEvent: {}
[28.994517] (-) TimerEvent: {}
[29.094878] (-) TimerEvent: {}
[29.195343] (-) TimerEvent: {}
[29.295703] (-) TimerEvent: {}
[29.396077] (-) TimerEvent: {}
[29.496412] (-) TimerEvent: {}
[29.596827] (-) TimerEvent: {}
[29.658787] (global_traj_generate) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable global_traj_generate_node\x1b[0m\n'}
[29.696980] (-) TimerEvent: {}
[29.797359] (-) TimerEvent: {}
[29.897777] (-) TimerEvent: {}
[29.998170] (-) TimerEvent: {}
[30.035848] (global_traj_generate) StdoutLine: {'line': b'[100%] Built target global_traj_generate_node\n'}
[30.051025] (global_traj_generate) CommandEnded: {'returncode': 0}
[30.051953] (global_traj_generate) JobProgress: {'identifier': 'global_traj_generate', 'progress': 'install'}
[30.052557] (global_traj_generate) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/nr_navigation/build/global_traj_generate'], 'cwd': '/home/<USER>/nr_navigation/build/global_traj_generate', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/global_traj_generate'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[30.064277] (global_traj_generate) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[30.064551] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/ament_index/resource_index/rosidl_interfaces/global_traj_generate\n'}
[30.064656] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[30.064788] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[30.064910] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/navigation_target.h\n'}
[30.065037] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_generator_c__visibility_control.h\n'}
[30.065163] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/navigation_result.h\n'}
[30.065301] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[30.065401] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__functions.h\n'}
[30.065520] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__type_support.h\n'}
[30.065602] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__functions.c\n'}
[30.065711] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__struct.h\n'}
[30.065820] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__struct.h\n'}
[30.065946] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__functions.c\n'}
[30.066049] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__type_support.h\n'}
[30.066149] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__functions.h\n'}
[30.066298] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/library_path.sh\n'}
[30.066406] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/library_path.dsv\n'}
[30.066514] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_c.so\n'}
[30.066611] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_c.so" to ""\n'}
[30.066704] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[30.066825] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[30.066930] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_fastrtps_c__visibility_control.h\n'}
[30.067029] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[30.067131] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__rosidl_typesupport_fastrtps_c.h\n'}
[30.067217] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__rosidl_typesupport_fastrtps_c.h\n'}
[30.067337] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so\n'}
[30.067445] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so" to ""\n'}
[30.067530] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[30.067638] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[30.067739] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/navigation_result.hpp\n'}
[30.067835] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_generator_cpp__visibility_control.hpp\n'}
[30.067954] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/navigation_target.hpp\n'}
[30.068049] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[30.068127] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__traits.hpp\n'}
[30.068235] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__builder.hpp\n'}
[30.068332] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__builder.hpp\n'}
[30.068465] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__type_support.hpp\n'}
[30.068573] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__type_support.hpp\n'}
[30.068666] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__struct.hpp\n'}
[30.068765] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__traits.hpp\n'}
[30.068849] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__struct.hpp\n'}
[30.068956] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[30.069049] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[30.069127] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h\n'}
[30.069234] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[30.069336] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[30.069431] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/dds_fastrtps\n'}
[30.069526] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__rosidl_typesupport_fastrtps_cpp.hpp\n'}
[30.069633] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so\n'}
[30.069739] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so" to ""\n'}
[30.069861] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[30.069984] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[30.070092] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_introspection_c__visibility_control.h\n'}
[30.070201] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[30.070318] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__type_support.c\n'}
[30.070395] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__rosidl_typesupport_introspection_c.h\n'}
[30.070471] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__rosidl_typesupport_introspection_c.h\n'}
[30.070546] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__type_support.c\n'}
[30.070622] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_c.so\n'}
[30.070698] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_c.so" to ""\n'}
[30.070774] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_c.so\n'}
[30.070850] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_c.so" to ""\n'}
[30.070926] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate\n'}
[30.071006] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg\n'}
[30.071083] (global_traj_generate) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail\n'}
[30.071159] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__rosidl_typesupport_introspection_cpp.hpp\n'}
[30.071266] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__rosidl_typesupport_introspection_cpp.hpp\n'}
[30.071353] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__type_support.cpp\n'}
[30.071436] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__type_support.cpp\n'}
[30.071511] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so\n'}
[30.071587] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so" to ""\n'}
[30.071663] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_cpp.so\n'}
[30.071737] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_cpp.so" to ""\n'}
[30.071811] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/pythonpath.sh\n'}
[30.071887] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/pythonpath.dsv\n'}
[30.071965] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info\n'}
[30.072043] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/dependency_links.txt\n'}
[30.072118] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/SOURCES.txt\n'}
[30.072194] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/top_level.txt\n'}
[30.072280] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/PKG-INFO\n'}
[30.072357] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate\n'}
[30.072432] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_introspection_c.c\n'}
[30.072508] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_fastrtps_c.c\n'}
[30.072585] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[30.072660] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/libglobal_traj_generate__rosidl_generator_py.so\n'}
[30.072737] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_c.c\n'}
[30.072811] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[30.072889] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[30.072967] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg\n'}
[30.073047] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_result_s.c\n'}
[30.073121] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_target_s.c\n'}
[30.073199] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_result.py\n'}
[30.073287] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_target.py\n'}
[30.073362] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/__init__.py\n'}
[30.073442] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/__init__.py\n'}
[30.098278] (-) TimerEvent: {}
[30.099305] (global_traj_generate) StdoutLine: {'line': b"Listing '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate'...\n"}
[30.099500] (global_traj_generate) StdoutLine: {'line': b"Compiling '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/__init__.py'...\n"}
[30.099592] (global_traj_generate) StdoutLine: {'line': b"Listing '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg'...\n"}
[30.099674] (global_traj_generate) StdoutLine: {'line': b"Compiling '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/__init__.py'...\n"}
[30.099755] (global_traj_generate) StdoutLine: {'line': b"Compiling '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_result.py'...\n"}
[30.099835] (global_traj_generate) StdoutLine: {'line': b"Compiling '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_target.py'...\n"}
[30.103006] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so\n'}
[30.103240] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[30.103382] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so\n'}
[30.103554] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[30.103687] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so\n'}
[30.103930] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""\n'}
[30.104027] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_py.so\n'}
[30.104167] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_py.so" to ""\n'}
[30.104327] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/msg/NavigationTarget.idl\n'}
[30.105213] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/msg/NavigationResult.idl\n'}
[30.105380] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/msg/NavigationTarget.msg\n'}
[30.105497] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/msg/NavigationResult.msg\n'}
[30.105599] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/global_traj_generate/global_traj_generate_node\n'}
[30.107212] (global_traj_generate) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/global_traj_generate/global_traj_generate_node" to ""\n'}
[30.107362] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//launch\n'}
[30.107460] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//launch/global_traj_generate_launch.py\n'}
[30.107548] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//config\n'}
[30.107643] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//config/global_traj_generate.yaml\n'}
[30.107705] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//config/global_traj_generate_ros2.yaml\n'}
[30.107828] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/ament_index/resource_index/package_run_dependencies/global_traj_generate\n'}
[30.108048] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/ament_index/resource_index/parent_prefix_path/global_traj_generate\n'}
[30.108360] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/ament_prefix_path.sh\n'}
[30.108777] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/ament_prefix_path.dsv\n'}
[30.108931] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/path.sh\n'}
[30.109036] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/path.dsv\n'}
[30.109220] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/local_setup.bash\n'}
[30.109348] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/local_setup.sh\n'}
[30.109567] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/local_setup.zsh\n'}
[30.109749] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/local_setup.dsv\n'}
[30.109933] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.dsv\n'}
[30.110176] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/ament_index/resource_index/packages/global_traj_generate\n'}
[30.110418] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cExport.cmake\n'}
[30.110667] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cExport-release.cmake\n'}
[30.110873] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cExport.cmake\n'}
[30.111101] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cExport-release.cmake\n'}
[30.111304] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cppExport.cmake\n'}
[30.111499] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cppExport.cmake\n'}
[30.111715] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cppExport-release.cmake\n'}
[30.111919] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cExport.cmake\n'}
[30.112133] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cExport-release.cmake\n'}
[30.112338] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cExport.cmake\n'}
[30.112558] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cExport-release.cmake\n'}
[30.112747] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cppExport.cmake\n'}
[30.112957] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cppExport-release.cmake\n'}
[30.113152] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cppExport.cmake\n'}
[30.113407] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cppExport-release.cmake\n'}
[30.113623] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_pyExport.cmake\n'}
[30.113857] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_pyExport-release.cmake\n'}
[30.114033] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake-extras.cmake\n'}
[30.114247] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[30.114453] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[30.114616] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[30.114799] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_targets-extras.cmake\n'}
[30.115028] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake\n'}
[30.115180] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake\n'}
[30.115366] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generateConfig.cmake\n'}
[30.115533] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generateConfig-version.cmake\n'}
[30.115704] (global_traj_generate) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.xml\n'}
[30.118423] (global_traj_generate) CommandEnded: {'returncode': 0}
[30.129218] (global_traj_generate) JobEnded: {'identifier': 'global_traj_generate', 'rc': 0}
[30.198422] (-) TimerEvent: {}
[30.298787] (-) TimerEvent: {}
[30.399190] (-) TimerEvent: {}
[30.499568] (-) TimerEvent: {}
[30.599904] (-) TimerEvent: {}
[30.700335] (-) TimerEvent: {}
[30.800709] (-) TimerEvent: {}
[30.901114] (-) TimerEvent: {}
[31.001491] (-) TimerEvent: {}
[31.101850] (-) TimerEvent: {}
[31.122533] (rrt_star_global_planner) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable rrt_star_planner\x1b[0m\n'}
[31.201950] (-) TimerEvent: {}
[31.302245] (-) TimerEvent: {}
[31.402589] (-) TimerEvent: {}
[31.502940] (-) TimerEvent: {}
[31.541382] (rrt_star_global_planner) StdoutLine: {'line': b'[100%] Built target rrt_star_planner\n'}
[31.556899] (rrt_star_global_planner) CommandEnded: {'returncode': 0}
[31.557731] (rrt_star_global_planner) JobProgress: {'identifier': 'rrt_star_global_planner', 'progress': 'install'}
[31.558040] (rrt_star_global_planner) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/nr_navigation/build/rrt_star_global_planner'], 'cwd': '/home/<USER>/nr_navigation/build/rrt_star_global_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/rrt_star_global_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[31.567923] (rrt_star_global_planner) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[31.568179] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/librrt_star_global_planner_lib.so\n'}
[31.568598] (rrt_star_global_planner) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/librrt_star_global_planner_lib.so" to ""\n'}
[31.568763] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/rrt_star_global_planner/rrt_star_planner\n'}
[31.569939] (rrt_star_global_planner) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/rrt_star_global_planner/rrt_star_planner" to ""\n'}
[31.570078] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/include/\n'}
[31.570172] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/include//rrt_star_global_planner\n'}
[31.570352] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/include//rrt_star_global_planner/rrt_star_ros.hpp\n'}
[31.570457] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//launch\n'}
[31.570554] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//launch/rrt_node_launch.py\n'}
[31.570651] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//launch/start_planning_launch.py\n'}
[31.570756] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//params\n'}
[31.570865] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//params/rrt_star_planner.yaml\n'}
[31.570945] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//params/test_rrt_star_planner_ros2.yaml\n'}
[31.571040] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//maps\n'}
[31.571146] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//rviz\n'}
[31.571237] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//rviz/rrt_star_global_planner.rviz\n'}
[31.571341] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/rrt_star_planner_plugin.xml\n'}
[31.571447] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/library_path.sh\n'}
[31.571528] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/library_path.dsv\n'}
[31.571661] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/ament_index/resource_index/package_run_dependencies/rrt_star_global_planner\n'}
[31.571755] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/ament_index/resource_index/parent_prefix_path/rrt_star_global_planner\n'}
[31.571845] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/ament_prefix_path.sh\n'}
[31.571939] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/ament_prefix_path.dsv\n'}
[31.572041] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/path.sh\n'}
[31.572118] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/path.dsv\n'}
[31.572199] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/local_setup.bash\n'}
[31.572353] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/local_setup.sh\n'}
[31.572433] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/local_setup.zsh\n'}
[31.572515] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/local_setup.dsv\n'}
[31.572595] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.dsv\n'}
[31.572676] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/ament_index/resource_index/packages/rrt_star_global_planner\n'}
[31.572756] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[31.572836] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[31.572952] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[31.573030] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/rrt_star_global_plannerConfig.cmake\n'}
[31.573108] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/rrt_star_global_plannerConfig-version.cmake\n'}
[31.573185] (rrt_star_global_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.xml\n'}
[31.574579] (rrt_star_global_planner) CommandEnded: {'returncode': 0}
[31.584737] (rrt_star_global_planner) JobEnded: {'identifier': 'rrt_star_global_planner', 'rc': 0}
[31.603059] (-) TimerEvent: {}
[31.703396] (-) TimerEvent: {}
[31.803699] (-) TimerEvent: {}
[31.903961] (-) TimerEvent: {}
[32.004263] (-) TimerEvent: {}
[32.104565] (-) TimerEvent: {}
[32.204842] (-) TimerEvent: {}
[32.305144] (-) TimerEvent: {}
[32.405472] (-) TimerEvent: {}
[32.505757] (-) TimerEvent: {}
[32.547437] (local_planner) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable localPlanner\x1b[0m\n'}
[32.605883] (-) TimerEvent: {}
[32.706182] (-) TimerEvent: {}
[32.806451] (-) TimerEvent: {}
[32.906724] (-) TimerEvent: {}
[33.007013] (-) TimerEvent: {}
[33.107363] (-) TimerEvent: {}
[33.148865] (local_planner) StdoutLine: {'line': b'[100%] Built target localPlanner\n'}
[33.164603] (local_planner) CommandEnded: {'returncode': 0}
[33.165406] (local_planner) JobProgress: {'identifier': 'local_planner', 'progress': 'install'}
[33.166109] (local_planner) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/nr_navigation/build/local_planner'], 'cwd': '/home/<USER>/nr_navigation/build/local_planner', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/local_planner'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[33.174587] (local_planner) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[33.174766] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/lib/local_planner/localPlanner\n'}
[33.176483] (local_planner) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/local_planner/lib/local_planner/localPlanner" to ""\n'}
[33.176625] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/lib/local_planner/pathFollower\n'}
[33.177907] (local_planner) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/local_planner/lib/local_planner/pathFollower" to ""\n'}
[33.177961] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/lib/local_planner/calibration\n'}
[33.179108] (local_planner) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/local_planner/lib/local_planner/calibration" to ""\n'}
[33.179167] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/lib/local_planner/pointPublish\n'}
[33.179857] (local_planner) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/local_planner/lib/local_planner/pointPublish" to ""\n'}
[33.179921] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/include/\n'}
[33.179996] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/include//local_planner\n'}
[33.180099] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/include//local_planner/NavigationResult.h\n'}
[33.180218] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/include//local_planner/NavigationTarget.h\n'}
[33.180355] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//launch\n'}
[33.180437] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//launch/local_planner.launch\n'}
[33.180528] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//launch/local_planner_launch.py\n'}
[33.180608] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config\n'}
[33.180670] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/point_publish.yaml\n'}
[33.180756] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/pointcarfirst.txt\n'}
[33.180816] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/point.txt\n'}
[33.180905] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/point_end.txt\n'}
[33.180965] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/local_planner.yaml\n'}
[33.181026] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/calibration.yaml\n'}
[33.181126] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/path_follower.yaml\n'}
[33.181187] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/point_orin.txt\n'}
[33.181250] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/obstacle_stop.yaml\n'}
[33.181328] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//paths\n'}
[33.181382] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//paths/startPaths.ply\n'}
[33.181543] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//paths/correspondences.txt\n'}
[33.204094] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//paths/pathList.ply\n'}
[33.204265] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//paths/paths.ply\n'}
[33.207444] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//paths/path_generator.m\n'}
[33.207590] (-) TimerEvent: {}
[33.207783] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/ament_index/resource_index/package_run_dependencies/local_planner\n'}
[33.207850] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/ament_index/resource_index/parent_prefix_path/local_planner\n'}
[33.207930] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/environment/ament_prefix_path.sh\n'}
[33.207991] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/environment/ament_prefix_path.dsv\n'}
[33.208108] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/environment/path.sh\n'}
[33.208156] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/environment/path.dsv\n'}
[33.208316] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/local_setup.bash\n'}
[33.208493] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/local_setup.sh\n'}
[33.208632] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/local_setup.zsh\n'}
[33.208717] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/local_setup.dsv\n'}
[33.208791] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.dsv\n'}
[33.208874] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/ament_index/resource_index/packages/local_planner\n'}
[33.209031] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[33.209228] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/cmake/local_plannerConfig.cmake\n'}
[33.209349] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/cmake/local_plannerConfig-version.cmake\n'}
[33.209433] (local_planner) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.xml\n'}
[33.210962] (local_planner) CommandEnded: {'returncode': 0}
[33.219238] (local_planner) JobEnded: {'identifier': 'local_planner', 'rc': 0}
[33.307763] (-) TimerEvent: {}
[33.408095] (-) TimerEvent: {}
[33.508608] (-) TimerEvent: {}
[33.608993] (-) TimerEvent: {}
[33.709309] (-) TimerEvent: {}
[33.809583] (-) TimerEvent: {}
[33.909880] (-) TimerEvent: {}
[34.010180] (-) TimerEvent: {}
[34.110519] (-) TimerEvent: {}
[34.210806] (-) TimerEvent: {}
[34.311117] (-) TimerEvent: {}
[34.411400] (-) TimerEvent: {}
[34.511733] (-) TimerEvent: {}
[34.612122] (-) TimerEvent: {}
[34.712489] (-) TimerEvent: {}
[34.812849] (-) TimerEvent: {}
[34.871619] (yocs_velocity_smoother) StdoutLine: {'line': b'[ 50%] \x1b[32m\x1b[1mLinking CXX shared library libvelocity_smoother_component.so\x1b[0m\n'}
[34.913049] (-) TimerEvent: {}
[35.013689] (-) TimerEvent: {}
[35.048417] (yocs_velocity_smoother) StdoutLine: {'line': b'[ 50%] Built target velocity_smoother_component\n'}
[35.089108] (yocs_velocity_smoother) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/velocity_smoother_node.dir/src/velocity_smoother_node.cpp.o\x1b[0m\n'}
[35.113885] (-) TimerEvent: {}
[35.214504] (-) TimerEvent: {}
[35.314842] (-) TimerEvent: {}
[35.415246] (-) TimerEvent: {}
[35.515529] (-) TimerEvent: {}
[35.615830] (-) TimerEvent: {}
[35.716170] (-) TimerEvent: {}
[35.816578] (-) TimerEvent: {}
[35.916893] (-) TimerEvent: {}
[36.017231] (-) TimerEvent: {}
[36.117520] (-) TimerEvent: {}
[36.217848] (-) TimerEvent: {}
[36.318172] (-) TimerEvent: {}
[36.418475] (-) TimerEvent: {}
[36.518800] (-) TimerEvent: {}
[36.619113] (-) TimerEvent: {}
[36.719413] (-) TimerEvent: {}
[36.819733] (-) TimerEvent: {}
[36.920087] (-) TimerEvent: {}
[37.020403] (-) TimerEvent: {}
[37.120728] (-) TimerEvent: {}
[37.221055] (-) TimerEvent: {}
[37.302196] (yocs_velocity_smoother) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable velocity_smoother_node\x1b[0m\n'}
[37.321191] (-) TimerEvent: {}
[37.421773] (-) TimerEvent: {}
[37.442023] (yocs_velocity_smoother) StdoutLine: {'line': b'[100%] Built target velocity_smoother_node\n'}
[37.459021] (yocs_velocity_smoother) CommandEnded: {'returncode': 0}
[37.460327] (yocs_velocity_smoother) JobProgress: {'identifier': 'yocs_velocity_smoother', 'progress': 'install'}
[37.461090] (yocs_velocity_smoother) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/nr_navigation/build/yocs_velocity_smoother'], 'cwd': '/home/<USER>/nr_navigation/build/yocs_velocity_smoother', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:en'), ('USER', 'hy'), ('LC_TIME', 'zh_CN.UTF-8'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '3'), ('LD_LIBRARY_PATH', '/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib:/tmp/.mount_CursoranlDZv/usr/lib/:/tmp/.mount_CursoranlDZv/usr/lib32/:/tmp/.mount_CursoranlDZv/usr/lib64/:/tmp/.mount_CursoranlDZv/lib/:/tmp/.mount_CursoranlDZv/lib/i386-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/x86_64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib/aarch64-linux-gnu/:/tmp/.mount_CursoranlDZv/lib32/:/tmp/.mount_CursoranlDZv/lib64/:'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'cursor.desktop'), ('APPDIR', '/tmp/.mount_CursoranlDZv'), ('OLDPWD', '/home/<USER>/nr_navigation'), ('DISABLE_AUTO_UPDATE', 'true'), ('TERM_PROGRAM_VERSION', '1.3.5'), ('DESKTOP_SESSION', 'ubuntu'), ('PERLLIB', '/tmp/.mount_CursoranlDZv/usr/share/perl5/:/tmp/.mount_CursoranlDZv/usr/lib/perl5/:'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'sh -c "head -n 10000 | cat"'), ('VSCODE_GIT_ASKPASS_MAIN', '/tmp/.mount_CursoranlDZv/usr/share/cursor/resources/app/extensions/git/dist/askpass-main.js'), ('LC_MONETARY', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_NODE', '/tmp/.mount_CursoranlDZv/usr/share/cursor/cursor'), ('MANAGERPID', '1497'), ('npm_config_yes', 'true'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1637'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '11781'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('GTK_IM_MODULE', 'fcitx'), ('LOGNAME', 'hy'), ('OWD', '/home/<USER>/Downloads'), ('JOURNAL_STREAM', '8:16756'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'hy'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('COMPOSER_NO_INTERACTION', '1'), ('PATH', '/opt/ros/humble/bin:/tmp/.mount_CursoranlDZv/usr/bin/:/tmp/.mount_CursoranlDZv/usr/sbin/:/tmp/.mount_CursoranlDZv/usr/games/:/tmp/.mount_CursoranlDZv/bin/:/tmp/.mount_CursoranlDZv/sbin/:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/hy-Inspiron-16-Plus-7640:@/tmp/.ICE-unix/1637,unix/hy-Inspiron-16-Plus-7640:/tmp/.ICE-unix/1637'), ('INVOCATION_ID', 'd39d66dec0fc4a3a98bba198e615a66e'), ('PAPERSIZE', 'a4'), ('APPIMAGE', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('XDG_MENU_PREFIX', 'gnome-'), ('LC_ADDRESS', 'zh_CN.UTF-8'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('CURSOR_AGENT', '1'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('LC_TELEPHONE', 'zh_CN.UTF-8'), ('XMODIFIERS', '@im=fcitx'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.UTH692'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-4fcc2d74e9.sock'), ('TERM_PROGRAM', 'vscode'), ('CURSOR_TRACE_ID', '03fc05cef4ba4f359015ec5b51ff16bb'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('GSETTINGS_SCHEMA_DIR', '/tmp/.mount_CursoranlDZv/usr/share/glib-2.0/schemas/:'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('LC_NAME', 'zh_CN.UTF-8'), ('ARGV0', '/home/<USER>/Downloads/Cursor-1.3.5-x86_64.AppImage'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('LC_MEASUREMENT', 'zh_CN.UTF-8'), ('LC_IDENTIFICATION', 'zh_CN.UTF-8'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'fcitx'), ('PWD', '/home/<USER>/nr_navigation/build/yocs_velocity_smoother'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('CLUTTER_IM_MODULE', 'fcitx'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=7e6a0925b7ed4ce4b12016ba6889cc5c'), ('XDG_DATA_DIRS', '/tmp/.mount_CursoranlDZv/usr/share/:/usr/local/share:/usr/share:/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('LC_NUMERIC', 'zh_CN.UTF-8'), ('LC_PAPER', 'zh_CN.UTF-8'), ('QT_PLUGIN_PATH', '/tmp/.mount_CursoranlDZv/usr/lib/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt4/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/i386-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/x86_64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib/aarch64-linux-gnu/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib32/qt5/plugins/:/tmp/.mount_CursoranlDZv/usr/lib64/qt5/plugins/:'), ('COLCON', '1'), ('PIP_NO_INPUT', 'true'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[37.481737] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Install configuration: "Release"\n'}
[37.482110] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/libvelocity_smoother_component.so\n'}
[37.483481] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/libvelocity_smoother_component.so" to ""\n'}
[37.483750] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/yocs_velocity_smoother/velocity_smoother_node\n'}
[37.483958] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/yocs_velocity_smoother/velocity_smoother_node" to ""\n'}
[37.484098] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/include/\n'}
[37.484214] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/include//yocs_velocity_smoother\n'}
[37.484397] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/include//yocs_velocity_smoother/velocity_smoother_component.hpp\n'}
[37.484458] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//launch\n'}
[37.484503] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//launch/standalone_launch.py\n'}
[37.485245] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//launch/velocity_smoother_component.launch.py\n'}
[37.485397] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//param\n'}
[37.485477] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//param/standalone_ros2.yaml\n'}
[37.485557] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//param/standalone.yaml\n'}
[37.485645] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/plugins/velocity_smoother_component.xml\n'}
[37.485764] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/library_path.sh\n'}
[37.485830] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/library_path.dsv\n'}
[37.485950] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/ament_index/resource_index/package_run_dependencies/yocs_velocity_smoother\n'}
[37.486031] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/ament_index/resource_index/parent_prefix_path/yocs_velocity_smoother\n'}
[37.486099] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/ament_prefix_path.sh\n'}
[37.486161] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/ament_prefix_path.dsv\n'}
[37.486225] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/path.sh\n'}
[37.486282] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/path.dsv\n'}
[37.486342] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/local_setup.bash\n'}
[37.486397] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/local_setup.sh\n'}
[37.486458] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/local_setup.zsh\n'}
[37.486517] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/local_setup.dsv\n'}
[37.486579] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.dsv\n'}
[37.486650] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/ament_index/resource_index/packages/yocs_velocity_smoother\n'}
[37.486726] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/ament_index/resource_index/rclcpp_components/yocs_velocity_smoother\n'}
[37.486818] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/velocity_smoother_componentExport.cmake\n'}
[37.486894] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/velocity_smoother_componentExport-release.cmake\n'}
[37.486959] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[37.487018] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[37.487074] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[37.487126] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/ament_cmake_export_targets-extras.cmake\n'}
[37.487179] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/yocs_velocity_smootherConfig.cmake\n'}
[37.487238] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/yocs_velocity_smootherConfig-version.cmake\n'}
[37.487300] (yocs_velocity_smoother) StdoutLine: {'line': b'-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.xml\n'}
[37.488895] (yocs_velocity_smoother) CommandEnded: {'returncode': 0}
[37.499754] (yocs_velocity_smoother) JobEnded: {'identifier': 'yocs_velocity_smoother', 'rc': 0}
[37.500397] (-) EventReactorShutdown: {}
