[0.011s] Invoking command in '/home/<USER>/nr_navigation/build/yocs_velocity_smoother': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/yocs_velocity_smoother -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/yocs_velocity_smoother
[0.095s] -- The C compiler identification is GNU 11.4.0
[0.168s] -- The CXX compiler identification is GNU 11.4.0
[0.180s] -- Detecting C compiler ABI info
[0.278s] -- Detecting C compiler ABI info - done
[0.288s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.289s] -- Detecting C compile features
[0.290s] -- Detecting C compile features - done
[0.294s] -- Detecting CXX compiler ABI info
[0.407s] -- Detecting CXX compiler ABI info - done
[0.412s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.412s] -- Detecting CXX compile features
[0.412s] -- Detecting CXX compile features - done
[0.421s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.574s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.669s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.713s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.720s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.734s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.753s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.770s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.808s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.810s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.936s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.979s] -- Found FastRTPS: /opt/ros/humble/include  
[1.021s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.031s] -- Looking for pthread.h
[1.140s] -- Looking for pthread.h - found
[1.140s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.256s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.258s] -- Found Threads: TRUE  
[1.338s] -- Found rclcpp_components: 16.0.14 (/opt/ros/humble/share/rclcpp_components/cmake)
[1.369s] -- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)
[1.394s] -- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[1.414s] -- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
[1.445s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.540s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.542s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.543s] -- Configured cppcheck include dirs: /home/<USER>/nr_navigation/src/yocs_velocity_smoother/include
[1.543s] -- Configured cppcheck exclude dirs and/or files: 
[1.544s] -- Added test 'cpplint' to check C / C++ code against the Google style
[1.544s] -- Configured cpplint exclude dirs and/or files: 
[1.544s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.545s] -- Added test 'lint_cmake' to check CMake code style
[1.546s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.547s] -- Added test 'uncrustify' to check C / C++ code style
[1.547s] -- Configured uncrustify additional arguments: 
[1.547s] -- Added test 'xmllint' to check XML markup files
[1.551s] -- Configuring done
[1.561s] -- Generating done
[1.565s] -- Build files have been written to: /home/<USER>/nr_navigation/build/yocs_velocity_smoother
[1.574s] Invoked command in '/home/<USER>/nr_navigation/build/yocs_velocity_smoother' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/yocs_velocity_smoother -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/yocs_velocity_smoother
[1.576s] Invoking command in '/home/<USER>/nr_navigation/build/yocs_velocity_smoother': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/yocs_velocity_smoother -- -j16 -l16
[1.656s] [ 25%] [32mBuilding CXX object CMakeFiles/velocity_smoother_component.dir/src/velocity_smoother_component.cpp.o[0m
[13.310s] [ 50%] [32m[1mLinking CXX shared library libvelocity_smoother_component.so[0m
[13.487s] [ 50%] Built target velocity_smoother_component
[13.528s] [ 75%] [32mBuilding CXX object CMakeFiles/velocity_smoother_node.dir/src/velocity_smoother_node.cpp.o[0m
[15.741s] [100%] [32m[1mLinking CXX executable velocity_smoother_node[0m
[15.880s] [100%] Built target velocity_smoother_node
[15.898s] Invoked command in '/home/<USER>/nr_navigation/build/yocs_velocity_smoother' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/yocs_velocity_smoother -- -j16 -l16
[15.901s] Invoking command in '/home/<USER>/nr_navigation/build/yocs_velocity_smoother': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/yocs_velocity_smoother
[15.920s] -- Install configuration: "Release"
[15.920s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/libvelocity_smoother_component.so
[15.922s] -- Set runtime path of "/home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/libvelocity_smoother_component.so" to ""
[15.922s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/yocs_velocity_smoother/velocity_smoother_node
[15.922s] -- Set runtime path of "/home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/yocs_velocity_smoother/velocity_smoother_node" to ""
[15.922s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/include/
[15.923s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/include//yocs_velocity_smoother
[15.923s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/include//yocs_velocity_smoother/velocity_smoother_component.hpp
[15.923s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//launch
[15.923s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//launch/standalone_launch.py
[15.924s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//launch/velocity_smoother_component.launch.py
[15.924s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//param
[15.924s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//param/standalone_ros2.yaml
[15.924s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//param/standalone.yaml
[15.924s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/plugins/velocity_smoother_component.xml
[15.924s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/library_path.sh
[15.924s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/library_path.dsv
[15.924s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/ament_index/resource_index/package_run_dependencies/yocs_velocity_smoother
[15.924s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/ament_index/resource_index/parent_prefix_path/yocs_velocity_smoother
[15.924s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/ament_prefix_path.sh
[15.924s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/ament_prefix_path.dsv
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/path.sh
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/path.dsv
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/local_setup.bash
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/local_setup.sh
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/local_setup.zsh
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/local_setup.dsv
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.dsv
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/ament_index/resource_index/packages/yocs_velocity_smoother
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/ament_index/resource_index/rclcpp_components/yocs_velocity_smoother
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/velocity_smoother_componentExport.cmake
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/velocity_smoother_componentExport-release.cmake
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/ament_cmake_export_include_directories-extras.cmake
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/ament_cmake_export_libraries-extras.cmake
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/ament_cmake_export_dependencies-extras.cmake
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/ament_cmake_export_targets-extras.cmake
[15.925s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/yocs_velocity_smootherConfig.cmake
[15.926s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/yocs_velocity_smootherConfig-version.cmake
[15.926s] -- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.xml
[15.927s] Invoked command in '/home/<USER>/nr_navigation/build/yocs_velocity_smoother' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/yocs_velocity_smoother
