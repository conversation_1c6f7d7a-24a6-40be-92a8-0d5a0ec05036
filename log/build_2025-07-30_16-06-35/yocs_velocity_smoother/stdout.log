-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found rclcpp_components: 16.0.14 (/opt/ros/humble/share/rclcpp_components/cmake)
-- Found pluginlib: 5.1.0 (/opt/ros/humble/share/pluginlib/cmake)
-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: /home/<USER>/nr_navigation/src/yocs_velocity_smoother/include
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/nr_navigation/build/yocs_velocity_smoother
[ 25%] [32mBuilding CXX object CMakeFiles/velocity_smoother_component.dir/src/velocity_smoother_component.cpp.o[0m
[ 50%] [32m[1mLinking CXX shared library libvelocity_smoother_component.so[0m
[ 50%] Built target velocity_smoother_component
[ 75%] [32mBuilding CXX object CMakeFiles/velocity_smoother_node.dir/src/velocity_smoother_node.cpp.o[0m
[100%] [32m[1mLinking CXX executable velocity_smoother_node[0m
[100%] Built target velocity_smoother_node
-- Install configuration: "Release"
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/libvelocity_smoother_component.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/libvelocity_smoother_component.so" to ""
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/yocs_velocity_smoother/velocity_smoother_node
-- Set runtime path of "/home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/yocs_velocity_smoother/velocity_smoother_node" to ""
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/include/
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/include//yocs_velocity_smoother
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/include//yocs_velocity_smoother/velocity_smoother_component.hpp
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//launch
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//launch/standalone_launch.py
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//launch/velocity_smoother_component.launch.py
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//param
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//param/standalone_ros2.yaml
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother//param/standalone.yaml
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/plugins/velocity_smoother_component.xml
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/library_path.sh
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/library_path.dsv
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/ament_index/resource_index/package_run_dependencies/yocs_velocity_smoother
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/ament_index/resource_index/parent_prefix_path/yocs_velocity_smoother
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/path.sh
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/environment/path.dsv
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/local_setup.bash
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/local_setup.sh
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/local_setup.zsh
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/local_setup.dsv
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.dsv
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/ament_index/resource_index/packages/yocs_velocity_smoother
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/ament_index/resource_index/rclcpp_components/yocs_velocity_smoother
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/velocity_smoother_componentExport.cmake
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/velocity_smoother_componentExport-release.cmake
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/ament_cmake_export_include_directories-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/ament_cmake_export_libraries-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/ament_cmake_export_dependencies-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/ament_cmake_export_targets-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/yocs_velocity_smootherConfig.cmake
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/cmake/yocs_velocity_smootherConfig-version.cmake
-- Installing: /home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.xml
