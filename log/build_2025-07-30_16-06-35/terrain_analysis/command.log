Invoking command in '/home/<USER>/nr_navigation/build/terrain_analysis': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/terrain_analysis -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/terrain_analysis
Invoked command in '/home/<USER>/nr_navigation/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/terrain_analysis -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/terrain_analysis
Invoking command in '/home/<USER>/nr_navigation/build/terrain_analysis': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/terrain_analysis -- -j16 -l16
Invoked command in '/home/<USER>/nr_navigation/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/terrain_analysis -- -j16 -l16
Invoking command in '/home/<USER>/nr_navigation/build/terrain_analysis': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/terrain_analysis
Invoked command in '/home/<USER>/nr_navigation/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/terrain_analysis
