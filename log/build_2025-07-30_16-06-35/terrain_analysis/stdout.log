-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)
-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Found Eigen3: TRUE (found version "3.4.0") 
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found tf2_eigen: 0.25.15 (/opt/ros/humble/share/tf2_eigen/cmake)
-- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)
-- Checking for module 'eigen3'
--   Found eigen3, version 3.4.0
-- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") 
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- Checking for module 'flann'
--   Found flann, version 1.9.1
-- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  
-- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
-- Checking for module 'libusb-1.0'
--   Found libusb-1.0, version 1.0.25
-- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Found Qhull version 8.0.2
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  
-- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  
-- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  
-- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  
-- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  
-- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  
-- Found PCL_2D: /usr/include/pcl-1.12  
-- Found PCL_GEOMETRY: /usr/include/pcl-1.12  
-- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  
-- Found PCL_FEATURES: /usr/lib/x86_64-linux-gnu/libpcl_features.so  
-- Found PCL_ML: /usr/lib/x86_64-linux-gnu/libpcl_ml.so  
-- Found PCL_SEGMENTATION: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so  
-- Found PCL_VISUALIZATION: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so  
-- Found PCL_SURFACE: /usr/lib/x86_64-linux-gnu/libpcl_surface.so  
-- Found PCL_REGISTRATION: /usr/lib/x86_64-linux-gnu/libpcl_registration.so  
-- Found PCL_KEYPOINTS: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so  
-- Found PCL_TRACKING: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so  
-- Found PCL_RECOGNITION: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so  
-- Found PCL_STEREO: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so  
-- Found PCL_APPS: /usr/lib/x86_64-linux-gnu/libpcl_apps.so  
-- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  
-- Found PCL_MODELER: /usr/include/pcl-1.12  
-- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  
-- Found PCL_OUTOFCORE: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so  
-- Found PCL_PEOPLE: /usr/lib/x86_64-linux-gnu/libpcl_people.so  
-- Found laser_geometry: 2.4.0 (/opt/ros/humble/share/laser_geometry/cmake)
-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
-- Found OpenCV: /usr (found version "4.5.4") 
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: /home/<USER>/nr_navigation/src/terrain_analysis/include
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/nr_navigation/build/terrain_analysis
[ 50%] [32mBuilding CXX object CMakeFiles/terrain_analysis.dir/src/terrain_analysis_ros2.cpp.o[0m
[100%] [32m[1mLinking CXX executable terrain_analysis[0m
[100%] Built target terrain_analysis
-- Install configuration: "Release"
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/lib/terrain_analysis/terrain_analysis
-- Set runtime path of "/home/<USER>/nr_navigation/install/terrain_analysis/lib/terrain_analysis/terrain_analysis" to ""
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//launch
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//launch/terrain_analysis_launch.py
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config/dynamic_detector.yaml
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config/patchworkpp.yaml
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config/1.rviz
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config/terrain_analysis_ros2.yaml
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/ament_index/resource_index/package_run_dependencies/terrain_analysis
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/ament_index/resource_index/parent_prefix_path/terrain_analysis
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/environment/path.sh
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/environment/path.dsv
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/local_setup.bash
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/local_setup.sh
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/local_setup.zsh
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/local_setup.dsv
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.dsv
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/ament_index/resource_index/packages/terrain_analysis
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig.cmake
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig-version.cmake
-- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.xml
