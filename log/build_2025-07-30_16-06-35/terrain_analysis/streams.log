[0.013s] Invoking command in '/home/<USER>/nr_navigation/build/terrain_analysis': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/terrain_analysis -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/terrain_analysis
[0.130s] -- The C compiler identification is GNU 11.4.0
[0.250s] -- The CXX compiler identification is GNU 11.4.0
[0.261s] -- Detecting C compiler ABI info
[0.369s] -- Detecting C compiler ABI info - done
[0.376s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.377s] -- Detecting C compile features
[0.377s] -- Detecting C compile features - done
[0.380s] -- Detecting CXX compiler ABI info
[0.489s] -- Detecting CXX compiler ABI info - done
[0.494s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.494s] -- Detecting CXX compile features
[0.495s] -- Detecting CXX compile features - done
[0.496s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.626s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.705s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.738s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.742s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.749s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.759s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.771s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.806s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.808s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.962s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.991s] -- Found FastRTPS: /opt/ros/humble/include  
[1.027s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.033s] -- Looking for pthread.h
[1.136s] -- Looking for pthread.h - found
[1.137s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.251s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.253s] -- Found Threads: TRUE  
[1.298s] -- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)
[1.299s] -- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
[1.323s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[1.337s] -- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
[1.346s] -- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[1.358s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[1.359s] -- Found Eigen3: TRUE (found version "3.4.0") 
[1.360s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.425s] -- Found tf2_eigen: 0.25.15 (/opt/ros/humble/share/tf2_eigen/cmake)
[1.428s] -- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)
[1.461s] -- Checking for module 'eigen3'
[1.483s] --   Found eigen3, version 3.4.0
[1.548s] -- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") 
[1.548s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[1.581s] -- Checking for module 'flann'
[1.607s] --   Found flann, version 1.9.1
[1.675s] -- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  
[1.675s] -- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
[2.878s] -- Checking for module 'libusb-1.0'
[2.906s] --   Found libusb-1.0, version 1.0.25
[2.964s] -- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  
[2.966s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[3.063s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[3.208s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[3.216s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[3.223s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[3.370s] -- Found Qhull version 8.0.2
[3.500s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[3.626s] -- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  
[3.627s] -- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  
[3.628s] -- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  
[3.628s] -- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  
[3.629s] -- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  
[3.630s] -- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  
[3.630s] -- Found PCL_2D: /usr/include/pcl-1.12  
[3.630s] -- Found PCL_GEOMETRY: /usr/include/pcl-1.12  
[3.631s] -- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  
[3.632s] -- Found PCL_FEATURES: /usr/lib/x86_64-linux-gnu/libpcl_features.so  
[3.633s] -- Found PCL_ML: /usr/lib/x86_64-linux-gnu/libpcl_ml.so  
[3.633s] -- Found PCL_SEGMENTATION: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so  
[3.634s] -- Found PCL_VISUALIZATION: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so  
[3.635s] -- Found PCL_SURFACE: /usr/lib/x86_64-linux-gnu/libpcl_surface.so  
[3.636s] -- Found PCL_REGISTRATION: /usr/lib/x86_64-linux-gnu/libpcl_registration.so  
[3.637s] -- Found PCL_KEYPOINTS: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so  
[3.637s] -- Found PCL_TRACKING: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so  
[3.638s] -- Found PCL_RECOGNITION: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so  
[3.639s] -- Found PCL_STEREO: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so  
[3.639s] -- Found PCL_APPS: /usr/lib/x86_64-linux-gnu/libpcl_apps.so  
[3.640s] -- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  
[3.640s] -- Found PCL_MODELER: /usr/include/pcl-1.12  
[3.651s] -- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  
[3.652s] -- Found PCL_OUTOFCORE: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so  
[3.653s] -- Found PCL_PEOPLE: /usr/lib/x86_64-linux-gnu/libpcl_people.so  
[3.656s] -- Found laser_geometry: 2.4.0 (/opt/ros/humble/share/laser_geometry/cmake)
[3.660s] -- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
[3.675s] -- Found OpenCV: /usr (found version "4.5.4") 
[3.676s] [33mCMake Warning (dev) at CMakeLists.txt:28 (find_package):
[3.676s]   Policy CMP0074 is not set: find_package uses <PackageName>_ROOT variables.
[3.676s]   Run "cmake --help-policy CMP0074" for policy details.  Use the cmake_policy
[3.676s]   command to set the policy and suppress this warning.
[3.676s] 
[3.676s]   CMake variable PCL_ROOT is set to:
[3.676s] 
[3.676s]     /usr
[3.676s] 
[3.676s]   For compatibility, CMake is ignoring the variable.
[3.676s] This warning is for project developers.  Use -Wno-dev to suppress it.
[3.676s] [0m
[3.692s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[3.758s] -- Added test 'copyright' to check source files copyright and LICENSE
[3.759s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[3.759s] -- Configured cppcheck include dirs: /home/<USER>/nr_navigation/src/terrain_analysis/include
[3.759s] -- Configured cppcheck exclude dirs and/or files: 
[3.760s] -- Added test 'cpplint' to check C / C++ code against the Google style
[3.760s] -- Configured cpplint exclude dirs and/or files: 
[3.760s] -- Added test 'flake8' to check Python code syntax and style conventions
[3.760s] -- Added test 'lint_cmake' to check CMake code style
[3.761s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[3.761s] -- Added test 'uncrustify' to check C / C++ code style
[3.761s] -- Configured uncrustify additional arguments: 
[3.761s] -- Added test 'xmllint' to check XML markup files
[3.762s] -- Configuring done
[3.792s] -- Generating done
[3.799s] -- Build files have been written to: /home/<USER>/nr_navigation/build/terrain_analysis
[3.818s] Invoked command in '/home/<USER>/nr_navigation/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/terrain_analysis -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/terrain_analysis
[3.820s] Invoking command in '/home/<USER>/nr_navigation/build/terrain_analysis': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/terrain_analysis -- -j16 -l16
[3.889s] [ 50%] [32mBuilding CXX object CMakeFiles/terrain_analysis.dir/src/terrain_analysis_ros2.cpp.o[0m
[6.835s] [01m[K/home/<USER>/nr_navigation/src/terrain_analysis/src/terrain_analysis_ros2.cpp:[m[K In member function ‘[01m[Kvoid TerrainAnalysisNode::callbackCloud(sensor_msgs::msg::PointCloud2_<std::allocator<void> >::SharedPtr)[m[K’:
[6.835s] [01m[K/home/<USER>/nr_navigation/src/terrain_analysis/src/terrain_analysis_ros2.cpp:72:71:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kcloud_msg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[6.835s]    72 |     void callbackCloud([01;35m[Kconst sensor_msgs::msg::PointCloud2::SharedPtr cloud_msg[m[K)
[6.836s]       |                        [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~[m[K
[20.978s] [100%] [32m[1mLinking CXX executable terrain_analysis[0m
[21.488s] [100%] Built target terrain_analysis
[21.508s] Invoked command in '/home/<USER>/nr_navigation/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/terrain_analysis -- -j16 -l16
[21.521s] Invoking command in '/home/<USER>/nr_navigation/build/terrain_analysis': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/terrain_analysis
[21.532s] -- Install configuration: "Release"
[21.532s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/lib/terrain_analysis/terrain_analysis
[21.533s] -- Set runtime path of "/home/<USER>/nr_navigation/install/terrain_analysis/lib/terrain_analysis/terrain_analysis" to ""
[21.534s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//launch
[21.534s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//launch/terrain_analysis_launch.py
[21.534s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config
[21.534s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config/dynamic_detector.yaml
[21.534s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config/patchworkpp.yaml
[21.534s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config/1.rviz
[21.534s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis//config/terrain_analysis_ros2.yaml
[21.535s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/ament_index/resource_index/package_run_dependencies/terrain_analysis
[21.535s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/ament_index/resource_index/parent_prefix_path/terrain_analysis
[21.535s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.sh
[21.535s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/environment/ament_prefix_path.dsv
[21.536s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/environment/path.sh
[21.536s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/environment/path.dsv
[21.536s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/local_setup.bash
[21.536s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/local_setup.sh
[21.536s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/local_setup.zsh
[21.536s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/local_setup.dsv
[21.537s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.dsv
[21.537s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/ament_index/resource_index/packages/terrain_analysis
[21.537s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig.cmake
[21.537s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/cmake/terrain_analysisConfig-version.cmake
[21.537s] -- Installing: /home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.xml
[21.540s] Invoked command in '/home/<USER>/nr_navigation/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/terrain_analysis
