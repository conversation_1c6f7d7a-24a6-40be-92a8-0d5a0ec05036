[33mCMake Warning (dev) at CMakeLists.txt:28 (find_package):
  Policy CMP0074 is not set: find_package uses <PackageName>_ROOT variables.
  Run "cmake --help-policy CMP0074" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.

  CMake variable PCL_ROOT is set to:

    /usr

  For compatibility, CMake is ignoring the variable.
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[01m[K/home/<USER>/nr_navigation/src/terrain_analysis/src/terrain_analysis_ros2.cpp:[m[K In member function ‘[01m[Kvoid TerrainAnalysisNode::callbackCloud(sensor_msgs::msg::PointCloud2_<std::allocator<void> >::SharedPtr)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/terrain_analysis/src/terrain_analysis_ros2.cpp:72:71:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kcloud_msg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   72 |     void callbackCloud([01;35m[Kconst sensor_msgs::msg::PointCloud2::SharedPtr cloud_msg[m[K)
      |                        [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~[m[K
