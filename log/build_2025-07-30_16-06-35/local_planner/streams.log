[0.013s] Invoking command in '/home/<USER>/nr_navigation/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/local_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/local_planner
[0.135s] -- The C compiler identification is GNU 11.4.0
[0.254s] -- The CXX compiler identification is GNU 11.4.0
[0.265s] -- Detecting C compiler ABI info
[0.382s] -- Detecting C compiler ABI info - done
[0.390s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.391s] -- Detecting C compile features
[0.391s] -- Detecting C compile features - done
[0.395s] -- Detecting CXX compiler ABI info
[0.494s] -- Detecting CXX compiler ABI info - done
[0.500s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.500s] -- Detecting CXX compile features
[0.500s] -- Detecting CXX compile features - done
[0.502s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.634s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.712s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.743s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.746s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.753s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.763s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.776s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.810s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.812s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.943s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.985s] -- Found FastRTPS: /opt/ros/humble/include  
[1.024s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.034s] -- Looking for pthread.h
[1.139s] -- Looking for pthread.h - found
[1.140s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.249s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.251s] -- Found Threads: TRUE  
[1.302s] -- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)
[1.303s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[1.327s] -- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
[1.341s] -- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
[1.350s] -- Found tf2_ros: 0.25.15 (/opt/ros/humble/share/tf2_ros/cmake)
[1.429s] -- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[1.439s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[1.440s] -- Found Eigen3: TRUE (found version "3.4.0") 
[1.440s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.442s] -- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)
[1.469s] -- Checking for module 'eigen3'
[1.489s] --   Found eigen3, version 3.4.0
[1.547s] -- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") 
[1.547s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[1.581s] -- Checking for module 'flann'
[1.605s] --   Found flann, version 1.9.1
[1.680s] -- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  
[1.680s] -- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
[2.895s] -- Checking for module 'libusb-1.0'
[2.917s] --   Found libusb-1.0, version 1.0.25
[2.989s] -- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  
[2.989s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[3.095s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[3.242s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[3.250s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[3.258s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[3.389s] -- Found Qhull version 8.0.2
[3.518s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[3.642s] -- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  
[3.643s] -- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  
[3.644s] -- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  
[3.646s] -- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  
[3.647s] -- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  
[3.648s] -- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  
[3.648s] -- Found PCL_2D: /usr/include/pcl-1.12  
[3.648s] -- Found PCL_GEOMETRY: /usr/include/pcl-1.12  
[3.649s] -- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  
[3.650s] -- Found PCL_FEATURES: /usr/lib/x86_64-linux-gnu/libpcl_features.so  
[3.650s] -- Found PCL_ML: /usr/lib/x86_64-linux-gnu/libpcl_ml.so  
[3.651s] -- Found PCL_SEGMENTATION: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so  
[3.652s] -- Found PCL_VISUALIZATION: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so  
[3.653s] -- Found PCL_SURFACE: /usr/lib/x86_64-linux-gnu/libpcl_surface.so  
[3.654s] -- Found PCL_REGISTRATION: /usr/lib/x86_64-linux-gnu/libpcl_registration.so  
[3.654s] -- Found PCL_KEYPOINTS: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so  
[3.655s] -- Found PCL_TRACKING: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so  
[3.656s] -- Found PCL_RECOGNITION: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so  
[3.657s] -- Found PCL_STEREO: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so  
[3.658s] -- Found PCL_APPS: /usr/lib/x86_64-linux-gnu/libpcl_apps.so  
[3.658s] -- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  
[3.658s] -- Found PCL_MODELER: /usr/include/pcl-1.12  
[3.659s] -- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  
[3.659s] -- Found PCL_OUTOFCORE: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so  
[3.660s] -- Found PCL_PEOPLE: /usr/lib/x86_64-linux-gnu/libpcl_people.so  
[3.661s] [33mCMake Warning (dev) at CMakeLists.txt:27 (find_package):
[3.661s]   Policy CMP0074 is not set: find_package uses <PackageName>_ROOT variables.
[3.661s]   Run "cmake --help-policy CMP0074" for policy details.  Use the cmake_policy
[3.661s]   command to set the policy and suppress this warning.
[3.662s] 
[3.662s]   CMake variable PCL_ROOT is set to:
[3.662s] 
[3.662s]     /usr
[3.662s] 
[3.662s]   For compatibility, CMake is ignoring the variable.
[3.662s] This warning is for project developers.  Use -Wno-dev to suppress it.
[3.662s] [0m
[3.692s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[3.750s] -- Added test 'copyright' to check source files copyright and LICENSE
[3.751s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[3.752s] -- Configured cppcheck include dirs: /home/<USER>/nr_navigation/src/local_planner/include
[3.752s] -- Configured cppcheck exclude dirs and/or files: 
[3.753s] -- Added test 'cpplint' to check C / C++ code against the Google style
[3.753s] -- Configured cpplint exclude dirs and/or files: 
[3.754s] -- Added test 'flake8' to check Python code syntax and style conventions
[3.754s] -- Added test 'lint_cmake' to check CMake code style
[3.755s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[3.756s] -- Added test 'uncrustify' to check C / C++ code style
[3.756s] -- Configured uncrustify additional arguments: 
[3.756s] -- Added test 'xmllint' to check XML markup files
[3.757s] -- Configuring done
[3.831s] -- Generating done
[3.850s] -- Build files have been written to: /home/<USER>/nr_navigation/build/local_planner
[3.866s] Invoked command in '/home/<USER>/nr_navigation/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/local_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/local_planner
[3.867s] Invoking command in '/home/<USER>/nr_navigation/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/local_planner -- -j16 -l16
[3.950s] [ 12%] [32mBuilding CXX object CMakeFiles/pathFollower.dir/src/pathFollower.cpp.o[0m
[3.953s] [ 25%] [32mBuilding CXX object CMakeFiles/calibration.dir/src/calibration.cpp.o[0m
[3.960s] [ 37%] [32mBuilding CXX object CMakeFiles/localPlanner.dir/src/localPlanner.cpp.o[0m
[3.960s] [ 50%] [32mBuilding CXX object CMakeFiles/pointPublish.dir/src/pointPublish.cpp.o[0m
[9.279s] [01m[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:[m[K In member function ‘[01m[Kdouble PIDController::Control(double, double, double, double, double)[m[K’:
[9.279s] [01m[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:54:41:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kdt[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[9.279s]    54 |     double Control(double error, [01;35m[Kdouble dt[m[K, double actual, double err_max, double limit)
[9.279s]       |                                  [01;35m[K~~~~~~~^~[m[K
[9.279s] [01m[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:54:52:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kactual[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[9.279s]    54 |     double Control(double error, double dt, [01;35m[Kdouble actual[m[K, double err_max, double limit)
[9.279s]       |                                             [01;35m[K~~~~~~~^~~~~~[m[K
[9.294s] [01m[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[9.294s] [01m[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:730:13:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KsinVehicleRoll[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[9.294s]   730 |       float [01;35m[KsinVehicleRoll[m[K = sin(vehicleRoll);
[9.294s]       |             [01;35m[K^~~~~~~~~~~~~~[m[K
[9.294s] [01m[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:731:13:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KcosVehicleRoll[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[9.294s]   731 |       float [01;35m[KcosVehicleRoll[m[K = cos(vehicleRoll);
[9.295s]       |             [01;35m[K^~~~~~~~~~~~~~[m[K
[9.295s] [01m[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:732:13:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KsinVehiclePitch[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[9.295s]   732 |       float [01;35m[KsinVehiclePitch[m[K = sin(vehiclePitch);
[9.295s]       |             [01;35m[K^~~~~~~~~~~~~~~[m[K
[9.295s] [01m[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:733:13:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KcosVehiclePitch[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[9.295s]   733 |       float [01;35m[KcosVehiclePitch[m[K = cos(vehiclePitch);
[9.295s]       |             [01;35m[K^~~~~~~~~~~~~~~[m[K
[17.321s] [ 62%] [32m[1mLinking CXX executable pointPublish[0m
[17.867s] [ 62%] Built target pointPublish
[20.786s] [01m[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:[m[K In member function ‘[01m[Kvoid CalibrationNode::odomHandler(nav_msgs::msg::Odometry_<std::allocator<void> >::SharedPtr)[m[K’:
[20.787s] [01m[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:271:22:[m[K [01;35m[Kwarning: [m[K‘[01m[Kpitch[m[K’ is used uninitialized [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wuninitialized-Wuninitialized]8;;[m[K]
[20.787s]   271 |         double roll, [01;35m[Kpitch[m[K, yaw;
[20.787s]       |                      [01;35m[K^~~~~[m[K
[20.787s] [01m[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:271:16:[m[K [01;35m[Kwarning: [m[K‘[01m[Kroll[m[K’ is used uninitialized [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wuninitialized-Wuninitialized]8;;[m[K]
[20.787s]   271 |         double [01;35m[Kroll[m[K, pitch, yaw;
[20.787s]       |                [01;35m[K^~~~[m[K
[27.124s] [ 75%] [32m[1mLinking CXX executable pathFollower[0m
[27.568s] [ 75%] Built target pathFollower
[28.022s] [ 87%] [32m[1mLinking CXX executable calibration[0m
[28.445s] [ 87%] Built target calibration
[32.543s] [100%] [32m[1mLinking CXX executable localPlanner[0m
[33.145s] [100%] Built target localPlanner
[33.161s] Invoked command in '/home/<USER>/nr_navigation/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/local_planner -- -j16 -l16
[33.162s] Invoking command in '/home/<USER>/nr_navigation/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/local_planner
[33.170s] -- Install configuration: "Release"
[33.170s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/lib/local_planner/localPlanner
[33.172s] -- Set runtime path of "/home/<USER>/nr_navigation/install/local_planner/lib/local_planner/localPlanner" to ""
[33.172s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/lib/local_planner/pathFollower
[33.174s] -- Set runtime path of "/home/<USER>/nr_navigation/install/local_planner/lib/local_planner/pathFollower" to ""
[33.174s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/lib/local_planner/calibration
[33.175s] -- Set runtime path of "/home/<USER>/nr_navigation/install/local_planner/lib/local_planner/calibration" to ""
[33.175s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/lib/local_planner/pointPublish
[33.176s] -- Set runtime path of "/home/<USER>/nr_navigation/install/local_planner/lib/local_planner/pointPublish" to ""
[33.176s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/include/
[33.176s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/include//local_planner
[33.176s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/include//local_planner/NavigationResult.h
[33.176s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/include//local_planner/NavigationTarget.h
[33.176s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//launch
[33.176s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//launch/local_planner.launch
[33.176s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//launch/local_planner_launch.py
[33.176s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config
[33.176s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/point_publish.yaml
[33.176s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/pointcarfirst.txt
[33.176s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/point.txt
[33.177s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/point_end.txt
[33.177s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/local_planner.yaml
[33.177s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/calibration.yaml
[33.177s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/path_follower.yaml
[33.177s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/point_orin.txt
[33.177s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//config/obstacle_stop.yaml
[33.177s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//paths
[33.177s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//paths/startPaths.ply
[33.177s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//paths/correspondences.txt
[33.200s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//paths/pathList.ply
[33.200s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//paths/paths.ply
[33.203s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner//paths/path_generator.m
[33.203s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/ament_index/resource_index/package_run_dependencies/local_planner
[33.204s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/ament_index/resource_index/parent_prefix_path/local_planner
[33.204s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/environment/ament_prefix_path.sh
[33.204s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/environment/ament_prefix_path.dsv
[33.204s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/environment/path.sh
[33.204s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/environment/path.dsv
[33.204s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/local_setup.bash
[33.204s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/local_setup.sh
[33.204s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/local_setup.zsh
[33.204s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/local_setup.dsv
[33.204s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.dsv
[33.205s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/ament_index/resource_index/packages/local_planner
[33.205s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/cmake/ament_cmake_export_dependencies-extras.cmake
[33.205s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/cmake/local_plannerConfig.cmake
[33.205s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/cmake/local_plannerConfig-version.cmake
[33.205s] -- Installing: /home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.xml
[33.207s] Invoked command in '/home/<USER>/nr_navigation/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/local_planner
