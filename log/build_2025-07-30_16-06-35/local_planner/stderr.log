[33mCMake Warning (dev) at CMakeLists.txt:27 (find_package):
  Policy CMP0074 is not set: find_package uses <PackageName>_ROOT variables.
  Run "cmake --help-policy CMP0074" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.

  CMake variable PCL_ROOT is set to:

    /usr

  For compatibility, CMake is ignoring the variable.
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[01m[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:[m[K In member function ‘[01m[Kdouble PIDController::Control(double, double, double, double, double)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:54:41:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kdt[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   54 |     double Control(double error, [01;35m[Kdouble dt[m[K, double actual, double err_max, double limit)
      |                                  [01;35m[K~~~~~~~^~[m[K
[01m[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:54:52:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kactual[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
   54 |     double Control(double error, double dt, [01;35m[Kdouble actual[m[K, double err_max, double limit)
      |                                             [01;35m[K~~~~~~~^~~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:730:13:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KsinVehicleRoll[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  730 |       float [01;35m[KsinVehicleRoll[m[K = sin(vehicleRoll);
      |             [01;35m[K^~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:731:13:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KcosVehicleRoll[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  731 |       float [01;35m[KcosVehicleRoll[m[K = cos(vehicleRoll);
      |             [01;35m[K^~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:732:13:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KsinVehiclePitch[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  732 |       float [01;35m[KsinVehiclePitch[m[K = sin(vehiclePitch);
      |             [01;35m[K^~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/local_planner/src/localPlanner.cpp:733:13:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KcosVehiclePitch[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  733 |       float [01;35m[KcosVehiclePitch[m[K = cos(vehiclePitch);
      |             [01;35m[K^~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:[m[K In member function ‘[01m[Kvoid CalibrationNode::odomHandler(nav_msgs::msg::Odometry_<std::allocator<void> >::SharedPtr)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:271:22:[m[K [01;35m[Kwarning: [m[K‘[01m[Kpitch[m[K’ is used uninitialized [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wuninitialized-Wuninitialized]8;;[m[K]
  271 |         double roll, [01;35m[Kpitch[m[K, yaw;
      |                      [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/local_planner/src/calibration.cpp:271:16:[m[K [01;35m[Kwarning: [m[K‘[01m[Kroll[m[K’ is used uninitialized [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wuninitialized-Wuninitialized]8;;[m[K]
  271 |         double [01;35m[Kroll[m[K, pitch, yaw;
      |                [01;35m[K^~~~[m[K
