-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)
-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
-- Found tf2_ros: 0.25.15 (/opt/ros/humble/share/tf2_ros/cmake)
-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Found Eigen3: TRUE (found version "3.4.0") 
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)
-- Checking for module 'eigen3'
--   Found eigen3, version 3.4.0
-- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") 
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- Checking for module 'flann'
--   Found flann, version 1.9.1
-- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  
-- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
-- Checking for module 'libusb-1.0'
--   Found libusb-1.0, version 1.0.25
-- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
-- Found Qhull version 8.0.2
-- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
-- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  
-- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  
-- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  
-- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  
-- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  
-- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  
-- Found PCL_2D: /usr/include/pcl-1.12  
-- Found PCL_GEOMETRY: /usr/include/pcl-1.12  
-- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  
-- Found PCL_FEATURES: /usr/lib/x86_64-linux-gnu/libpcl_features.so  
-- Found PCL_ML: /usr/lib/x86_64-linux-gnu/libpcl_ml.so  
-- Found PCL_SEGMENTATION: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so  
-- Found PCL_VISUALIZATION: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so  
-- Found PCL_SURFACE: /usr/lib/x86_64-linux-gnu/libpcl_surface.so  
-- Found PCL_REGISTRATION: /usr/lib/x86_64-linux-gnu/libpcl_registration.so  
-- Found PCL_KEYPOINTS: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so  
-- Found PCL_TRACKING: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so  
-- Found PCL_RECOGNITION: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so  
-- Found PCL_STEREO: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so  
-- Found PCL_APPS: /usr/lib/x86_64-linux-gnu/libpcl_apps.so  
-- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  
-- Found PCL_MODELER: /usr/include/pcl-1.12  
-- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  
-- Found PCL_OUTOFCORE: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so  
-- Found PCL_PEOPLE: /usr/lib/x86_64-linux-gnu/libpcl_people.so  
-- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
-- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
-- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
-- Using PYTHON_EXECUTABLE: /usr/bin/python3
-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
-- Found PythonExtra: .so  
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: 
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/nr_navigation/build/global_traj_generate
[  2%] [34m[1mGenerating C code for ROS interfaces[0m
[  4%] [34m[1mGenerating C++ code for ROS interfaces[0m
[  4%] Built target ament_cmake_python_copy_global_traj_generate
running egg_info
creating global_traj_generate.egg-info
writing global_traj_generate.egg-info/PKG-INFO
writing dependency_links to global_traj_generate.egg-info/dependency_links.txt
writing top-level names to global_traj_generate.egg-info/top_level.txt
writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'
reading manifest file 'global_traj_generate.egg-info/SOURCES.txt'
writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'
[  4%] Built target ament_cmake_python_build_global_traj_generate_egg
[  4%] Built target global_traj_generate__cpp
[  7%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[  9%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[ 12%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[ 14%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_c.dir/rosidl_generator_c/global_traj_generate/msg/detail/navigation_result__functions.c.o[0m
[ 17%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_c.dir/rosidl_generator_c/global_traj_generate/msg/detail/navigation_target__functions.c.o[0m
[ 19%] [32m[1mLinking C shared library libglobal_traj_generate__rosidl_generator_c.so[0m
[ 19%] Built target global_traj_generate__rosidl_generator_c
[ 21%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[ 24%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[ 26%] [34m[1mGenerating C introspection for ROS interfaces[0m
[ 29%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/global_traj_generate/msg/detail/navigation_result__type_support.cpp.o[0m
[ 31%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/global_traj_generate/msg/detail/navigation_target__type_support.cpp.o[0m
[ 34%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/global_traj_generate/msg/detail/dds_fastrtps/navigation_result__type_support.cpp.o[0m
[ 36%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/global_traj_generate/msg/detail/dds_fastrtps/navigation_target__type_support.cpp.o[0m
[ 39%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/global_traj_generate/msg/navigation_result__type_support.cpp.o[0m
[ 41%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/global_traj_generate/msg/navigation_target__type_support.cpp.o[0m
[ 46%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_c.dir/rosidl_typesupport_c/global_traj_generate/msg/navigation_target__type_support.cpp.o[0m
[ 46%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_c.dir/rosidl_typesupport_c/global_traj_generate/msg/navigation_result__type_support.cpp.o[0m
[ 48%] [32m[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_c.so[0m
[ 51%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/global_traj_generate/msg/detail/navigation_target__type_support_c.cpp.o[0m
[ 53%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/global_traj_generate/msg/detail/navigation_result__type_support_c.cpp.o[0m
[ 56%] [32m[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_cpp.so[0m
[ 58%] [32m[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so[0m
[ 58%] Built target global_traj_generate__rosidl_typesupport_c
[ 58%] Built target global_traj_generate__rosidl_typesupport_cpp
[ 60%] [32m[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so[0m
[ 60%] Built target global_traj_generate__rosidl_typesupport_introspection_cpp
[ 65%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/global_traj_generate/msg/detail/navigation_target__type_support.c.o[0m
[ 65%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/global_traj_generate/msg/detail/navigation_result__type_support.c.o[0m
[ 68%] [32m[1mLinking C shared library libglobal_traj_generate__rosidl_typesupport_introspection_c.so[0m
[ 68%] Built target global_traj_generate__rosidl_typesupport_fastrtps_cpp
[ 68%] Built target global_traj_generate__rosidl_typesupport_introspection_c
[ 70%] [32m[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so[0m
[ 70%] Built target global_traj_generate__rosidl_typesupport_fastrtps_c
[ 70%] Built target global_traj_generate
[ 73%] [32mBuilding CXX object CMakeFiles/global_traj_generate_node.dir/src/global_traj_generate_ros2.cpp.o[0m
[ 75%] [34m[1mGenerating Python code for ROS interfaces[0m
[ 75%] Built target global_traj_generate__py
[ 78%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_py.dir/rosidl_generator_py/global_traj_generate/msg/_navigation_target_s.c.o[0m
[ 80%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_py.dir/rosidl_generator_py/global_traj_generate/msg/_navigation_result_s.c.o[0m
[ 82%] [32m[1mLinking C shared library rosidl_generator_py/global_traj_generate/libglobal_traj_generate__rosidl_generator_py.so[0m
[ 82%] Built target global_traj_generate__rosidl_generator_py
[ 85%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_fastrtps_c.c.o[0m
[ 87%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_introspection_c.c.o[0m
[ 90%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_c.c.o[0m
[ 92%] [32m[1mLinking C shared library rosidl_generator_py/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so[0m
[ 95%] [32m[1mLinking C shared library rosidl_generator_py/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so[0m
[ 97%] [32m[1mLinking C shared library rosidl_generator_py/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so[0m
[ 97%] Built target global_traj_generate__rosidl_typesupport_fastrtps_c__pyext
[ 97%] Built target global_traj_generate__rosidl_typesupport_c__pyext
[ 97%] Built target global_traj_generate__rosidl_typesupport_introspection_c__pyext
[100%] [32m[1mLinking CXX executable global_traj_generate_node[0m
[100%] Built target global_traj_generate_node
-- Install configuration: "Release"
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/ament_index/resource_index/rosidl_interfaces/global_traj_generate
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/navigation_target.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_generator_c__visibility_control.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/navigation_result.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__functions.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__type_support.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__functions.c
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__struct.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__struct.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__functions.c
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__type_support.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__functions.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/library_path.sh
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/library_path.dsv
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_c.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_c.so" to ""
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__rosidl_typesupport_fastrtps_c.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__rosidl_typesupport_fastrtps_c.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so" to ""
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/navigation_result.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_generator_cpp__visibility_control.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/navigation_target.hpp
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__traits.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__builder.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__builder.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__type_support.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__type_support.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__struct.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__traits.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__struct.hpp
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__rosidl_typesupport_fastrtps_cpp.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/dds_fastrtps
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__rosidl_typesupport_fastrtps_cpp.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so" to ""
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_introspection_c__visibility_control.h
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__type_support.c
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__rosidl_typesupport_introspection_c.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__rosidl_typesupport_introspection_c.h
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__type_support.c
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_c.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_c.so" to ""
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_c.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_c.so" to ""
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg
-- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__rosidl_typesupport_introspection_cpp.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__rosidl_typesupport_introspection_cpp.hpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__type_support.cpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__type_support.cpp
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so" to ""
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_cpp.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_cpp.so" to ""
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/pythonpath.sh
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/pythonpath.dsv
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/dependency_links.txt
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/SOURCES.txt
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/top_level.txt
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/PKG-INFO
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_introspection_c.c
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_fastrtps_c.c
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/libglobal_traj_generate__rosidl_generator_py.so
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_c.c
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_result_s.c
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_target_s.c
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_result.py
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_target.py
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/__init__.py
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/__init__.py
Listing '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate'...
Compiling '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/__init__.py'...
Listing '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg'...
Compiling '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/__init__.py'...
Compiling '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_result.py'...
Compiling '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_target.py'...
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_py.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_py.so" to ""
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/msg/NavigationTarget.idl
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/msg/NavigationResult.idl
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/msg/NavigationTarget.msg
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/msg/NavigationResult.msg
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/global_traj_generate/global_traj_generate_node
-- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/global_traj_generate/global_traj_generate_node" to ""
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//launch
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//launch/global_traj_generate_launch.py
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//config
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//config/global_traj_generate.yaml
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//config/global_traj_generate_ros2.yaml
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/ament_index/resource_index/package_run_dependencies/global_traj_generate
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/ament_index/resource_index/parent_prefix_path/global_traj_generate
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/path.sh
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/path.dsv
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/local_setup.bash
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/local_setup.sh
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/local_setup.zsh
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/local_setup.dsv
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.dsv
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/ament_index/resource_index/packages/global_traj_generate
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cExport.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cExport-release.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cExport.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cExport-release.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cppExport.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cppExport.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cppExport-release.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cExport.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cExport-release.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cExport.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cExport-release.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cppExport.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cppExport-release.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cppExport.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cppExport-release.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_pyExport.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_pyExport-release.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_dependencies-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_include_directories-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_libraries-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_targets-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generateConfig.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generateConfig-version.cmake
-- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.xml
