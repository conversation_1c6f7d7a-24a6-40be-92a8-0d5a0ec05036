[33mCMake Warning (dev) at CMakeLists.txt:27 (find_package):
  Policy CMP0074 is not set: find_package uses <PackageName>_ROOT variables.
  Run "cmake --help-policy CMP0074" for policy details.  Use the cmake_policy
  command to set the policy and suppress this warning.

  CMake variable PCL_ROOT is set to:

    /usr

  For compatibility, CMake is ignoring the variable.
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[0mCMake Deprecation Warning at /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_target_interfaces.cmake:32 (message):
  Use rosidl_get_typesupport_target() and target_link_libraries() instead of
  rosidl_target_interfaces()
Call Stack (most recent call first):
  CMakeLists.txt:60 (rosidl_target_interfaces)

[0m
