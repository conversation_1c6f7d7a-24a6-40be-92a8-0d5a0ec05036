[0.012s] Invoking command in '/home/<USER>/nr_navigation/build/global_traj_generate': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/global_traj_generate -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/global_traj_generate
[0.138s] -- The C compiler identification is GNU 11.4.0
[0.259s] -- The CXX compiler identification is GNU 11.4.0
[0.269s] -- Detecting C compiler ABI info
[0.381s] -- Detecting C compiler ABI info - done
[0.387s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.387s] -- Detecting C compile features
[0.387s] -- Detecting C compile features - done
[0.390s] -- Detecting CXX compiler ABI info
[0.487s] -- Detecting CXX compiler ABI info - done
[0.494s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.494s] -- Detecting CXX compile features
[0.495s] -- Detecting CXX compile features - done
[0.499s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.636s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.714s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.746s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.750s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.757s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.767s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.779s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.814s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.816s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.966s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.998s] -- Found FastRTPS: /opt/ros/humble/include  
[1.035s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.042s] -- Looking for pthread.h
[1.138s] -- Looking for pthread.h - found
[1.138s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.258s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.260s] -- Found Threads: TRUE  
[1.306s] -- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)
[1.307s] -- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[1.317s] -- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
[1.331s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[1.345s] -- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
[1.354s] -- Found tf2_ros: 0.25.15 (/opt/ros/humble/share/tf2_ros/cmake)
[1.433s] -- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[1.449s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[1.450s] -- Found Eigen3: TRUE (found version "3.4.0") 
[1.450s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.452s] -- Found pcl_conversions: 2.4.5 (/opt/ros/humble/share/pcl_conversions/cmake)
[1.482s] -- Checking for module 'eigen3'
[1.506s] --   Found eigen3, version 3.4.0
[1.568s] -- Found Eigen: /usr/include/eigen3 (Required is at least version "3.1") 
[1.568s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[1.597s] -- Checking for module 'flann'
[1.619s] --   Found flann, version 1.9.1
[1.685s] -- Found FLANN: /usr/lib/x86_64-linux-gnu/libflann_cpp.so  
[1.685s] -- FLANN found (include: /usr/include, lib: /usr/lib/x86_64-linux-gnu/libflann_cpp.so)
[2.903s] -- Checking for module 'libusb-1.0'
[2.923s] --   Found libusb-1.0, version 1.0.25
[2.995s] -- Found libusb: /usr/lib/x86_64-linux-gnu/libusb-1.0.so  
[2.995s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[3.084s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[3.222s] -- Eigen found (include: /usr/include/eigen3, version: 3.4.0)
[3.229s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[3.236s] -- OpenNI2 found (version: 2.2.0.33, include: /usr/include/openni2, lib: /usr/lib/x86_64-linux-gnu/libOpenNI2.so;libusb::libusb)
[3.378s] -- Found Qhull version 8.0.2
[3.520s] -- OpenNI found (version: 1.5.4.0, include: /usr/include/ni, lib: /usr/lib/libOpenNI.so;libusb::libusb)
[3.642s] -- Found PCL_COMMON: /usr/lib/x86_64-linux-gnu/libpcl_common.so  
[3.643s] -- Found PCL_KDTREE: /usr/lib/x86_64-linux-gnu/libpcl_kdtree.so  
[3.643s] -- Found PCL_OCTREE: /usr/lib/x86_64-linux-gnu/libpcl_octree.so  
[3.644s] -- Found PCL_SEARCH: /usr/lib/x86_64-linux-gnu/libpcl_search.so  
[3.645s] -- Found PCL_SAMPLE_CONSENSUS: /usr/lib/x86_64-linux-gnu/libpcl_sample_consensus.so  
[3.645s] -- Found PCL_FILTERS: /usr/lib/x86_64-linux-gnu/libpcl_filters.so  
[3.645s] -- Found PCL_2D: /usr/include/pcl-1.12  
[3.646s] -- Found PCL_GEOMETRY: /usr/include/pcl-1.12  
[3.646s] -- Found PCL_IO: /usr/lib/x86_64-linux-gnu/libpcl_io.so  
[3.647s] -- Found PCL_FEATURES: /usr/lib/x86_64-linux-gnu/libpcl_features.so  
[3.648s] -- Found PCL_ML: /usr/lib/x86_64-linux-gnu/libpcl_ml.so  
[3.648s] -- Found PCL_SEGMENTATION: /usr/lib/x86_64-linux-gnu/libpcl_segmentation.so  
[3.649s] -- Found PCL_VISUALIZATION: /usr/lib/x86_64-linux-gnu/libpcl_visualization.so  
[3.650s] -- Found PCL_SURFACE: /usr/lib/x86_64-linux-gnu/libpcl_surface.so  
[3.651s] -- Found PCL_REGISTRATION: /usr/lib/x86_64-linux-gnu/libpcl_registration.so  
[3.652s] -- Found PCL_KEYPOINTS: /usr/lib/x86_64-linux-gnu/libpcl_keypoints.so  
[3.652s] -- Found PCL_TRACKING: /usr/lib/x86_64-linux-gnu/libpcl_tracking.so  
[3.653s] -- Found PCL_RECOGNITION: /usr/lib/x86_64-linux-gnu/libpcl_recognition.so  
[3.654s] -- Found PCL_STEREO: /usr/lib/x86_64-linux-gnu/libpcl_stereo.so  
[3.654s] -- Found PCL_APPS: /usr/lib/x86_64-linux-gnu/libpcl_apps.so  
[3.655s] -- Found PCL_IN_HAND_SCANNER: /usr/include/pcl-1.12  
[3.655s] -- Found PCL_MODELER: /usr/include/pcl-1.12  
[3.659s] -- Found PCL_POINT_CLOUD_EDITOR: /usr/include/pcl-1.12  
[3.660s] -- Found PCL_OUTOFCORE: /usr/lib/x86_64-linux-gnu/libpcl_outofcore.so  
[3.661s] -- Found PCL_PEOPLE: /usr/lib/x86_64-linux-gnu/libpcl_people.so  
[3.664s] -- Found rosidl_default_generators: 1.2.0 (/opt/ros/humble/share/rosidl_default_generators/cmake)
[3.672s] [33mCMake Warning (dev) at CMakeLists.txt:27 (find_package):
[3.672s]   Policy CMP0074 is not set: find_package uses <PackageName>_ROOT variables.
[3.672s]   Run "cmake --help-policy CMP0074" for policy details.  Use the cmake_policy
[3.672s]   command to set the policy and suppress this warning.
[3.672s] 
[3.672s]   CMake variable PCL_ROOT is set to:
[3.672s] 
[3.672s]     /usr
[3.672s] 
[3.672s]   For compatibility, CMake is ignoring the variable.
[3.672s] This warning is for project developers.  Use -Wno-dev to suppress it.
[3.673s] [0m
[3.929s] -- Found ament_cmake_ros: 0.10.0 (/opt/ros/humble/share/ament_cmake_ros/cmake)
[4.352s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[4.498s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[4.521s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.10.12", minimum required is "3.6") 
[4.522s] -- Found python_cmake_module: 0.10.0 (/opt/ros/humble/share/python_cmake_module/cmake)
[4.549s] -- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.10.so (found suitable version "3.10.12", minimum required is "3.5") 
[4.549s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[4.549s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.10
[4.549s] -- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.10.so
[4.571s] -- Found PythonExtra: .so  
[4.641s] [0mCMake Deprecation Warning at /opt/ros/humble/share/rosidl_cmake/cmake/rosidl_target_interfaces.cmake:32 (message):
[4.641s]   Use rosidl_get_typesupport_target() and target_link_libraries() instead of
[4.641s]   rosidl_target_interfaces()
[4.641s] Call Stack (most recent call first):
[4.642s]   CMakeLists.txt:60 (rosidl_target_interfaces)
[4.642s] 
[4.642s] [0m
[4.642s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[4.658s] -- Added test 'copyright' to check source files copyright and LICENSE
[4.659s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[4.659s] -- Configured cppcheck include dirs: 
[4.660s] -- Configured cppcheck exclude dirs and/or files: 
[4.660s] -- Added test 'cpplint' to check C / C++ code against the Google style
[4.660s] -- Configured cpplint exclude dirs and/or files: 
[4.660s] -- Added test 'flake8' to check Python code syntax and style conventions
[4.661s] -- Added test 'lint_cmake' to check CMake code style
[4.661s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[4.661s] -- Added test 'uncrustify' to check C / C++ code style
[4.662s] -- Configured uncrustify additional arguments: 
[4.662s] -- Added test 'xmllint' to check XML markup files
[4.663s] -- Configuring done
[4.710s] -- Generating done
[4.725s] -- Build files have been written to: /home/<USER>/nr_navigation/build/global_traj_generate
[4.749s] Invoked command in '/home/<USER>/nr_navigation/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/global_traj_generate -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/global_traj_generate
[4.751s] Invoking command in '/home/<USER>/nr_navigation/build/global_traj_generate': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/global_traj_generate -- -j16 -l16
[4.814s] [  2%] [34m[1mGenerating C code for ROS interfaces[0m
[4.833s] [  4%] [34m[1mGenerating C++ code for ROS interfaces[0m
[4.847s] [  4%] Built target ament_cmake_python_copy_global_traj_generate
[5.035s] running egg_info
[5.036s] creating global_traj_generate.egg-info
[5.036s] writing global_traj_generate.egg-info/PKG-INFO
[5.037s] writing dependency_links to global_traj_generate.egg-info/dependency_links.txt
[5.037s] writing top-level names to global_traj_generate.egg-info/top_level.txt
[5.037s] writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'
[5.040s] reading manifest file 'global_traj_generate.egg-info/SOURCES.txt'
[5.041s] writing manifest file 'global_traj_generate.egg-info/SOURCES.txt'
[5.076s] [  4%] Built target ament_cmake_python_build_global_traj_generate_egg
[5.191s] [  4%] Built target global_traj_generate__cpp
[5.209s] [  7%] [34m[1mGenerating C++ type support for eProsima Fast-RTPS[0m
[5.216s] [  9%] [34m[1mGenerating C++ type support dispatch for ROS interfaces[0m
[5.217s] [ 12%] [34m[1mGenerating C++ introspection for ROS interfaces[0m
[5.297s] [ 14%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_c.dir/rosidl_generator_c/global_traj_generate/msg/detail/navigation_result__functions.c.o[0m
[5.298s] [ 17%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_c.dir/rosidl_generator_c/global_traj_generate/msg/detail/navigation_target__functions.c.o[0m
[5.418s] [ 19%] [32m[1mLinking C shared library libglobal_traj_generate__rosidl_generator_c.so[0m
[5.471s] [ 19%] Built target global_traj_generate__rosidl_generator_c
[5.493s] [ 21%] [34m[1mGenerating C type support for eProsima Fast-RTPS[0m
[5.493s] [ 24%] [34m[1mGenerating C type support dispatch for ROS interfaces[0m
[5.497s] [ 26%] [34m[1mGenerating C introspection for ROS interfaces[0m
[5.565s] [ 29%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/global_traj_generate/msg/detail/navigation_result__type_support.cpp.o[0m
[5.567s] [ 31%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_cpp.dir/rosidl_typesupport_introspection_cpp/global_traj_generate/msg/detail/navigation_target__type_support.cpp.o[0m
[5.608s] [ 34%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/global_traj_generate/msg/detail/dds_fastrtps/navigation_result__type_support.cpp.o[0m
[5.610s] [ 36%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_cpp.dir/rosidl_typesupport_fastrtps_cpp/global_traj_generate/msg/detail/dds_fastrtps/navigation_target__type_support.cpp.o[0m
[5.673s] [ 39%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/global_traj_generate/msg/navigation_result__type_support.cpp.o[0m
[5.678s] [ 41%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_cpp.dir/rosidl_typesupport_cpp/global_traj_generate/msg/navigation_target__type_support.cpp.o[0m
[5.908s] [ 46%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_c.dir/rosidl_typesupport_c/global_traj_generate/msg/navigation_target__type_support.cpp.o[0m
[5.913s] [ 46%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_c.dir/rosidl_typesupport_c/global_traj_generate/msg/navigation_result__type_support.cpp.o[0m
[6.008s] [ 48%] [32m[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_c.so[0m
[6.015s] [ 51%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/global_traj_generate/msg/detail/navigation_target__type_support_c.cpp.o[0m
[6.018s] [ 53%] [32mBuilding CXX object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c.dir/rosidl_typesupport_fastrtps_c/global_traj_generate/msg/detail/navigation_result__type_support_c.cpp.o[0m
[6.021s] [ 56%] [32m[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_cpp.so[0m
[6.083s] [ 58%] [32m[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so[0m
[6.103s] [ 58%] Built target global_traj_generate__rosidl_typesupport_c
[6.122s] [ 58%] Built target global_traj_generate__rosidl_typesupport_cpp
[6.123s] [ 60%] [32m[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so[0m
[6.168s] [ 60%] Built target global_traj_generate__rosidl_typesupport_introspection_cpp
[6.174s] [ 65%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/global_traj_generate/msg/detail/navigation_target__type_support.c.o[0m
[6.175s] [ 65%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c.dir/rosidl_typesupport_introspection_c/global_traj_generate/msg/detail/navigation_result__type_support.c.o[0m
[6.231s] [ 68%] [32m[1mLinking C shared library libglobal_traj_generate__rosidl_typesupport_introspection_c.so[0m
[6.240s] [ 68%] Built target global_traj_generate__rosidl_typesupport_fastrtps_cpp
[6.276s] [ 68%] Built target global_traj_generate__rosidl_typesupport_introspection_c
[6.342s] [ 70%] [32m[1mLinking CXX shared library libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so[0m
[6.429s] [ 70%] Built target global_traj_generate__rosidl_typesupport_fastrtps_c
[6.465s] [ 70%] Built target global_traj_generate
[6.500s] [ 73%] [32mBuilding CXX object CMakeFiles/global_traj_generate_node.dir/src/global_traj_generate_ros2.cpp.o[0m
[6.505s] [ 75%] [34m[1mGenerating Python code for ROS interfaces[0m
[7.042s] [ 75%] Built target global_traj_generate__py
[7.076s] [ 78%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_py.dir/rosidl_generator_py/global_traj_generate/msg/_navigation_target_s.c.o[0m
[7.076s] [ 80%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_generator_py.dir/rosidl_generator_py/global_traj_generate/msg/_navigation_result_s.c.o[0m
[7.273s] [ 82%] [32m[1mLinking C shared library rosidl_generator_py/global_traj_generate/libglobal_traj_generate__rosidl_generator_py.so[0m
[7.320s] [ 82%] Built target global_traj_generate__rosidl_generator_py
[7.361s] [ 85%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_fastrtps_c__pyext.dir/rosidl_generator_py/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_fastrtps_c.c.o[0m
[7.362s] [ 87%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_introspection_c__pyext.dir/rosidl_generator_py/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_introspection_c.c.o[0m
[7.368s] [ 90%] [32mBuilding C object CMakeFiles/global_traj_generate__rosidl_typesupport_c__pyext.dir/rosidl_generator_py/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_c.c.o[0m
[7.470s] [ 92%] [32m[1mLinking C shared library rosidl_generator_py/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so[0m
[7.471s] [ 95%] [32m[1mLinking C shared library rosidl_generator_py/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so[0m
[7.500s] [ 97%] [32m[1mLinking C shared library rosidl_generator_py/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so[0m
[7.526s] [ 97%] Built target global_traj_generate__rosidl_typesupport_fastrtps_c__pyext
[7.533s] [ 97%] Built target global_traj_generate__rosidl_typesupport_c__pyext
[7.559s] [ 97%] Built target global_traj_generate__rosidl_typesupport_introspection_c__pyext
[29.658s] [100%] [32m[1mLinking CXX executable global_traj_generate_node[0m
[30.035s] [100%] Built target global_traj_generate_node
[30.051s] Invoked command in '/home/<USER>/nr_navigation/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/global_traj_generate -- -j16 -l16
[30.052s] Invoking command in '/home/<USER>/nr_navigation/build/global_traj_generate': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/global_traj_generate
[30.064s] -- Install configuration: "Release"
[30.064s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/ament_index/resource_index/rosidl_interfaces/global_traj_generate
[30.064s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate
[30.064s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg
[30.064s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/navigation_target.h
[30.064s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_generator_c__visibility_control.h
[30.064s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/navigation_result.h
[30.065s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail
[30.065s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__functions.h
[30.065s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__type_support.h
[30.065s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__functions.c
[30.065s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__struct.h
[30.065s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__struct.h
[30.065s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__functions.c
[30.065s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__type_support.h
[30.065s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__functions.h
[30.066s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/library_path.sh
[30.066s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/library_path.dsv
[30.066s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_c.so
[30.066s] -- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_c.so" to ""
[30.066s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate
[30.066s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg
[30.066s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_fastrtps_c__visibility_control.h
[30.066s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail
[30.066s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__rosidl_typesupport_fastrtps_c.h
[30.066s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__rosidl_typesupport_fastrtps_c.h
[30.067s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so
[30.067s] -- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_c.so" to ""
[30.067s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate
[30.067s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg
[30.067s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/navigation_result.hpp
[30.067s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_generator_cpp__visibility_control.hpp
[30.067s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/navigation_target.hpp
[30.067s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail
[30.067s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__traits.hpp
[30.067s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__builder.hpp
[30.068s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__builder.hpp
[30.068s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__type_support.hpp
[30.068s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__type_support.hpp
[30.068s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__struct.hpp
[30.068s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__traits.hpp
[30.068s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__struct.hpp
[30.068s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate
[30.068s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg
[30.068s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_fastrtps_cpp__visibility_control.h
[30.068s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail
[30.069s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__rosidl_typesupport_fastrtps_cpp.hpp
[30.069s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/dds_fastrtps
[30.069s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__rosidl_typesupport_fastrtps_cpp.hpp
[30.069s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so
[30.069s] -- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_fastrtps_cpp.so" to ""
[30.069s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate
[30.069s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg
[30.069s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/rosidl_typesupport_introspection_c__visibility_control.h
[30.069s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail
[30.070s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__type_support.c
[30.070s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__rosidl_typesupport_introspection_c.h
[30.070s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__rosidl_typesupport_introspection_c.h
[30.070s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__type_support.c
[30.070s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_c.so
[30.070s] -- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_c.so" to ""
[30.070s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_c.so
[30.070s] -- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_c.so" to ""
[30.070s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate
[30.070s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg
[30.070s] -- Up-to-date: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail
[30.070s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__rosidl_typesupport_introspection_cpp.hpp
[30.070s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__rosidl_typesupport_introspection_cpp.hpp
[30.071s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_target__type_support.cpp
[30.071s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/include/global_traj_generate/global_traj_generate/msg/detail/navigation_result__type_support.cpp
[30.071s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so
[30.071s] -- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_introspection_cpp.so" to ""
[30.071s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_cpp.so
[30.071s] -- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_typesupport_cpp.so" to ""
[30.071s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/pythonpath.sh
[30.071s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/pythonpath.dsv
[30.071s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info
[30.071s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/dependency_links.txt
[30.071s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/SOURCES.txt
[30.071s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/top_level.txt
[30.071s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate-0.0.0-py3.10.egg-info/PKG-INFO
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_introspection_c.c
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_fastrtps_c.c
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/libglobal_traj_generate__rosidl_generator_py.so
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/_global_traj_generate_s.ep.rosidl_typesupport_c.c
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_result_s.c
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_target_s.c
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_result.py
[30.072s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_target.py
[30.073s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/__init__.py
[30.073s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/__init__.py
[30.099s] Listing '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate'...
[30.099s] Compiling '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/__init__.py'...
[30.099s] Listing '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg'...
[30.099s] Compiling '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/__init__.py'...
[30.099s] Compiling '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_result.py'...
[30.099s] Compiling '/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/msg/_navigation_target.py'...
[30.102s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so
[30.102s] -- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_fastrtps_c.cpython-310-x86_64-linux-gnu.so" to ""
[30.103s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so
[30.103s] -- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_introspection_c.cpython-310-x86_64-linux-gnu.so" to ""
[30.103s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so
[30.103s] -- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/local/lib/python3.10/dist-packages/global_traj_generate/global_traj_generate_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so" to ""
[30.103s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_py.so
[30.103s] -- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/libglobal_traj_generate__rosidl_generator_py.so" to ""
[30.104s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/msg/NavigationTarget.idl
[30.104s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/msg/NavigationResult.idl
[30.105s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/msg/NavigationTarget.msg
[30.105s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/msg/NavigationResult.msg
[30.105s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/lib/global_traj_generate/global_traj_generate_node
[30.106s] -- Set runtime path of "/home/<USER>/nr_navigation/install/global_traj_generate/lib/global_traj_generate/global_traj_generate_node" to ""
[30.107s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//launch
[30.107s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//launch/global_traj_generate_launch.py
[30.107s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//config
[30.107s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//config/global_traj_generate.yaml
[30.107s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate//config/global_traj_generate_ros2.yaml
[30.107s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/ament_index/resource_index/package_run_dependencies/global_traj_generate
[30.107s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/ament_index/resource_index/parent_prefix_path/global_traj_generate
[30.108s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/ament_prefix_path.sh
[30.108s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/ament_prefix_path.dsv
[30.108s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/path.sh
[30.108s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/environment/path.dsv
[30.108s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/local_setup.bash
[30.109s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/local_setup.sh
[30.109s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/local_setup.zsh
[30.109s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/local_setup.dsv
[30.109s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.dsv
[30.109s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/ament_index/resource_index/packages/global_traj_generate
[30.110s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cExport.cmake
[30.110s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cExport-release.cmake
[30.110s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cExport.cmake
[30.110s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cExport-release.cmake
[30.110s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_cppExport.cmake
[30.111s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cppExport.cmake
[30.111s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_typesupport_fastrtps_cppExport-release.cmake
[30.111s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cExport.cmake
[30.111s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cExport-release.cmake
[30.112s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cExport.cmake
[30.112s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cExport-release.cmake
[30.112s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cppExport.cmake
[30.112s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_introspection_cppExport-release.cmake
[30.112s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cppExport.cmake
[30.113s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generate__rosidl_typesupport_cppExport-release.cmake
[30.113s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_pyExport.cmake
[30.113s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/export_global_traj_generate__rosidl_generator_pyExport-release.cmake
[30.113s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake-extras.cmake
[30.113s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_dependencies-extras.cmake
[30.114s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_include_directories-extras.cmake
[30.114s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_libraries-extras.cmake
[30.114s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/ament_cmake_export_targets-extras.cmake
[30.114s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake_export_typesupport_targets-extras.cmake
[30.114s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/rosidl_cmake_export_typesupport_libraries-extras.cmake
[30.115s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generateConfig.cmake
[30.115s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/cmake/global_traj_generateConfig-version.cmake
[30.115s] -- Installing: /home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.xml
[30.118s] Invoked command in '/home/<USER>/nr_navigation/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/global_traj_generate
