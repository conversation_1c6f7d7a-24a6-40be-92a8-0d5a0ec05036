[0.081s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'yocs_velocity_smoother', 'terrain_analysis', 'global_traj_generate', 'rrt_star_global_planner', 'local_planner', '--cmake-args', '-DCMAKE_BUILD_TYPE=Release', '--parallel-workers', '4']
[0.081s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=4, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['yocs_velocity_smoother', 'terrain_analysis', 'global_traj_generate', 'rrt_star_global_planner', 'local_planner'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=['-DCMAKE_BUILD_TYPE=Release'], cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x76f767304e20>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x76f767304a60>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x76f767304a60>>)
[0.189s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.189s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.189s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.189s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.189s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.189s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.189s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/nr_navigation'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.189s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.190s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.197s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore_ament_install'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_pkg']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_pkg'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_meta']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_meta'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ros']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ros'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['cmake', 'python']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'cmake'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['python_setup_py']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python_setup_py'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore_ament_install'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_pkg']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_pkg'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_meta']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_meta'
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ros']
[0.198s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ros'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['cmake', 'python']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'cmake'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['python_setup_py']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python_setup_py'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extensions ['ignore', 'ignore_ament_install']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'ignore'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'ignore_ament_install'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extensions ['colcon_pkg']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'colcon_pkg'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extensions ['colcon_meta']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'colcon_meta'
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extensions ['ros']
[0.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/global_traj_generate) by extension 'ros'
[0.201s] DEBUG:colcon.colcon_core.package_identification:Package 'src/global_traj_generate' with type 'ros.ament_cmake' and name 'global_traj_generate'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ignore', 'ignore_ament_install']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ignore_ament_install'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_pkg']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_pkg'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['colcon_meta']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'colcon_meta'
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extensions ['ros']
[0.202s] Level 1:colcon.colcon_core.package_identification:_identify(src/local_planner) by extension 'ros'
[0.203s] DEBUG:colcon.colcon_core.package_identification:Package 'src/local_planner' with type 'ros.ament_cmake' and name 'local_planner'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/rrt_star_global_planner) by extensions ['ignore', 'ignore_ament_install']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/rrt_star_global_planner) by extension 'ignore'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/rrt_star_global_planner) by extension 'ignore_ament_install'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/rrt_star_global_planner) by extensions ['colcon_pkg']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/rrt_star_global_planner) by extension 'colcon_pkg'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/rrt_star_global_planner) by extensions ['colcon_meta']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/rrt_star_global_planner) by extension 'colcon_meta'
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/rrt_star_global_planner) by extensions ['ros']
[0.203s] Level 1:colcon.colcon_core.package_identification:_identify(src/rrt_star_global_planner) by extension 'ros'
[0.204s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rrt_star_global_planner' with type 'ros.ament_cmake' and name 'rrt_star_global_planner'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ignore', 'ignore_ament_install']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ignore_ament_install'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_pkg']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_pkg'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['colcon_meta']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'colcon_meta'
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extensions ['ros']
[0.204s] Level 1:colcon.colcon_core.package_identification:_identify(src/terrain_analysis) by extension 'ros'
[0.205s] DEBUG:colcon.colcon_core.package_identification:Package 'src/terrain_analysis' with type 'ros.ament_cmake' and name 'terrain_analysis'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/yocs_velocity_smoother) by extensions ['ignore', 'ignore_ament_install']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/yocs_velocity_smoother) by extension 'ignore'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/yocs_velocity_smoother) by extension 'ignore_ament_install'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/yocs_velocity_smoother) by extensions ['colcon_pkg']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/yocs_velocity_smoother) by extension 'colcon_pkg'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/yocs_velocity_smoother) by extensions ['colcon_meta']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/yocs_velocity_smoother) by extension 'colcon_meta'
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/yocs_velocity_smoother) by extensions ['ros']
[0.205s] Level 1:colcon.colcon_core.package_identification:_identify(src/yocs_velocity_smoother) by extension 'ros'
[0.206s] DEBUG:colcon.colcon_core.package_identification:Package 'src/yocs_velocity_smoother' with type 'ros.ament_cmake' and name 'yocs_velocity_smoother'
[0.206s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.206s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.206s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.206s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.206s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.228s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.228s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.230s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 435 installed packages in /opt/ros/humble
[0.231s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.263s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_target' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_clean_cache' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_clean_first' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'cmake_force_configure' from command line to 'False'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'ament_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'catkin_cmake_args' from command line to 'None'
[0.263s] Level 5:colcon.colcon_core.verb:set package 'global_traj_generate' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.263s] DEBUG:colcon.colcon_core.verb:Building package 'global_traj_generate' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/nr_navigation/build/global_traj_generate', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/nr_navigation/install/global_traj_generate', 'merge_install': False, 'path': '/home/<USER>/nr_navigation/src/global_traj_generate', 'symlink_install': False, 'test_result_base': None}
[0.263s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'local_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.264s] DEBUG:colcon.colcon_core.verb:Building package 'local_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/nr_navigation/build/local_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/nr_navigation/install/local_planner', 'merge_install': False, 'path': '/home/<USER>/nr_navigation/src/local_planner', 'symlink_install': False, 'test_result_base': None}
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rrt_star_global_planner' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rrt_star_global_planner' build argument 'cmake_target' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rrt_star_global_planner' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rrt_star_global_planner' build argument 'cmake_clean_cache' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rrt_star_global_planner' build argument 'cmake_clean_first' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rrt_star_global_planner' build argument 'cmake_force_configure' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rrt_star_global_planner' build argument 'ament_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rrt_star_global_planner' build argument 'catkin_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'rrt_star_global_planner' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.264s] DEBUG:colcon.colcon_core.verb:Building package 'rrt_star_global_planner' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/nr_navigation/build/rrt_star_global_planner', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/nr_navigation/install/rrt_star_global_planner', 'merge_install': False, 'path': '/home/<USER>/nr_navigation/src/rrt_star_global_planner', 'symlink_install': False, 'test_result_base': None}
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_target' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_clean_cache' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_clean_first' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'cmake_force_configure' from command line to 'False'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'ament_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'catkin_cmake_args' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'terrain_analysis' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.264s] DEBUG:colcon.colcon_core.verb:Building package 'terrain_analysis' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/nr_navigation/build/terrain_analysis', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/nr_navigation/install/terrain_analysis', 'merge_install': False, 'path': '/home/<USER>/nr_navigation/src/terrain_analysis', 'symlink_install': False, 'test_result_base': None}
[0.264s] Level 5:colcon.colcon_core.verb:set package 'yocs_velocity_smoother' build argument 'cmake_args' from command line to '['-DCMAKE_BUILD_TYPE=Release']'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'yocs_velocity_smoother' build argument 'cmake_target' from command line to 'None'
[0.264s] Level 5:colcon.colcon_core.verb:set package 'yocs_velocity_smoother' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'yocs_velocity_smoother' build argument 'cmake_clean_cache' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'yocs_velocity_smoother' build argument 'cmake_clean_first' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'yocs_velocity_smoother' build argument 'cmake_force_configure' from command line to 'False'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'yocs_velocity_smoother' build argument 'ament_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'yocs_velocity_smoother' build argument 'catkin_cmake_args' from command line to 'None'
[0.265s] Level 5:colcon.colcon_core.verb:set package 'yocs_velocity_smoother' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.265s] DEBUG:colcon.colcon_core.verb:Building package 'yocs_velocity_smoother' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/nr_navigation/build/yocs_velocity_smoother', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_BUILD_TYPE=Release'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/nr_navigation/install/yocs_velocity_smoother', 'merge_install': False, 'path': '/home/<USER>/nr_navigation/src/yocs_velocity_smoother', 'symlink_install': False, 'test_result_base': None}
[0.265s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.265s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.266s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/nr_navigation/src/global_traj_generate' with build type 'ament_cmake'
[0.266s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/nr_navigation/src/global_traj_generate'
[0.267s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.267s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.267s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.270s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/nr_navigation/src/local_planner' with build type 'ament_cmake'
[0.270s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/nr_navigation/src/local_planner'
[0.270s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.270s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.272s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/nr_navigation/src/rrt_star_global_planner' with build type 'ament_cmake'
[0.272s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/nr_navigation/src/rrt_star_global_planner'
[0.272s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.272s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.274s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/nr_navigation/src/terrain_analysis' with build type 'ament_cmake'
[0.274s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/nr_navigation/src/terrain_analysis'
[0.274s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.274s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.279s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/global_traj_generate': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/global_traj_generate -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/global_traj_generate
[0.283s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/local_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/local_planner
[0.285s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/rrt_star_global_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/rrt_star_global_planner
[0.287s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/terrain_analysis': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/terrain_analysis -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/terrain_analysis
[2.151s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/rrt_star_global_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/rrt_star_global_planner
[2.153s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/rrt_star_global_planner -- -j16 -l16
[4.092s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/terrain_analysis -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/terrain_analysis
[4.095s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/terrain_analysis': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/terrain_analysis -- -j16 -l16
[4.136s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/local_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/local_planner
[4.137s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/local_planner -- -j16 -l16
[5.015s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/global_traj_generate -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/global_traj_generate
[5.017s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/global_traj_generate': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/global_traj_generate -- -j16 -l16
[21.783s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/terrain_analysis -- -j16 -l16
[21.795s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/terrain_analysis': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/terrain_analysis
[21.813s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis)
[21.814s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/terrain_analysis' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/terrain_analysis
[21.817s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis' for CMake module files
[21.817s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis' for CMake config files
[21.818s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis', 'cmake_prefix_path')
[21.818s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.ps1'
[21.818s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.dsv'
[21.819s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.sh'
[21.819s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis/lib'
[21.819s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis/bin'
[21.820s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis/lib/pkgconfig/terrain_analysis.pc'
[21.820s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis/lib/python3.10/site-packages'
[21.820s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis/bin'
[21.820s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.ps1'
[21.821s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.dsv'
[21.821s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.sh'
[21.822s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.bash'
[21.822s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.zsh'
[21.823s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nr_navigation/install/terrain_analysis/share/colcon-core/packages/terrain_analysis)
[21.823s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(terrain_analysis)
[21.823s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis' for CMake module files
[21.823s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis' for CMake config files
[21.823s] Level 1:colcon.colcon_core.shell:create_environment_hook('terrain_analysis', 'cmake_prefix_path')
[21.824s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.ps1'
[21.824s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.dsv'
[21.824s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/hook/cmake_prefix_path.sh'
[21.824s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis/lib'
[21.824s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis/bin'
[21.825s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis/lib/pkgconfig/terrain_analysis.pc'
[21.825s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis/lib/python3.10/site-packages'
[21.825s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/terrain_analysis/bin'
[21.825s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.ps1'
[21.825s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.dsv'
[21.826s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.sh'
[21.826s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.bash'
[21.826s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/terrain_analysis/share/terrain_analysis/package.zsh'
[21.826s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nr_navigation/install/terrain_analysis/share/colcon-core/packages/terrain_analysis)
[21.826s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/nr_navigation/src/yocs_velocity_smoother' with build type 'ament_cmake'
[21.827s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/nr_navigation/src/yocs_velocity_smoother'
[21.827s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[21.827s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[21.839s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/yocs_velocity_smoother': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/yocs_velocity_smoother -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/yocs_velocity_smoother
[23.402s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/yocs_velocity_smoother' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/yocs_velocity_smoother -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/yocs_velocity_smoother
[23.404s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/yocs_velocity_smoother': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/yocs_velocity_smoother -- -j16 -l16
[30.317s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/global_traj_generate -- -j16 -l16
[30.319s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/global_traj_generate': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/global_traj_generate
[30.384s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(global_traj_generate)
[30.384s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate' for CMake module files
[30.385s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/global_traj_generate' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/global_traj_generate
[30.385s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate' for CMake config files
[30.386s] Level 1:colcon.colcon_core.shell:create_environment_hook('global_traj_generate', 'cmake_prefix_path')
[30.386s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.ps1'
[30.386s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.dsv'
[30.386s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.sh'
[30.387s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate/lib'
[30.387s] Level 1:colcon.colcon_core.shell:create_environment_hook('global_traj_generate', 'ld_library_path_lib')
[30.387s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.ps1'
[30.387s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.dsv'
[30.387s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.sh'
[30.388s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate/bin'
[30.388s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate/lib/pkgconfig/global_traj_generate.pc'
[30.388s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate/lib/python3.10/site-packages'
[30.388s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate/bin'
[30.388s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.ps1'
[30.388s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.dsv'
[30.389s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.sh'
[30.389s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.bash'
[30.389s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.zsh'
[30.389s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nr_navigation/install/global_traj_generate/share/colcon-core/packages/global_traj_generate)
[30.390s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(global_traj_generate)
[30.390s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate' for CMake module files
[30.390s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate' for CMake config files
[30.390s] Level 1:colcon.colcon_core.shell:create_environment_hook('global_traj_generate', 'cmake_prefix_path')
[30.390s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.ps1'
[30.391s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.dsv'
[30.391s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/hook/cmake_prefix_path.sh'
[30.392s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate/lib'
[30.392s] Level 1:colcon.colcon_core.shell:create_environment_hook('global_traj_generate', 'ld_library_path_lib')
[30.392s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.ps1'
[30.392s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.dsv'
[30.392s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/hook/ld_library_path_lib.sh'
[30.393s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate/bin'
[30.393s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate/lib/pkgconfig/global_traj_generate.pc'
[30.393s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate/lib/python3.10/site-packages'
[30.393s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/global_traj_generate/bin'
[30.393s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.ps1'
[30.394s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.dsv'
[30.394s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.sh'
[30.394s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.bash'
[30.394s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/global_traj_generate/share/global_traj_generate/package.zsh'
[30.395s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nr_navigation/install/global_traj_generate/share/colcon-core/packages/global_traj_generate)
[31.823s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/rrt_star_global_planner -- -j16 -l16
[31.824s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/rrt_star_global_planner
[31.840s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rrt_star_global_planner)
[31.841s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner' for CMake module files
[31.841s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/rrt_star_global_planner
[31.842s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner' for CMake config files
[31.842s] Level 1:colcon.colcon_core.shell:create_environment_hook('rrt_star_global_planner', 'cmake_prefix_path')
[31.842s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/hook/cmake_prefix_path.ps1'
[31.843s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/hook/cmake_prefix_path.dsv'
[31.843s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/hook/cmake_prefix_path.sh'
[31.843s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner/lib'
[31.843s] Level 1:colcon.colcon_core.shell:create_environment_hook('rrt_star_global_planner', 'ld_library_path_lib')
[31.843s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/hook/ld_library_path_lib.ps1'
[31.844s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/hook/ld_library_path_lib.dsv'
[31.844s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/hook/ld_library_path_lib.sh'
[31.844s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner/bin'
[31.844s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/pkgconfig/rrt_star_global_planner.pc'
[31.844s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/python3.10/site-packages'
[31.844s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner/bin'
[31.845s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.ps1'
[31.845s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.dsv'
[31.845s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.sh'
[31.845s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.bash'
[31.846s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.zsh'
[31.846s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/colcon-core/packages/rrt_star_global_planner)
[31.846s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rrt_star_global_planner)
[31.846s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner' for CMake module files
[31.847s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner' for CMake config files
[31.847s] Level 1:colcon.colcon_core.shell:create_environment_hook('rrt_star_global_planner', 'cmake_prefix_path')
[31.847s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/hook/cmake_prefix_path.ps1'
[31.847s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/hook/cmake_prefix_path.dsv'
[31.847s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/hook/cmake_prefix_path.sh'
[31.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner/lib'
[31.848s] Level 1:colcon.colcon_core.shell:create_environment_hook('rrt_star_global_planner', 'ld_library_path_lib')
[31.848s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/hook/ld_library_path_lib.ps1'
[31.848s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/hook/ld_library_path_lib.dsv'
[31.848s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/hook/ld_library_path_lib.sh'
[31.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner/bin'
[31.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/pkgconfig/rrt_star_global_planner.pc'
[31.848s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/python3.10/site-packages'
[31.849s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/rrt_star_global_planner/bin'
[31.849s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.ps1'
[31.849s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.dsv'
[31.849s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.sh'
[31.850s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.bash'
[31.850s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.zsh'
[31.850s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nr_navigation/install/rrt_star_global_planner/share/colcon-core/packages/rrt_star_global_planner)
[33.431s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/local_planner -- -j16 -l16
[33.433s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/local_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/local_planner
[33.476s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[33.477s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner' for CMake module files
[33.477s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/local_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/local_planner
[33.478s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner' for CMake config files
[33.478s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[33.478s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[33.478s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[33.479s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[33.479s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner/lib'
[33.479s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner/bin'
[33.479s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner/lib/pkgconfig/local_planner.pc'
[33.479s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner/lib/python3.10/site-packages'
[33.479s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner/bin'
[33.480s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.ps1'
[33.480s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.dsv'
[33.480s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.sh'
[33.481s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.bash'
[33.481s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.zsh'
[33.481s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nr_navigation/install/local_planner/share/colcon-core/packages/local_planner)
[33.481s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(local_planner)
[33.481s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner' for CMake module files
[33.482s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner' for CMake config files
[33.482s] Level 1:colcon.colcon_core.shell:create_environment_hook('local_planner', 'cmake_prefix_path')
[33.482s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/hook/cmake_prefix_path.ps1'
[33.482s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/hook/cmake_prefix_path.dsv'
[33.482s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/hook/cmake_prefix_path.sh'
[33.483s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner/lib'
[33.483s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner/bin'
[33.483s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner/lib/pkgconfig/local_planner.pc'
[33.483s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner/lib/python3.10/site-packages'
[33.483s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/local_planner/bin'
[33.483s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.ps1'
[33.484s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.dsv'
[33.484s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.sh'
[33.484s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.bash'
[33.484s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/local_planner/share/local_planner/package.zsh'
[33.485s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nr_navigation/install/local_planner/share/colcon-core/packages/local_planner)
[37.725s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/yocs_velocity_smoother' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/yocs_velocity_smoother -- -j16 -l16
[37.729s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/nr_navigation/build/yocs_velocity_smoother': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/yocs_velocity_smoother
[37.754s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(yocs_velocity_smoother)
[37.755s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/nr_navigation/build/yocs_velocity_smoother' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/yocs_velocity_smoother
[37.755s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother' for CMake module files
[37.756s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother' for CMake config files
[37.756s] Level 1:colcon.colcon_core.shell:create_environment_hook('yocs_velocity_smoother', 'cmake_prefix_path')
[37.756s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/hook/cmake_prefix_path.ps1'
[37.757s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/hook/cmake_prefix_path.dsv'
[37.757s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/hook/cmake_prefix_path.sh'
[37.758s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib'
[37.758s] Level 1:colcon.colcon_core.shell:create_environment_hook('yocs_velocity_smoother', 'ld_library_path_lib')
[37.758s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/hook/ld_library_path_lib.ps1'
[37.759s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/hook/ld_library_path_lib.dsv'
[37.759s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/hook/ld_library_path_lib.sh'
[37.759s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/bin'
[37.759s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/pkgconfig/yocs_velocity_smoother.pc'
[37.759s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/python3.10/site-packages'
[37.759s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/bin'
[37.759s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.ps1'
[37.760s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.dsv'
[37.760s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.sh'
[37.760s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.bash'
[37.760s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.zsh'
[37.761s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/colcon-core/packages/yocs_velocity_smoother)
[37.761s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(yocs_velocity_smoother)
[37.761s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother' for CMake module files
[37.761s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother' for CMake config files
[37.761s] Level 1:colcon.colcon_core.shell:create_environment_hook('yocs_velocity_smoother', 'cmake_prefix_path')
[37.762s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/hook/cmake_prefix_path.ps1'
[37.762s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/hook/cmake_prefix_path.dsv'
[37.762s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/hook/cmake_prefix_path.sh'
[37.762s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib'
[37.762s] Level 1:colcon.colcon_core.shell:create_environment_hook('yocs_velocity_smoother', 'ld_library_path_lib')
[37.763s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/hook/ld_library_path_lib.ps1'
[37.763s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/hook/ld_library_path_lib.dsv'
[37.763s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/hook/ld_library_path_lib.sh'
[37.763s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/bin'
[37.763s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/pkgconfig/yocs_velocity_smoother.pc'
[37.763s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/lib/python3.10/site-packages'
[37.764s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/bin'
[37.764s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.ps1'
[37.764s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.dsv'
[37.764s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.sh'
[37.765s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.bash'
[37.765s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/yocs_velocity_smoother/package.zsh'
[37.765s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/nr_navigation/install/yocs_velocity_smoother/share/colcon-core/packages/yocs_velocity_smoother)
[37.765s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[37.765s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[37.765s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[37.766s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[37.775s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[37.775s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[37.775s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[37.804s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[37.804s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/nr_navigation/install/local_setup.ps1'
[37.805s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/nr_navigation/install/_local_setup_util_ps1.py'
[37.807s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/nr_navigation/install/setup.ps1'
[37.807s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/nr_navigation/install/local_setup.sh'
[37.808s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/nr_navigation/install/_local_setup_util_sh.py'
[37.808s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/nr_navigation/install/setup.sh'
[37.809s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/nr_navigation/install/local_setup.bash'
[37.809s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/nr_navigation/install/setup.bash'
[37.810s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/nr_navigation/install/local_setup.zsh'
[37.810s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/nr_navigation/install/setup.zsh'
