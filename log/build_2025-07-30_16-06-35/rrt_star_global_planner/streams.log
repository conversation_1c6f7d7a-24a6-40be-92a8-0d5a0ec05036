[0.013s] Invoking command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/rrt_star_global_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/rrt_star_global_planner
[0.133s] -- The C compiler identification is GNU 11.4.0
[0.252s] -- The CXX compiler identification is GNU 11.4.0
[0.263s] -- Detecting C compiler ABI info
[0.382s] -- Detecting C compiler ABI info - done
[0.390s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.390s] -- Detecting C compile features
[0.391s] -- Detecting C compile features - done
[0.394s] -- Detecting CXX compiler ABI info
[0.492s] -- Detecting CXX compiler ABI info - done
[0.497s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.497s] -- Detecting CXX compile features
[0.498s] -- Detecting CXX compile features - done
[0.499s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.632s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.706s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.740s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.744s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.751s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.761s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.774s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.809s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.810s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.971s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.999s] -- Found FastRTPS: /opt/ros/humble/include  
[1.036s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.046s] -- Looking for pthread.h
[1.151s] -- Looking for pthread.h - found
[1.151s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.251s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.252s] -- Found Threads: TRUE  
[1.300s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.317s] -- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)
[1.319s] -- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
[1.328s] -- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
[1.340s] -- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
[1.352s] -- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)
[1.369s] -- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
[1.377s] -- Found tf2_ros: 0.25.15 (/opt/ros/humble/share/tf2_ros/cmake)
[1.439s] -- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[1.450s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[1.451s] -- Found Eigen3: TRUE (found version "3.4.0") 
[1.451s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.453s] -- Found nav2_core: 1.1.18 (/opt/ros/humble/share/nav2_core/cmake)
[1.588s] -- Found nav2_common: 1.1.18 (/opt/ros/humble/share/nav2_common/cmake)
[1.702s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.772s] -- Added test 'copyright' to check source files copyright and LICENSE
[1.773s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.774s] -- Configured cppcheck include dirs: /home/<USER>/nr_navigation/src/rrt_star_global_planner/include
[1.774s] -- Configured cppcheck exclude dirs and/or files: 
[1.775s] -- Added test 'cpplint' to check C / C++ code against the Google style
[1.775s] -- Configured cpplint exclude dirs and/or files: 
[1.775s] -- Added test 'flake8' to check Python code syntax and style conventions
[1.776s] -- Added test 'lint_cmake' to check CMake code style
[1.776s] -- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
[1.777s] -- Added test 'uncrustify' to check C / C++ code style
[1.777s] -- Configured uncrustify additional arguments: 
[1.777s] -- Added test 'xmllint' to check XML markup files
[1.779s] -- Configuring done
[1.858s] -- Generating done
[1.867s] -- Build files have been written to: /home/<USER>/nr_navigation/build/rrt_star_global_planner
[1.878s] Invoked command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/rrt_star_global_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/rrt_star_global_planner
[1.881s] Invoking command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/rrt_star_global_planner -- -j16 -l16
[1.956s] [ 25%] [32mBuilding CXX object CMakeFiles/rrt_star_global_planner_lib.dir/src/rrt_star_ros.cpp.o[0m
[9.860s] [01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:[m[K In member function ‘[01m[Kvoid rrt_star_global_planner::RRTstarPlannerROS::getPathFromTree(std::vector<Node>&, std::vector<Node>&, Node&, std::vector<geometry_msgs::msg::PoseStamped_<std::allocator<void> >, std::allocator<geometry_msgs::msg::PoseStamped_<std::allocator<void> > > >&, GetPlanMode)[m[K’:
[9.861s] [01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:405:33:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcurrent_node.Node::parent_id[m[K’ may be used uninitialized in this function [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmaybe-uninitialized-Wmaybe-uninitialized]8;;[m[K]
[9.861s]   405 |   while ([01;35m[Kcurrent_node.parent_id != tree1[0].parent_id[m[K)
[9.861s]       |          [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~[m[K
[9.861s] [01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:378:8:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcurrent_node.Node::y[m[K’ may be used uninitialized in this function [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmaybe-uninitialized-Wmaybe-uninitialized]8;;[m[K]
[9.861s]   378 |   Node [01;35m[Kcurrent_node[m[K;
[9.861s]       |        [01;35m[K^~~~~~~~~~~~[m[K
[9.861s] [01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:378:8:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcurrent_node.Node::x[m[K’ may be used uninitialized in this function [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmaybe-uninitialized-Wmaybe-uninitialized]8;;[m[K]
[10.213s] [ 50%] [32m[1mLinking CXX shared library librrt_star_global_planner_lib.so[0m
[10.523s] [ 50%] Built target rrt_star_global_planner_lib
[10.562s] [ 75%] [32mBuilding CXX object CMakeFiles/rrt_star_planner.dir/src/rrt_star_plan_node.cpp.o[0m
[13.797s] [01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:[m[K In member function ‘[01m[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_configure(const rclcpp_lifecycle::State&)[m[K’:
[13.797s] [01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:358:77:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[13.797s]   358 |     RRTstarPlannerWithCostmap::on_configure([01;35m[Kconst rclcpp_lifecycle::State & state[m[K)
[13.797s]       |                                             [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~[m[K
[14.306s] [01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:[m[K In member function ‘[01m[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_activate(const rclcpp_lifecycle::State&)[m[K’:
[14.306s] [01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:450:76:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[14.307s]   450 |     RRTstarPlannerWithCostmap::on_activate([01;35m[Kconst rclcpp_lifecycle::State & state[m[K)
[14.307s]       |                                            [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~[m[K
[14.307s] [01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:[m[K In member function ‘[01m[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_deactivate(const rclcpp_lifecycle::State&)[m[K’:
[14.307s] [01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:465:78:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[14.307s]   465 |     RRTstarPlannerWithCostmap::on_deactivate([01;35m[Kconst rclcpp_lifecycle::State & state[m[K)
[14.307s]       |                                              [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~[m[K
[14.308s] [01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:[m[K In member function ‘[01m[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_cleanup(const rclcpp_lifecycle::State&)[m[K’:
[14.308s] [01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:479:75:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[14.308s]   479 |     RRTstarPlannerWithCostmap::on_cleanup([01;35m[Kconst rclcpp_lifecycle::State & state[m[K)
[14.308s]       |                                           [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~[m[K
[31.116s] [100%] [32m[1mLinking CXX executable rrt_star_planner[0m
[31.535s] [100%] Built target rrt_star_planner
[31.551s] Invoked command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/rrt_star_global_planner -- -j16 -l16
[31.552s] Invoking command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/rrt_star_global_planner
[31.562s] -- Install configuration: "Release"
[31.562s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/librrt_star_global_planner_lib.so
[31.562s] -- Set runtime path of "/home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/librrt_star_global_planner_lib.so" to ""
[31.562s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/rrt_star_global_planner/rrt_star_planner
[31.564s] -- Set runtime path of "/home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/rrt_star_global_planner/rrt_star_planner" to ""
[31.564s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/include/
[31.564s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/include//rrt_star_global_planner
[31.564s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/include//rrt_star_global_planner/rrt_star_ros.hpp
[31.564s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//launch
[31.564s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//launch/rrt_node_launch.py
[31.564s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//launch/start_planning_launch.py
[31.564s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//params
[31.564s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//params/rrt_star_planner.yaml
[31.564s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//params/test_rrt_star_planner_ros2.yaml
[31.565s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//maps
[31.565s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//rviz
[31.565s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//rviz/rrt_star_global_planner.rviz
[31.565s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/rrt_star_planner_plugin.xml
[31.565s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/library_path.sh
[31.565s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/library_path.dsv
[31.565s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/ament_index/resource_index/package_run_dependencies/rrt_star_global_planner
[31.565s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/ament_index/resource_index/parent_prefix_path/rrt_star_global_planner
[31.565s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/ament_prefix_path.sh
[31.565s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/ament_prefix_path.dsv
[31.566s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/path.sh
[31.566s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/path.dsv
[31.566s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/local_setup.bash
[31.566s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/local_setup.sh
[31.566s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/local_setup.zsh
[31.566s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/local_setup.dsv
[31.566s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.dsv
[31.566s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/ament_index/resource_index/packages/rrt_star_global_planner
[31.566s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/ament_cmake_export_include_directories-extras.cmake
[31.566s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/ament_cmake_export_libraries-extras.cmake
[31.566s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/ament_cmake_export_dependencies-extras.cmake
[31.567s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/rrt_star_global_plannerConfig.cmake
[31.567s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/rrt_star_global_plannerConfig-version.cmake
[31.567s] -- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.xml
[31.569s] Invoked command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/rrt_star_global_planner
