[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:[m[K In member function ‘[01m[Kvoid rrt_star_global_planner::RRTstarPlannerROS::getPathFromTree(std::vector<Node>&, std::vector<Node>&, Node&, std::vector<geometry_msgs::msg::PoseStamped_<std::allocator<void> >, std::allocator<geometry_msgs::msg::PoseStamped_<std::allocator<void> > > >&, GetPlanMode)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:405:33:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcurrent_node.Node::parent_id[m[K’ may be used uninitialized in this function [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmaybe-uninitialized-Wmaybe-uninitialized]8;;[m[K]
  405 |   while ([01;35m[Kcurrent_node.parent_id != tree1[0].parent_id[m[K)
      |          [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:378:8:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcurrent_node.Node::y[m[K’ may be used uninitialized in this function [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmaybe-uninitialized-Wmaybe-uninitialized]8;;[m[K]
  378 |   Node [01;35m[Kcurrent_node[m[K;
      |        [01;35m[K^~~~~~~~~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:378:8:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcurrent_node.Node::x[m[K’ may be used uninitialized in this function [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmaybe-uninitialized-Wmaybe-uninitialized]8;;[m[K]
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:[m[K In member function ‘[01m[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_configure(const rclcpp_lifecycle::State&)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:358:77:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  358 |     RRTstarPlannerWithCostmap::on_configure([01;35m[Kconst rclcpp_lifecycle::State & state[m[K)
      |                                             [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:[m[K In member function ‘[01m[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_activate(const rclcpp_lifecycle::State&)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:450:76:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  450 |     RRTstarPlannerWithCostmap::on_activate([01;35m[Kconst rclcpp_lifecycle::State & state[m[K)
      |                                            [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:[m[K In member function ‘[01m[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_deactivate(const rclcpp_lifecycle::State&)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:465:78:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  465 |     RRTstarPlannerWithCostmap::on_deactivate([01;35m[Kconst rclcpp_lifecycle::State & state[m[K)
      |                                              [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:[m[K In member function ‘[01m[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_cleanup(const rclcpp_lifecycle::State&)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:479:75:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  479 |     RRTstarPlannerWithCostmap::on_cleanup([01;35m[Kconst rclcpp_lifecycle::State & state[m[K)
      |                                           [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~[m[K
