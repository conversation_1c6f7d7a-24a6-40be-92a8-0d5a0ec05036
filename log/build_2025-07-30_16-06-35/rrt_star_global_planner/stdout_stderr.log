-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
-- Found rclpy: 3.3.17 (/opt/ros/humble/share/rclpy/cmake)
-- Found geometry_msgs: 4.9.0 (/opt/ros/humble/share/geometry_msgs/cmake)
-- Found nav_msgs: 4.9.0 (/opt/ros/humble/share/nav_msgs/cmake)
-- Found sensor_msgs: 4.9.0 (/opt/ros/humble/share/sensor_msgs/cmake)
-- Found visualization_msgs: 4.9.0 (/opt/ros/humble/share/visualization_msgs/cmake)
-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
-- Found tf2_ros: 0.25.15 (/opt/ros/humble/share/tf2_ros/cmake)
-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
-- Found Eigen3: TRUE (found version "3.4.0") 
-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
-- Found nav2_core: 1.1.18 (/opt/ros/humble/share/nav2_core/cmake)
-- Found nav2_common: 1.1.18 (/opt/ros/humble/share/nav2_common/cmake)
-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
-- Added test 'copyright' to check source files copyright and LICENSE
-- Added test 'cppcheck' to perform static code analysis on C / C++ code
-- Configured cppcheck include dirs: /home/<USER>/nr_navigation/src/rrt_star_global_planner/include
-- Configured cppcheck exclude dirs and/or files: 
-- Added test 'cpplint' to check C / C++ code against the Google style
-- Configured cpplint exclude dirs and/or files: 
-- Added test 'flake8' to check Python code syntax and style conventions
-- Added test 'lint_cmake' to check CMake code style
-- Added test 'pep257' to check Python code against some of the docstring style conventions in PEP 257
-- Added test 'uncrustify' to check C / C++ code style
-- Configured uncrustify additional arguments: 
-- Added test 'xmllint' to check XML markup files
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/nr_navigation/build/rrt_star_global_planner
[ 25%] [32mBuilding CXX object CMakeFiles/rrt_star_global_planner_lib.dir/src/rrt_star_ros.cpp.o[0m
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:[m[K In member function ‘[01m[Kvoid rrt_star_global_planner::RRTstarPlannerROS::getPathFromTree(std::vector<Node>&, std::vector<Node>&, Node&, std::vector<geometry_msgs::msg::PoseStamped_<std::allocator<void> >, std::allocator<geometry_msgs::msg::PoseStamped_<std::allocator<void> > > >&, GetPlanMode)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:405:33:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcurrent_node.Node::parent_id[m[K’ may be used uninitialized in this function [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmaybe-uninitialized-Wmaybe-uninitialized]8;;[m[K]
  405 |   while ([01;35m[Kcurrent_node.parent_id != tree1[0].parent_id[m[K)
      |          [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:378:8:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcurrent_node.Node::y[m[K’ may be used uninitialized in this function [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmaybe-uninitialized-Wmaybe-uninitialized]8;;[m[K]
  378 |   Node [01;35m[Kcurrent_node[m[K;
      |        [01;35m[K^~~~~~~~~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_ros.cpp:378:8:[m[K [01;35m[Kwarning: [m[K‘[01m[Kcurrent_node.Node::x[m[K’ may be used uninitialized in this function [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmaybe-uninitialized-Wmaybe-uninitialized]8;;[m[K]
[ 50%] [32m[1mLinking CXX shared library librrt_star_global_planner_lib.so[0m
[ 50%] Built target rrt_star_global_planner_lib
[ 75%] [32mBuilding CXX object CMakeFiles/rrt_star_planner.dir/src/rrt_star_plan_node.cpp.o[0m
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:[m[K In member function ‘[01m[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_configure(const rclcpp_lifecycle::State&)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:358:77:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  358 |     RRTstarPlannerWithCostmap::on_configure([01;35m[Kconst rclcpp_lifecycle::State & state[m[K)
      |                                             [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:[m[K In member function ‘[01m[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_activate(const rclcpp_lifecycle::State&)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:450:76:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  450 |     RRTstarPlannerWithCostmap::on_activate([01;35m[Kconst rclcpp_lifecycle::State & state[m[K)
      |                                            [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:[m[K In member function ‘[01m[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_deactivate(const rclcpp_lifecycle::State&)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:465:78:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  465 |     RRTstarPlannerWithCostmap::on_deactivate([01;35m[Kconst rclcpp_lifecycle::State & state[m[K)
      |                                              [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~[m[K
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:[m[K In member function ‘[01m[Kvirtual rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn RRTstar_planner::RRTstarPlannerWithCostmap::on_cleanup(const rclcpp_lifecycle::State&)[m[K’:
[01m[K/home/<USER>/nr_navigation/src/rrt_star_global_planner/src/rrt_star_plan_node.cpp:479:75:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  479 |     RRTstarPlannerWithCostmap::on_cleanup([01;35m[Kconst rclcpp_lifecycle::State & state[m[K)
      |                                           [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~[m[K
[100%] [32m[1mLinking CXX executable rrt_star_planner[0m
[100%] Built target rrt_star_planner
-- Install configuration: "Release"
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/librrt_star_global_planner_lib.so
-- Set runtime path of "/home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/librrt_star_global_planner_lib.so" to ""
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/rrt_star_global_planner/rrt_star_planner
-- Set runtime path of "/home/<USER>/nr_navigation/install/rrt_star_global_planner/lib/rrt_star_global_planner/rrt_star_planner" to ""
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/include/
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/include//rrt_star_global_planner
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/include//rrt_star_global_planner/rrt_star_ros.hpp
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//launch
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//launch/rrt_node_launch.py
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//launch/start_planning_launch.py
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//params
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//params/rrt_star_planner.yaml
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//params/test_rrt_star_planner_ros2.yaml
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//maps
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//rviz
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner//rviz/rrt_star_global_planner.rviz
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/rrt_star_planner_plugin.xml
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/library_path.sh
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/library_path.dsv
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/ament_index/resource_index/package_run_dependencies/rrt_star_global_planner
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/ament_index/resource_index/parent_prefix_path/rrt_star_global_planner
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/path.sh
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/environment/path.dsv
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/local_setup.bash
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/local_setup.sh
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/local_setup.zsh
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/local_setup.dsv
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.dsv
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/ament_index/resource_index/packages/rrt_star_global_planner
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/ament_cmake_export_include_directories-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/ament_cmake_export_libraries-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/ament_cmake_export_dependencies-extras.cmake
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/rrt_star_global_plannerConfig.cmake
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/cmake/rrt_star_global_plannerConfig-version.cmake
-- Installing: /home/<USER>/nr_navigation/install/rrt_star_global_planner/share/rrt_star_global_planner/package.xml
