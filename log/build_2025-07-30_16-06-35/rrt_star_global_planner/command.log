Invoking command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/rrt_star_global_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/rrt_star_global_planner
Invoked command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/nr_navigation/src/rrt_star_global_planner -DCMAKE_BUILD_TYPE=Release -DCMAKE_INSTALL_PREFIX=/home/<USER>/nr_navigation/install/rrt_star_global_planner
Invoking command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/rrt_star_global_planner -- -j16 -l16
Invoked command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/nr_navigation/build/rrt_star_global_planner -- -j16 -l16
Invoking command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/rrt_star_global_planner
Invoked command in '/home/<USER>/nr_navigation/build/rrt_star_global_planner' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/nr_navigation/build/rrt_star_global_planner
