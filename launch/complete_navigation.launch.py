#!/usr/bin/env python3

"""
Complete ROS2 Navigation System Launch File
Integrates all successfully migrated packages for autonomous robot navigation
"""

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, TimerAction, GroupAction
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node

def generate_launch_description():
    # Declare launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='Use simulation time'
    )
    
    # Core Navigation Components (all successfully migrated)
    
    # 1. Terrain Analysis - Ground segmentation and obstacle detection
    terrain_analysis_node = Node(
        package='terrain_analysis',
        executable='terrain_analysis',
        name='terrain_analysis',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'voxel_size': 0.2,
            'ground_threshold': 0.1,
            'obstacle_threshold': 0.2
        }]
    )
    
    # 2. Global Trajectory Generator - High-level path planning
    global_traj_node = Node(
        package='global_traj_generate', 
        executable='global_traj_generate_node',
        name='global_traj_generate',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'planning_timeout': 10.0,
            'goal_tolerance': 0.5
        }]
    )
    
    # 3. Local Planner - Local path planning and obstacle avoidance
    local_planner_node = Node(
        package='local_planner',
        executable='localPlanner',
        name='local_planner',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'vehicle_length': 1.2,
            'vehicle_width': 0.8,
            'adjacent_range': 4.25,
            'arrived_dis_threshold': 0.2
        }]
    )
    
    # 4. Path Follower - Low-level trajectory tracking
    path_follower_node = Node(
        package='local_planner',
        executable='pathFollower',
        name='path_follower',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'look_ahead_dis': 0.5,
            'max_speed': 0.8,
            'max_yaw_rate': 30.0,
            'max_accel': 3.0
        }]
    )
    
    # 5. Velocity Smoother - Safe motion control
    velocity_smoother_node = Node(
        package='yocs_velocity_smoother',
        executable='velocity_smoother_node', 
        name='velocity_smoother',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'speed_lim_v': 1.0,
            'speed_lim_w': 2.0,
            'accel_lim_v': 0.5,
            'accel_lim_w': 1.0,
            'decel_lim_v': 0.5,
            'decel_lim_w': 1.0
        }]
    )
    
    # Sequential startup for proper dependency management
    perception_group = GroupAction([
        terrain_analysis_node
    ])
    
    planning_group = TimerAction(
        period=2.0,
        actions=[GroupAction([
            global_traj_node,
            local_planner_node
        ])]
    )
    
    control_group = TimerAction(
        period=4.0,
        actions=[GroupAction([
            path_follower_node,
            velocity_smoother_node
        ])]
    )

    return LaunchDescription([
        use_sim_time_arg,
        perception_group,
        planning_group,
        control_group
    ])

if __name__ == '__main__':
    generate_launch_description()