#!/usr/bin/env python3

"""
ROS2 Launch file for NR_Navigation integration test
Tests the 3 successfully migrated packages
"""

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, TimerAction
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node

def generate_launch_description():
    # Declare launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='Use simulation time'
    )
    
    # Launch successfully migrated nodes
    terrain_analysis_node = Node(
        package='terrain_analysis',
        executable='terrain_analysis',
        name='terrain_analysis',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time')
        }]
    )
    
    global_traj_generate_node = Node(
        package='global_traj_generate', 
        executable='global_traj_generate_node',
        name='global_traj_generate',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time')
        }]
    )
    
    velocity_smoother_node = Node(
        package='yocs_velocity_smoother',
        executable='velocity_smoother_node', 
        name='velocity_smoother',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'speed_lim_v': 1.0,
            'speed_lim_w': 2.0,
            'accel_lim_v': 0.5,
            'accel_lim_w': 1.0
        }]
    )
    
    # Add delay between node starts
    delayed_global_traj = TimerAction(
        period=2.0,
        actions=[global_traj_generate_node]
    )
    
    delayed_velocity_smoother = TimerAction(
        period=4.0, 
        actions=[velocity_smoother_node]
    )

    return LaunchDescription([
        use_sim_time_arg,
        terrain_analysis_node,
        delayed_global_traj,
        delayed_velocity_smoother
    ])

if __name__ == '__main__':
    generate_launch_description()