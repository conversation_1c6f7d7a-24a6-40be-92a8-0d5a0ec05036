#!/usr/bin/env python3

"""
ROS2 Launch file for NR_Navigation system
Replaces the original start_planning.launch
"""

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node


def generate_launch_description():
    # Declare launch arguments
    map_file_arg = DeclareLaunchArgument(
        'map_file',
        default_value='',
        description='Path to the map file (for indoor/outdoor specific maps)'
    )
    
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='Use simulation time'
    )
    
    # Get package directories
    try:
        rrt_star_dir = get_package_share_directory('rrt_star_global_planner')
        global_traj_dir = get_package_share_directory('global_traj_generate')
        terrain_dir = get_package_share_directory('terrain_analysis')
        local_planner_dir = get_package_share_directory('local_planner')
        velocity_smoother_dir = get_package_share_directory('yocs_velocity_smoother')
    except Exception as e:
        print(f"Error: Package not found. Make sure all packages are built: {e}")
        return LaunchDescription()

    # Include launch files from each package
    launch_files = []
    
    # Global path planner
    try:
        rrt_star_launch = IncludeLaunchDescription(
            PythonLaunchDescriptionSource([rrt_star_dir, '/launch/rrt_node_launch.py']),
            launch_arguments={
                'map_file': LaunchConfiguration('map_file'),
                'use_sim_time': LaunchConfiguration('use_sim_time')
            }.items()
        )
        launch_files.append(rrt_star_launch)
    except:
        print("Warning: RRT* global planner launch file not found")
    
    # Global trajectory generator
    try:
        global_traj_launch = IncludeLaunchDescription(
            PythonLaunchDescriptionSource([global_traj_dir, '/launch/global_traj_generate_launch.py']),
            launch_arguments={
                'use_sim_time': LaunchConfiguration('use_sim_time')
            }.items()
        )
        launch_files.append(global_traj_launch)
    except:
        print("Warning: Global trajectory generator launch file not found")
    
    # Terrain analysis
    try:
        terrain_launch = IncludeLaunchDescription(
            PythonLaunchDescriptionSource([terrain_dir, '/launch/terrain_analysis_launch.py']),
            launch_arguments={
                'use_sim_time': LaunchConfiguration('use_sim_time')
            }.items()
        )
        launch_files.append(terrain_launch)
    except:
        print("Warning: Terrain analysis launch file not found")
    
    # Local planner
    try:
        local_planner_launch = IncludeLaunchDescription(
            PythonLaunchDescriptionSource([local_planner_dir, '/launch/local_planner_launch.py']),
            launch_arguments={
                'use_sim_time': LaunchConfiguration('use_sim_time')
            }.items()
        )
        launch_files.append(local_planner_launch)
    except:
        print("Warning: Local planner launch file not found")
    
    # Velocity smoother
    try:
        velocity_smoother_launch = IncludeLaunchDescription(
            PythonLaunchDescriptionSource([velocity_smoother_dir, '/launch/standalone_launch.py']),
            launch_arguments={
                'use_sim_time': LaunchConfiguration('use_sim_time')
            }.items()
        )
        launch_files.append(velocity_smoother_launch)
    except:
        print("Warning: Velocity smoother launch file not found")

    return LaunchDescription([
        map_file_arg,
        use_sim_time_arg,
        *launch_files
    ])


if __name__ == '__main__':
    generate_launch_description()