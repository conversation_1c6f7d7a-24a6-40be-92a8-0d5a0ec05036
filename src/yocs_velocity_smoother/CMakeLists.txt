cmake_minimum_required(VERSION 3.8)
project(yocs_velocity_smoother)

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_components REQUIRED)
find_package(pluginlib REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(rcl_interfaces REQUIRED)
find_package(std_msgs REQUIRED)

include_directories(include)

# Create ament index resource which references the libraries in the binary dir
set(node_plugins "")

# Velocity Smoother Component
add_library(velocity_smoother_component SHARED
  src/velocity_smoother_component.cpp)

target_compile_definitions(velocity_smoother_component PRIVATE "VELOCITY_SMOOTHER_BUILDING_DLL")

ament_target_dependencies(velocity_smoother_component
  rclcpp
  rclcpp_components
  geometry_msgs
  nav_msgs
  rcl_interfaces
  std_msgs
)

rclcpp_components_register_nodes(velocity_smoother_component "yocs_velocity_smoother::VelocitySmootherComponent")
set(node_plugins "${node_plugins}yocs_velocity_smoother::VelocitySmootherComponent;$<TARGET_FILE:velocity_smoother_component>\n")

# Standalone executable
add_executable(velocity_smoother_node src/velocity_smoother_node.cpp)

ament_target_dependencies(velocity_smoother_node
  rclcpp
  rclcpp_components
)

target_link_libraries(velocity_smoother_node velocity_smoother_component)

# Install
install(TARGETS velocity_smoother_component
  EXPORT velocity_smoother_component
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

install(TARGETS velocity_smoother_node
  DESTINATION lib/${PROJECT_NAME}
)

install(DIRECTORY include/
  DESTINATION include/
)

install(DIRECTORY launch
  DESTINATION share/${PROJECT_NAME}/
)

install(DIRECTORY param
  DESTINATION share/${PROJECT_NAME}/
)

# Install component plugin description file
install(FILES plugins/velocity_smoother_component.xml
  DESTINATION share/${PROJECT_NAME}/plugins/
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

# Export old-style CMake variables
ament_export_include_directories(include)
ament_export_libraries(velocity_smoother_component)
ament_export_dependencies(
  rclcpp
  rclcpp_components
  geometry_msgs
  nav_msgs
  rcl_interfaces
  std_msgs
)

# Export modern CMake targets
ament_export_targets(velocity_smoother_component HAS_LIBRARY_TARGET)

ament_package()
