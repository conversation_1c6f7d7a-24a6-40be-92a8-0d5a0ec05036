#include "yocs_velocity_smoother/velocity_smoother_component.hpp"

#include <algorithm>
#include <cmath>
#include <functional>

namespace yocs_velocity_smoother
{

VelocitySmootherComponent::VelocitySmootherComponent(const rclcpp::NodeOptions & options)
: Node("velocity_smoother", options),
  shutdown_req_(false),
  input_active_(false),
  cb_avg_time_(0.1),
  pr_next_(0)
{
  // Initialize parameters
  initializeParameters();
  
  // Set up parameter callback
  parameter_callback_handle_ = this->add_on_set_parameters_callback(
    std::bind(&VelocitySmootherComponent::parametersCallback, this, std::placeholders::_1));

  // Initialize publishers and subscribers
  smooth_vel_pub_ = this->create_publisher<geometry_msgs::msg::Twist>("smooth_cmd_vel", 1);
  
  raw_in_vel_sub_ = this->create_subscription<geometry_msgs::msg::Twist>(
    "raw_cmd_vel", 1, std::bind(&VelocitySmootherComponent::velocityCB, this, std::placeholders::_1));
  
  odometry_sub_ = this->create_subscription<nav_msgs::msg::Odometry>(
    "odometry", 1, std::bind(&VelocitySmootherComponent::odometryCB, this, std::placeholders::_1));
  
  current_vel_sub_ = this->create_subscription<geometry_msgs::msg::Twist>(
    "robot_cmd_vel", 1, std::bind(&VelocitySmootherComponent::robotVelCB, this, std::placeholders::_1));

  // Initialize state
  last_cmd_vel_.linear.x = 0.0;
  last_cmd_vel_.angular.z = 0.0;
  current_vel_.linear.x = 0.0;
  current_vel_.angular.z = 0.0;
  target_vel_.linear.x = 0.0;
  target_vel_.angular.z = 0.0;
  
  last_cb_time_ = this->get_clock()->now();
  period_record_.reserve(PERIOD_RECORD_SIZE);

  // Start worker thread
  worker_thread_ = std::thread(&VelocitySmootherComponent::spin, this);
  
  RCLCPP_INFO(this->get_logger(), "Velocity Smoother component initialized");
}

VelocitySmootherComponent::~VelocitySmootherComponent()
{
  RCLCPP_DEBUG(this->get_logger(), "Velocity Smoother: waiting for worker thread to finish...");
  shutdown_req_ = true;
  if (worker_thread_.joinable()) {
    worker_thread_.join();
  }
}

void VelocitySmootherComponent::initializeParameters()
{
  // Declare parameters with default values
  this->declare_parameter("speed_lim_v", 1.0);
  this->declare_parameter("speed_lim_w", 5.0);
  this->declare_parameter("accel_lim_v", 0.5);
  this->declare_parameter("accel_lim_w", 2.5);
  this->declare_parameter("decel_factor", DEFAULT_DECEL_FACTOR);
  this->declare_parameter("frequency", DEFAULT_FREQUENCY);
  this->declare_parameter("quiet", false);
  this->declare_parameter("robot_feedback", static_cast<int>(NONE));

  // Get parameter values
  speed_lim_v_ = this->get_parameter("speed_lim_v").as_double();
  speed_lim_w_ = this->get_parameter("speed_lim_w").as_double();
  accel_lim_v_ = this->get_parameter("accel_lim_v").as_double();
  accel_lim_w_ = this->get_parameter("accel_lim_w").as_double();
  decel_factor_ = this->get_parameter("decel_factor").as_double();
  frequency_ = this->get_parameter("frequency").as_double();
  quiet_ = this->get_parameter("quiet").as_bool();
  
  int feedback = this->get_parameter("robot_feedback").as_int();
  if (feedback < NONE || feedback > COMMANDS) {
    RCLCPP_WARN(this->get_logger(), 
      "Invalid robot feedback type (%d). Valid options are 0 (NONE), 1 (ODOMETRY), 2 (COMMANDS)", 
      feedback);
    feedback = NONE;
  }
  robot_feedback_ = static_cast<RobotFeedbackType>(feedback);

  // Calculate deceleration limits
  decel_lim_v_ = decel_factor_ * accel_lim_v_;
  decel_lim_w_ = decel_factor_ * accel_lim_w_;

  RCLCPP_INFO(this->get_logger(), 
    "Parameters: speed_lim_v=%.2f, speed_lim_w=%.2f, accel_lim_v=%.2f, accel_lim_w=%.2f, decel_factor=%.2f",
    speed_lim_v_, speed_lim_w_, accel_lim_v_, accel_lim_w_, decel_factor_);
}

rcl_interfaces::msg::SetParametersResult VelocitySmootherComponent::parametersCallback(
  const std::vector<rclcpp::Parameter> & parameters)
{
  rcl_interfaces::msg::SetParametersResult result;
  result.successful = true;
  
  std::lock_guard<std::mutex> lock(mutex_);
  
  for (const auto & param : parameters) {
    if (param.get_name() == "speed_lim_v") {
      speed_lim_v_ = param.as_double();
    } else if (param.get_name() == "speed_lim_w") {
      speed_lim_w_ = param.as_double();
    } else if (param.get_name() == "accel_lim_v") {
      accel_lim_v_ = param.as_double();
      decel_lim_v_ = decel_factor_ * accel_lim_v_;
    } else if (param.get_name() == "accel_lim_w") {
      accel_lim_w_ = param.as_double();
      decel_lim_w_ = decel_factor_ * accel_lim_w_;
    } else if (param.get_name() == "decel_factor") {
      decel_factor_ = param.as_double();
      decel_lim_v_ = decel_factor_ * accel_lim_v_;
      decel_lim_w_ = decel_factor_ * accel_lim_w_;
    } else if (param.get_name() == "frequency") {
      frequency_ = param.as_double();
    } else if (param.get_name() == "quiet") {
      quiet_ = param.as_bool();
    } else if (param.get_name() == "robot_feedback") {
      int feedback = param.as_int();
      if (feedback >= NONE && feedback <= COMMANDS) {
        robot_feedback_ = static_cast<RobotFeedbackType>(feedback);
      } else {
        result.successful = false;
        result.reason = "Invalid robot feedback type";
        return result;
      }
    }
  }
  
  RCLCPP_INFO(this->get_logger(), 
    "Parameters updated: speed_lim_v=%.2f, speed_lim_w=%.2f, accel_lim_v=%.2f, accel_lim_w=%.2f, decel_factor=%.2f",
    speed_lim_v_, speed_lim_w_, accel_lim_v_, accel_lim_w_, decel_factor_);
  
  return result;
}

void VelocitySmootherComponent::velocityCB(const geometry_msgs::msg::Twist::SharedPtr msg)
{
  // Estimate commands frequency
  auto now = this->get_clock()->now();
  if (period_record_.size() < PERIOD_RECORD_SIZE) {
    period_record_.push_back((now - last_cb_time_).seconds());
  } else {
    period_record_[pr_next_] = (now - last_cb_time_).seconds();
  }

  pr_next_++;
  pr_next_ %= period_record_.size();
  last_cb_time_ = now;

  if (period_record_.size() <= PERIOD_RECORD_SIZE/2) {
    // Wait until we have some values; make a reasonable assumption (10 Hz) meanwhile
    cb_avg_time_ = 0.1;
  } else {
    // Enough; recalculate with the latest input
    cb_avg_time_ = median(period_record_);
  }

  input_active_ = true;

  // Bound speed with the maximum values
  std::lock_guard<std::mutex> lock(mutex_);
  target_vel_.linear.x = msg->linear.x > 0.0 ? 
    std::min(msg->linear.x, speed_lim_v_) : std::max(msg->linear.x, -speed_lim_v_);
  target_vel_.angular.z = msg->angular.z > 0.0 ? 
    std::min(msg->angular.z, speed_lim_w_) : std::max(msg->angular.z, -speed_lim_w_);
}

void VelocitySmootherComponent::odometryCB(const nav_msgs::msg::Odometry::SharedPtr msg)
{
  if (robot_feedback_ == ODOMETRY) {
    current_vel_ = msg->twist.twist;
  }
}

void VelocitySmootherComponent::robotVelCB(const geometry_msgs::msg::Twist::SharedPtr msg)
{
  if (robot_feedback_ == COMMANDS) {
    current_vel_ = *msg;
  }
}

void VelocitySmootherComponent::spin()
{
  double period = 1.0 / frequency_;
  auto loop_rate = std::chrono::duration<double>(period);
  auto next_time = std::chrono::steady_clock::now() + loop_rate;

  while (!shutdown_req_ && rclcpp::ok()) {
    std::unique_lock<std::mutex> lock(mutex_);
    double accel_lim_v = accel_lim_v_;
    double accel_lim_w = accel_lim_w_;
    double decel_lim_v = decel_lim_v_;
    double decel_lim_w = decel_lim_w_;
    lock.unlock();

    auto now = this->get_clock()->now();
    
    // Check if input is still active
    if (input_active_ && cb_avg_time_ > 0.0 &&
        (now - last_cb_time_).seconds() > std::min(3.0 * cb_avg_time_, 0.5)) {
      input_active_ = false;
      if (target_vel_.linear.x != 0.0 || target_vel_.angular.z != 0.0) {
        RCLCPP_WARN(this->get_logger(),
          "Velocity input got inactive leaving us a non-zero target velocity (%.2f, %.2f), zeroing...",
          target_vel_.linear.x, target_vel_.angular.z);
        target_vel_.linear.x = 0.0;
        target_vel_.angular.z = 0.0;
      }
    }

    // Check feedback deviation
    double period_buffer = 2.0;
    double v_deviation_lower = last_cmd_vel_.linear.x - decel_lim_v * period * period_buffer;
    double v_deviation_upper = last_cmd_vel_.linear.x + accel_lim_v * period * period_buffer;
    double w_deviation_lower = last_cmd_vel_.angular.z - decel_lim_w * period * period_buffer;
    double w_deviation_upper = last_cmd_vel_.angular.z + accel_lim_w * period * period_buffer;

    bool v_different = current_vel_.linear.x < v_deviation_lower || 
                      current_vel_.linear.x > v_deviation_upper;
    bool w_different = current_vel_.angular.z < w_deviation_lower || 
                      current_vel_.angular.z > w_deviation_upper;

    if (robot_feedback_ != NONE && input_active_ && cb_avg_time_ > 0.0 &&
        ((now - last_cb_time_).seconds() > 5.0 * cb_avg_time_ || v_different || w_different)) {
      if (!quiet_) {
        RCLCPP_WARN(this->get_logger(),
          "Using robot velocity feedback instead of last command: time_diff=%.3f, v_diff=%.3f, w_diff=%.3f",
          (now - last_cb_time_).seconds(),
          current_vel_.linear.x - last_cmd_vel_.linear.x,
          current_vel_.angular.z - last_cmd_vel_.angular.z);
      }
      last_cmd_vel_ = current_vel_;
    }

    // Generate smooth command
    if (target_vel_.linear.x != last_cmd_vel_.linear.x ||
        target_vel_.angular.z != last_cmd_vel_.angular.z) {
      
      auto cmd_vel = std::make_shared<geometry_msgs::msg::Twist>(target_vel_);
      
      double v_inc = target_vel_.linear.x - last_cmd_vel_.linear.x;
      double w_inc = target_vel_.angular.z - last_cmd_vel_.angular.z;
      
      double max_v_inc, max_w_inc;
      
      // Calculate maximum velocity increments
      if (robot_feedback_ == ODOMETRY && current_vel_.linear.x * target_vel_.linear.x < 0.0) {
        max_v_inc = decel_lim_v * period;
      } else {
        max_v_inc = ((v_inc * target_vel_.linear.x > 0.0) ? accel_lim_v : decel_lim_v) * period;
      }
      
      if (robot_feedback_ == ODOMETRY && current_vel_.angular.z * target_vel_.angular.z < 0.0) {
        max_w_inc = decel_lim_w * period;
      } else {
        max_w_inc = ((w_inc * target_vel_.angular.z > 0.0) ? accel_lim_w : decel_lim_w) * period;
      }

      // Apply constraints
      if (std::abs(v_inc) > 0.0 && std::abs(w_inc) > 0.0) {
        // Both linear and angular velocity changes requested
        double MA = std::sqrt(v_inc * v_inc + w_inc * w_inc);
        double MB = std::sqrt(max_v_inc * max_v_inc + max_w_inc * max_w_inc);
        
        if (MA > MB) {
          double Av = std::abs(v_inc) / MA;
          double Aw = std::abs(w_inc) / MA;
          double Bv = max_v_inc / MB;
          double Bw = max_w_inc / MB;
          double theta = std::atan2(Bw, Bv) - std::atan2(Aw, Av);
          
          if (theta < 0) {
            max_v_inc = (max_w_inc * std::abs(v_inc)) / std::abs(w_inc);
          } else {
            max_w_inc = (max_v_inc * std::abs(w_inc)) / std::abs(v_inc);
          }
        }
      }

      // Apply limits
      if (std::abs(v_inc) > max_v_inc) {
        cmd_vel->linear.x = last_cmd_vel_.linear.x + sign(v_inc) * max_v_inc;
      }
      
      if (std::abs(w_inc) > max_w_inc) {
        cmd_vel->angular.z = last_cmd_vel_.angular.z + sign(w_inc) * max_w_inc;
      }
      
      smooth_vel_pub_->publish(*cmd_vel);
      last_cmd_vel_ = *cmd_vel;
    } else if (input_active_) {
      // Keep resending last command while input is active
      auto cmd_vel = std::make_shared<geometry_msgs::msg::Twist>(last_cmd_vel_);
      smooth_vel_pub_->publish(*cmd_vel);
    }

    // Sleep until next iteration
    std::this_thread::sleep_until(next_time);
    next_time += loop_rate;
  }
}

}  // namespace yocs_velocity_smoother

#include "rclcpp_components/register_node_macro.hpp"
RCLCPP_COMPONENTS_REGISTER_NODE(yocs_velocity_smoother::VelocitySmootherComponent)