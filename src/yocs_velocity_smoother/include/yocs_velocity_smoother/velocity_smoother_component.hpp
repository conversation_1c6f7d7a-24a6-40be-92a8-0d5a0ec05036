/**
 * @file /include/yocs_velocity_smoother/velocity_smoother_component.hpp
 *
 * @brief Velocity smoother ROS2 component interface
 *
 * License: BSD
 *   https://raw.github.com/yujinrobot/yujin_ocs/hydro/yocs_velocity_smoother/LICENSE
 **/

#ifndef YOCS_VELOCITY_SMOOTHER__VELOCITY_SMOOTHER_COMPONENT_HPP_
#define YOCS_VELOCITY_SMOOTHER__VELOCITY_SMOOTHER_COMPONENT_HPP_

#include <memory>
#include <string>
#include <vector>
#include <mutex>
#include <thread>
#include <chrono>

#include <rclcpp/rclcpp.hpp>
#include <rclcpp_components/register_node_macro.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <rcl_interfaces/msg/parameter_descriptor.hpp>
#include <rcl_interfaces/msg/set_parameters_result.hpp>

namespace yocs_velocity_smoother
{

class VelocitySmootherComponent : public rclcpp::Node
{
public:
  explicit VelocitySmootherComponent(const rclcpp::NodeOptions & options);
  ~VelocitySmootherComponent();

private:
  enum RobotFeedbackType
  {
    NONE = 0,
    ODOMETRY = 1,
    COMMANDS = 2
  };

  // Parameters
  double speed_lim_v_;
  double speed_lim_w_;
  double accel_lim_v_;
  double accel_lim_w_;
  double decel_lim_v_;
  double decel_lim_w_;
  double decel_factor_;
  double frequency_;
  bool quiet_;
  RobotFeedbackType robot_feedback_;

  // State variables
  geometry_msgs::msg::Twist last_cmd_vel_;
  geometry_msgs::msg::Twist current_vel_;
  geometry_msgs::msg::Twist target_vel_;
  
  bool shutdown_req_;
  bool input_active_;
  double cb_avg_time_;
  rclcpp::Time last_cb_time_;
  std::vector<double> period_record_;
  unsigned int pr_next_;

  // Threading
  std::mutex mutex_;
  std::thread worker_thread_;

  // ROS2 interfaces
  rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr raw_in_vel_sub_;
  rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr odometry_sub_;
  rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr current_vel_sub_;
  rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr smooth_vel_pub_;

  // Parameter callback
  rclcpp::node_interfaces::OnSetParametersCallbackHandle::SharedPtr parameter_callback_handle_;

  // Callback functions
  void velocityCB(const geometry_msgs::msg::Twist::SharedPtr msg);
  void robotVelCB(const geometry_msgs::msg::Twist::SharedPtr msg);
  void odometryCB(const nav_msgs::msg::Odometry::SharedPtr msg);
  
  // Parameter callback
  rcl_interfaces::msg::SetParametersResult parametersCallback(
    const std::vector<rclcpp::Parameter> & parameters);

  // Worker thread function
  void spin();

  // Utility functions
  double sign(double x) { return x < 0.0 ? -1.0 : +1.0; }
  
  double median(std::vector<double> values) {
    if (values.empty()) return 0.0;
    std::nth_element(values.begin(), values.begin() + values.size()/2, values.end());
    return values[values.size()/2];
  }

  // Initialize parameters
  void initializeParameters();
  
  // Constants
  static constexpr size_t PERIOD_RECORD_SIZE = 5;
  static constexpr double DEFAULT_FREQUENCY = 20.0;
  static constexpr double DEFAULT_DECEL_FACTOR = 1.0;
};

}  // namespace yocs_velocity_smoother

#endif  // YOCS_VELOCITY_SMOOTHER__VELOCITY_SMOOTHER_COMPONENT_HPP_