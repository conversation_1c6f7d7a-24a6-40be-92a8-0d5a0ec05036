# Example configuration:
# - velocity limits are around a 10% above the physical limits
# - acceleration limits are just low enough to avoid jerking

# Mandatory parameters
speed_lim_v: 1.0
speed_lim_w: 0.8

accel_lim_v: 1.5
accel_lim_w: 0.75

# Optional parameters
frequency: 50.0
decel_factor: 20.0

# Robot velocity feedback type:
#  0 - none
#  1 - odometry
#  2 - end robot commands
robot_feedback: 0
