# ROS2 parameters for velocity smoother
velocity_smoother:
  ros__parameters:
    # Mandatory parameters
    speed_lim_v: 1.0      # Maximum linear velocity (m/s)
    speed_lim_w: 0.8      # Maximum angular velocity (rad/s)
    
    accel_lim_v: 1.5      # Maximum linear acceleration (m/s²)
    accel_lim_w: 0.75     # Maximum angular acceleration (rad/s²)
    
    # Optional parameters
    frequency: 50.0       # Control loop frequency (Hz)
    decel_factor: 20.0    # Deceleration factor (multiplier for acceleration limits)
    
    # Robot velocity feedback type:
    #  0 - none (default)
    #  1 - odometry
    #  2 - end robot commands
    robot_feedback: 0
    
    # Suppress warning messages about velocity multiplexing
    quiet: false