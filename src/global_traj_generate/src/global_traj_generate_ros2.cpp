#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/string.hpp>
#include <tf2_ros/transform_broadcaster.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/range_image/range_image.h>
#include <pcl/filters/filter.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/common/common.h>
#include <pcl/registration/icp.h>

#include <Eigen/Dense>

#include <sensor_msgs/msg/point_cloud2.hpp>
#include <sensor_msgs/msg/imu.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <geometry_msgs/msg/pose2_d.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/quaternion.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <nav_msgs/msg/path.hpp>

#include "global_traj_generate/msg/navigation_result.hpp"
#include "global_traj_generate/msg/navigation_target.hpp"

using namespace std;
typedef pcl::PointXYZ PointType;
constexpr double PI = 3.14159265;

class GlobalTrajGenerate : public rclcpp::Node
{
public:
    GlobalTrajGenerate() : Node("global_traj_generate")
    {
        // Initialize subscribers
        sub_laser_odometry_ = this->create_subscription<nav_msgs::msg::Odometry>(
            "/Odometry", 10, std::bind(&GlobalTrajGenerate::laserOdometryCallback, this, std::placeholders::_1));
        
        sub_goal_pose_ = this->create_subscription<geometry_msgs::msg::PoseStamped>(
            "/move_base_simple/goal", 10, std::bind(&GlobalTrajGenerate::goalPoseCallback, this, std::placeholders::_1));
        
        sub_web_goal_pose_ = this->create_subscription<global_traj_generate::msg::NavigationTarget>(
            "/navi_goal", 10, std::bind(&GlobalTrajGenerate::webGoalPoseCallback, this, std::placeholders::_1));
        
        sub_global_path_ = this->create_subscription<nav_msgs::msg::Path>(
            "/global_path", 10, std::bind(&GlobalTrajGenerate::globalPathCallback, this, std::placeholders::_1));

        // Initialize publishers
        pub_local_goal_ = this->create_publisher<global_traj_generate::msg::NavigationResult>("/navi_result", 10);

        // Initialize parameters
        this->declare_parameter("lookahead_distance", 2.0);
        this->declare_parameter("goal_tolerance", 0.5);
        
        lookahead_distance_ = this->get_parameter("lookahead_distance").as_double();
        goal_tolerance_ = this->get_parameter("goal_tolerance").as_double();

        RCLCPP_INFO(this->get_logger(), "Global trajectory generator initialized");
    }

private:
    void laserOdometryCallback(const nav_msgs::msg::Odometry::SharedPtr msg)
    {
        current_odom_ = *msg;
        // Process odometry data
        processOdometry();
    }

    void goalPoseCallback(const geometry_msgs::msg::PoseStamped::SharedPtr msg)
    {
        goal_pose_ = *msg;
        RCLCPP_INFO(this->get_logger(), "Received goal pose: [%.2f, %.2f]", 
                   msg->pose.position.x, msg->pose.position.y);
    }

    void webGoalPoseCallback(const global_traj_generate::msg::NavigationTarget::SharedPtr msg)
    {
        navigation_target_ = *msg;
        RCLCPP_INFO(this->get_logger(), "Received navigation target: point_id=%d, pose=[%.2f, %.2f]", 
                   msg->point_id, msg->pose_x, msg->pose_y);
        
        // Convert to goal pose
        goal_pose_.header.stamp = this->get_clock()->now();
        goal_pose_.header.frame_id = "map";
        goal_pose_.pose.position.x = msg->pose_x;
        goal_pose_.pose.position.y = msg->pose_y;
        goal_pose_.pose.position.z = msg->pose_z;
        
        // Convert yaw to quaternion
        tf2::Quaternion quat;
        quat.setRPY(0, 0, msg->yaw);
        goal_pose_.pose.orientation = tf2::toMsg(quat);
    }

    void globalPathCallback(const nav_msgs::msg::Path::SharedPtr msg)
    {
        global_path_ = *msg;
        RCLCPP_INFO(this->get_logger(), "Received global path with %zu points", msg->poses.size());
        
        // Process the path and generate local goals
        processGlobalPath();
    }

    void processOdometry()
    {
        // Check if we have reached the goal
        if (hasGoal())
        {
            double distance_to_goal = calculateDistanceToGoal();
            
            if (distance_to_goal < goal_tolerance_)
            {
                publishNavigationResult(0); // Success
                RCLCPP_INFO(this->get_logger(), "Goal reached!");
            }
        }
    }

    void processGlobalPath()
    {
        if (global_path_.poses.empty()) return;
        
        // Find the closest point on the path
        int closest_idx = findClosestPointOnPath();
        
        // Find lookahead point
        int lookahead_idx = findLookaheadPoint(closest_idx);
        
        if (lookahead_idx >= 0)
        {
            // Publish local goal
            publishLocalGoal(lookahead_idx);
        }
    }

    int findClosestPointOnPath()
    {
        if (global_path_.poses.empty()) return -1;
        
        double min_distance = std::numeric_limits<double>::max();
        int closest_idx = 0;
        
        for (size_t i = 0; i < global_path_.poses.size(); ++i)
        {
            double dx = global_path_.poses[i].pose.position.x - current_odom_.pose.pose.position.x;
            double dy = global_path_.poses[i].pose.position.y - current_odom_.pose.pose.position.y;
            double distance = sqrt(dx * dx + dy * dy);
            
            if (distance < min_distance)
            {
                min_distance = distance;
                closest_idx = i;
            }
        }
        
        return closest_idx;
    }

    int findLookaheadPoint(int start_idx)
    {
        if (global_path_.poses.empty()) return -1;
        
        for (size_t i = start_idx; i < global_path_.poses.size(); ++i)
        {
            double dx = global_path_.poses[i].pose.position.x - current_odom_.pose.pose.position.x;
            double dy = global_path_.poses[i].pose.position.y - current_odom_.pose.pose.position.y;
            double distance = sqrt(dx * dx + dy * dy);
            
            if (distance >= lookahead_distance_)
            {
                return i;
            }
        }
        
        return global_path_.poses.size() - 1; // Return last point if no lookahead found
    }

    void publishLocalGoal(int path_idx)
    {
        if (path_idx < 0 || path_idx >= static_cast<int>(global_path_.poses.size())) return;
        
        auto result = global_traj_generate::msg::NavigationResult();
        result.point_id = navigation_target_.point_id;
        result.target_pose_x = global_path_.poses[path_idx].pose.position.x;
        result.target_pose_y = global_path_.poses[path_idx].pose.position.y;
        result.target_pose_z = global_path_.poses[path_idx].pose.position.z;
        
        // Extract yaw from quaternion
        tf2::Quaternion quat;
        tf2::fromMsg(global_path_.poses[path_idx].pose.orientation, quat);
        double roll, pitch, yaw;
        tf2::Matrix3x3(quat).getRPY(roll, pitch, yaw);
        result.target_yaw = yaw;
        
        result.current_pose_x = current_odom_.pose.pose.position.x;
        result.current_pose_y = current_odom_.pose.pose.position.y;
        result.current_pose_z = current_odom_.pose.pose.position.z;
        
        tf2::fromMsg(current_odom_.pose.pose.orientation, quat);
        tf2::Matrix3x3(quat).getRPY(roll, pitch, yaw);
        result.current_yaw = yaw;
        
        result.nav_state = 1; // In progress
        
        pub_local_goal_->publish(result);
    }

    void publishNavigationResult(int state)
    {
        auto result = global_traj_generate::msg::NavigationResult();
        result.point_id = navigation_target_.point_id;
        result.target_pose_x = goal_pose_.pose.position.x;
        result.target_pose_y = goal_pose_.pose.position.y;
        result.target_pose_z = goal_pose_.pose.position.z;
        
        tf2::Quaternion quat;
        tf2::fromMsg(goal_pose_.pose.orientation, quat);
        double roll, pitch, yaw;
        tf2::Matrix3x3(quat).getRPY(roll, pitch, yaw);
        result.target_yaw = yaw;
        
        result.current_pose_x = current_odom_.pose.pose.position.x;
        result.current_pose_y = current_odom_.pose.pose.position.y;
        result.current_pose_z = current_odom_.pose.pose.position.z;
        
        tf2::fromMsg(current_odom_.pose.pose.orientation, quat);
        tf2::Matrix3x3(quat).getRPY(roll, pitch, yaw);
        result.current_yaw = yaw;
        
        result.nav_state = state;
        
        pub_local_goal_->publish(result);
    }

    bool hasGoal()
    {
        return !goal_pose_.header.frame_id.empty();
    }

    double calculateDistanceToGoal()
    {
        if (!hasGoal()) return std::numeric_limits<double>::max();
        
        double dx = goal_pose_.pose.position.x - current_odom_.pose.pose.position.x;
        double dy = goal_pose_.pose.position.y - current_odom_.pose.pose.position.y;
        return sqrt(dx * dx + dy * dy);
    }

    // Subscribers
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr sub_laser_odometry_;
    rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr sub_goal_pose_;
    rclcpp::Subscription<global_traj_generate::msg::NavigationTarget>::SharedPtr sub_web_goal_pose_;
    rclcpp::Subscription<nav_msgs::msg::Path>::SharedPtr sub_global_path_;
    
    // Publishers
    rclcpp::Publisher<global_traj_generate::msg::NavigationResult>::SharedPtr pub_local_goal_;
    
    // State variables
    nav_msgs::msg::Odometry current_odom_;
    geometry_msgs::msg::PoseStamped goal_pose_;
    global_traj_generate::msg::NavigationTarget navigation_target_;
    nav_msgs::msg::Path global_path_;
    
    // Parameters
    double lookahead_distance_;
    double goal_tolerance_;
};

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<GlobalTrajGenerate>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}