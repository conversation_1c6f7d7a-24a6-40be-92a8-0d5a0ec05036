#include <rclcpp/rclcpp.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <Eigen/Dense>
#include <Eigen/Geometry>
// #include <dynamic_detector/dynamic_detector.hpp>  // Temporarily disabled for compilation

using namespace std;

class TerrainAnalysisNode : public rclcpp::Node
{
public:
    TerrainAnalysisNode() : Node("terrain_analysis")
    {
        // Declare parameters
        this->declare_parameter("cloud_topic", std::string("/cloud_registered"));
        this->declare_parameter("odom_topic", std::string("/Odometry"));
        this->declare_parameter("terrain_cloud_topic", std::string("/terrain_map"));
        this->declare_parameter("resolution", 0.1);
        this->declare_parameter("log_odds_increase", 0.4);
        this->declare_parameter("log_odds_decrease", 0.2);
        this->declare_parameter("beam_num", 2048);
        this->declare_parameter("width", 40.0);
        this->declare_parameter("updatetime", 1.0);
        this->declare_parameter("occupancy_threshold", 0.7);
        this->declare_parameter("vehicle_length", 1.0);
        this->declare_parameter("vehicle_width", 0.5);
        this->declare_parameter("z_max", 1.0);
        this->declare_parameter("z_min", -1.0);

        // Get parameters
        cloud_topic_ = this->get_parameter("cloud_topic").as_string();
        odom_topic_ = this->get_parameter("odom_topic").as_string();
        terrain_cloud_topic_ = this->get_parameter("terrain_cloud_topic").as_string();
        resolution_ = this->get_parameter("resolution").as_double();
        log_odds_increase_ = this->get_parameter("log_odds_increase").as_double();
        log_odds_decrease_ = this->get_parameter("log_odds_decrease").as_double();
        beam_num_ = this->get_parameter("beam_num").as_int();
        width_ = this->get_parameter("width").as_double();
        updatetime_ = this->get_parameter("updatetime").as_double();
        occupancy_threshold_ = this->get_parameter("occupancy_threshold").as_double();
        vehicle_length_ = this->get_parameter("vehicle_length").as_double();
        vehicle_width_ = this->get_parameter("vehicle_width").as_double();
        z_max_ = this->get_parameter("z_max").as_double();
        z_min_ = this->get_parameter("z_min").as_double();

        // Initialize subscribers and publishers
        sub_cloud_ = this->create_subscription<sensor_msgs::msg::PointCloud2>(
            cloud_topic_, 1, std::bind(&TerrainAnalysisNode::callbackCloud, this, std::placeholders::_1));
        
        sub_odom_ = this->create_subscription<nav_msgs::msg::Odometry>(
            odom_topic_, 1, std::bind(&TerrainAnalysisNode::callbackOdom, this, std::placeholders::_1));
        
        sub_pose_ = this->create_subscription<geometry_msgs::msg::PoseStamped>(
            "/gazebo_state/pumbaa_pose", 100, std::bind(&TerrainAnalysisNode::callbackPose, this, std::placeholders::_1));

        pub_terrain_cloud_ = this->create_publisher<sensor_msgs::msg::PointCloud2>(terrain_cloud_topic_, 1);
        pub_odom_ = this->create_publisher<nav_msgs::msg::Odometry>("/state_estimation", 1);

        // Initialize odometry message
        odometry_ = std::make_shared<nav_msgs::msg::Odometry>();

        // Initialize dynamic detector (temporarily disabled)
        // dynamic_.initialization(width_, resolution_, occupancy_threshold_, beam_num_, log_odds_increase_, 
        //                        log_odds_decrease_, updatetime_, vehicle_length_, vehicle_width_, z_max_, z_min_);
        
        RCLCPP_INFO(this->get_logger(), "Terrain Analysis Node initialized successfully!");
    }

private:
    void callbackCloud(const sensor_msgs::msg::PointCloud2::SharedPtr cloud_msg)
    {
        auto terrain_cloud = std::make_shared<sensor_msgs::msg::PointCloud2>();
        // dynamic_.process(cloud_msg, odometry_, terrain_cloud);
        pub_terrain_cloud_->publish(*terrain_cloud);
        
        odometry_->header.frame_id = "map";
        odometry_->header.stamp = this->get_clock()->now();
        pub_odom_->publish(*odometry_);
    }

    void callbackOdom(const nav_msgs::msg::Odometry::SharedPtr odom_msg)
    {
        *odometry_ = *odom_msg;
        
        Eigen::Vector3d t;
        t << odom_msg->pose.pose.position.x, odom_msg->pose.pose.position.y, odom_msg->pose.pose.position.z;
        
        Eigen::Quaterniond t_Q;
        t_Q.x() = odom_msg->pose.pose.orientation.x;
        t_Q.y() = odom_msg->pose.pose.orientation.y;
        t_Q.z() = odom_msg->pose.pose.orientation.z;
        t_Q.w() = odom_msg->pose.pose.orientation.w;
        
        lidar_k2init_ = (Eigen::Translation3d(t.cast<double>()) * 
                        Eigen::AngleAxisd((t_Q.toRotationMatrix()).cast<double>()));
    }

    void callbackPose(const geometry_msgs::msg::PoseStamped::SharedPtr pose)
    {
        odometry_->header = pose->header;
        odometry_->pose.pose = pose->pose;
        odom_buffer_.push_back(*odometry_);
    }

    // Parameters
    std::string cloud_topic_;
    std::string odom_topic_;
    std::string terrain_cloud_topic_;
    double width_, resolution_, occupancy_threshold_, log_odds_increase_, log_odds_decrease_;
    double updatetime_, vehicle_length_, vehicle_width_, z_max_, z_min_;
    int beam_num_;

    // ROS2 interfaces
    rclcpp::Subscription<sensor_msgs::msg::PointCloud2>::SharedPtr sub_cloud_;
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr sub_odom_;
    rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr sub_pose_;
    rclcpp::Publisher<sensor_msgs::msg::PointCloud2>::SharedPtr pub_terrain_cloud_;
    rclcpp::Publisher<nav_msgs::msg::Odometry>::SharedPtr pub_odom_;

    // State variables
    nav_msgs::msg::Odometry::SharedPtr odometry_;
    Eigen::Affine3d lidar_k2init_;
    std::vector<nav_msgs::msg::Odometry> odom_buffer_;
    
    // Dynamic detector (would need actual implementation)
    // DynamicCloudDetector dynamic_;
};

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<TerrainAnalysisNode>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}