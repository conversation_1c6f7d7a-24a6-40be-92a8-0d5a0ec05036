rrt_star_planner_node:
  ros__parameters:
    RRTstarPlannerROS:
      search_radius: 2.0      # 搜索附近的节点的搜索范围
      goal_radius: 0.2        # 认为搜索到目标点的范围
      epsilon_min: 0.001      # 节点之间的最小允许距离
      epsilon_max: 0.1        # 节点之间的最大允许距离
      max_nodes_num: 2000000000.0  # 节点数的最大值，最大迭代次数
      plan_time_out: 3.0      # 规划超时，默认10s

global_costmap:
  global_costmap:
    ros__parameters:
      global_frame: map
      robot_base_frame: base_footprint
      update_frequency: 1.0
      publish_frequency: 1.0
      rolling_window: false
      resolution: 0.1
      transform_tolerance: 10.0
      track_unknown_space: true
      robot_radius: 0.3       # origin1.0
      
      # Map size configuration
      width: 1000
      height: 1000
      origin_x: -50.0
      origin_y: -50.0
      
      plugins: ["static_layer", "obstacle_layer", "inflation_layer"]
      
      static_layer:
        plugin: "nav2_costmap_2d::StaticLayer"
        enabled: true
        lethal_cost_threshold: 100
        map_topic: map
        first_map_only: false
        track_unknown_space: true
        trinary_costmap: true
      
      obstacle_layer:
        plugin: "nav2_costmap_2d::ObstacleLayer"
        enabled: true
        # These parameters apply to all sensors.
        obstacle_range: 5.0
        max_obstacle_height: 1.5  # assume something like an arm is mounted on top of the robot
        raytrace_range: 10.0
        
        # These parameters are used by the ObstacleCostmapPlugin.
        track_unknown_space: true        # true needed for disabling global path planning through unknown space
        footprint_clearing_enabled: true
        combination_method: 1
        
        # The following parameters are used by the VoxelCostmapPlugin.
        origin_z: 0.0
        z_resolution: 1.0
        z_voxels: 2
        unknown_threshold: 15
        mark_threshold: 0
        publish_voxel_map: false
        footprint_clearing_enabled: true
        
        observation_sources: ["scan_matched", "scan"]
        scan_matched:
          topic: /scan_matched_points2
          data_type: "PointCloud2"
          marking: true
          clearing: true
          observation_persistence: 0.0
          expected_update_rate: 0.0
          min_obstacle_height: 0.0
          max_obstacle_height: 1.5
          obstacle_range: 3.0
          raytrace_range: 10.0
        
        scan:
          topic: scan
          data_type: "LaserScan"
          marking: true
          clearing: true
          observation_persistence: 0.0
          expected_update_rate: 0.0
          min_obstacle_height: 0.0
          max_obstacle_height: 1.5
          obstacle_range: 3.0
          raytrace_range: 10.0
          inf_is_valid: true
      
      inflation_layer:
        plugin: "nav2_costmap_2d::InflationLayer"
        enabled: true
        cost_scaling_factor: 1.0  # exponential rate at which the obstacle cost drops off
        inflation_radius: 0.5

planner_server:
  ros__parameters:
    use_sim_time: false
    planner_plugins: ["GridBased"]
    GridBased:
      plugin: "nav2_navfn_planner/NavfnPlanner"
      allow_unknown: false                           # Allow planner to plan through unknown space
      default_tolerance: 0.5                        # If goal in obstacle, plan to the closest point in radius default_tolerance
      visualize_potential: false
      old_navfn_behavior: false                     # Exactly mirror behavior of navfn
      use_quadratic: true                           # Use the quadratic approximation of the potential
      use_dijkstra: true                            # Use dijkstra's algorithm. Otherwise, A*
      use_grid_path: false                          # Create a path that follows the grid boundaries
      
      # Needs to have track_unknown_space: true in the obstacle / voxel layer to work
      planner_window_x: 0.0
      planner_window_y: 0.0
      
      publish_scale: 100                            # Scale by which the published potential gets multiplied
      
      lethal_cost: 253
      neutral_cost: 50
      cost_factor: 3.0                              # Factor to multiply each cost from costmap by
      publish_potential: true                       # 是否发布地图中的搜索过的可行域

# 算法版本号：
rrt_star_planner:
  ros__parameters:
    navigation_alg_version: "v1.0.0_20250311_01"