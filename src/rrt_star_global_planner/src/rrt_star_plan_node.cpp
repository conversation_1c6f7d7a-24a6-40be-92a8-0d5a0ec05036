#include <rrt_star_global_planner/rrt_star_ros.hpp>
#include <nav2_costmap_2d/costmap_2d_ros.hpp>
#include <tf2_ros/transform_listener.h>
#include <rclcpp_lifecycle/lifecycle_node.hpp>
#include <iostream>
#include <nav_msgs/msg/path.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <std_msgs/msg/int8.hpp>
#include <geometry_msgs/msg/pose_with_covariance_stamped.hpp>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <tf2/utils.h>
// #include <rrt_star_global_planner/msg/navigation_result.hpp>
// #include <rrt_star_global_planner/msg/navigation_target.hpp>
#include <vector>
#include <std_msgs/msg/bool.hpp>
#include <geometry_msgs/msg/pose_array.hpp>

std_msgs::msg::Int8 safetystop;

namespace RRTstar_planner
{
    using namespace std;
    geometry_msgs::msg::PoseStamped robot_pose;
    geometry_msgs::msg::PoseStamped goal;
    
    //定义RRT规划的类
    class RRTstarPlannerWithCostmap : public rclcpp_lifecycle::LifecycleNode
    {
        public:
            RRTstarPlannerWithCostmap();
            
            // Lifecycle interface methods
            rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
            on_configure(const rclcpp_lifecycle::State & state) override;
            
            rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
            on_activate(const rclcpp_lifecycle::State & state) override;
            
            rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
            on_deactivate(const rclcpp_lifecycle::State & state) override;
            
            rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
            on_cleanup(const rclcpp_lifecycle::State & state) override;
            
        private:
            void poseCallback(const geometry_msgs::msg::PoseStamped::SharedPtr target);
            void webposeCallback(const geometry_msgs::msg::PoseStamped::SharedPtr target);    
            void odometryCallback(const nav_msgs::msg::Odometry::SharedPtr odom);
            void replanCallback(const std_msgs::msg::Int8::SharedPtr replan);
            void testposeCallback(const geometry_msgs::msg::PoseArray::SharedPtr target);
            
            // ROS2 components
            std::shared_ptr<nav2_costmap_2d::Costmap2DROS> costmap_ros_;
            std::shared_ptr<tf2_ros::Buffer> tf_buffer_;
            std::shared_ptr<tf2_ros::TransformListener> tf_listener_;
            std::unique_ptr<rrt_star_global_planner::RRTstarPlannerROS> planner_;
            
            // Subscribers
            rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr pose_sub_;
            rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr goal_pose_sub_;
            rclcpp::Subscription<geometry_msgs::msg::PoseArray>::SharedPtr test_pose_sub_;
            rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr odometry_sub_;
            rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr replan_sub_;
            
            // Publishers
            rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr goal_pub_;
            rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr goal_success_pub_;
            rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr inner_pub_stop_;
            rclcpp::Publisher<std_msgs::msg::Bool>::SharedPtr accessable_pub_;
            
            // Helper method to call planner
            bool makePlan(const geometry_msgs::msg::PoseStamped& start, 
                         const geometry_msgs::msg::PoseStamped& goal, 
                         std::vector<geometry_msgs::msg::PoseStamped>& path);
    };
    
    //目标点 from web's callback
    void RRTstarPlannerWithCostmap::webposeCallback(const geometry_msgs::msg::PoseStamped::SharedPtr target)                                         
    {
        //获取目标点位姿
        goal.pose.position.x = target->pose.position.x;
        goal.pose.position.y = target->pose.position.y;
        
        RCLCPP_INFO(this->get_logger(), "web target yaw: %f, unit:deg.", target->pose.orientation.z);
        
        // Convert from Euler angle to quaternion
        tf2::Quaternion q;
        q.setRPY(0, 0, target->pose.orientation.z);
        goal.pose.orientation = tf2::toMsg(q);

        goal_pub_->publish(goal);
        std::vector<geometry_msgs::msg::PoseStamped> path;
        unsigned int mx = 0, my = 0;
        
        //三维坐标投影到二维栅格地图时报错
        if(!costmap_ros_->getCostmap()->worldToMap(goal.pose.position.x, goal.pose.position.y, mx, my))
        {
            RCLCPP_ERROR(this->get_logger(), "worldToMap error");
            std::cout << "rrt_star_plan_node worldToMap error" << std::endl;
            safetystop.data = 1;  
            inner_pub_stop_->publish(safetystop);
            return;
        }
        
        //检查目标点的代价值 (webposeCallback)
        auto cost = costmap_ros_->getCostmap()->getCost(mx, my);
        RCLCPP_INFO(this->get_logger(), "Web goal cost: %d at (%d, %d)", cost, mx, my);
        
        const unsigned char MAX_ACCEPTABLE_COST = 250;
        if(cost > MAX_ACCEPTABLE_COST)
        {
            RCLCPP_INFO(this->get_logger(), "The web target point cost too high (%d)", cost);
            std::cout << "rrt_star_plan_node The target point is unreachable (cost too high)." << std::endl;
            safetystop.data = 1;  
            inner_pub_stop_->publish(safetystop);
            return;
        }
        else if(cost > nav2_costmap_2d::FREE_SPACE)
        {
            RCLCPP_INFO(this->get_logger(), "Web target has elevated cost (%d), allowing planning", cost);
        }
        
        std_msgs::msg::Int8 goalsuccess;
        safetystop.data = 0;  
        inner_pub_stop_->publish(safetystop);
        goalsuccess.data = 5;
        goal_success_pub_->publish(goalsuccess);
        std::cout<<"接受web导航目标成功，正在规划全局路径"<<std::endl;
        
        //进行RRT全局规划
        makePlan(robot_pose, goal, path);
    }
    
    void RRTstarPlannerWithCostmap::testposeCallback(const geometry_msgs::msg::PoseArray::SharedPtr target)                                         
    {
        std_msgs::msg::Bool msg;
        geometry_msgs::msg::PoseStamped start_point;
        geometry_msgs::msg::PoseStamped target_point;
        
        //获取目标点位姿
        if(target->poses.size() >= 2)
        {
            start_point.pose.position.x = target->poses[0].position.x;
            start_point.pose.position.y = target->poses[0].position.y;
            start_point.pose.orientation.x = 1;
            start_point.pose.orientation.y = 1;
            start_point.pose.orientation.z = 1;
            start_point.pose.orientation.w = 1;
            
            target_point.pose.position.x = target->poses[1].position.x;
            target_point.pose.position.y = target->poses[1].position.y;
            target_point.pose.orientation.x = 1;
            target_point.pose.orientation.y = 1;
            target_point.pose.orientation.z = 1;
            target_point.pose.orientation.w = 1;
        }
        
        std::vector<geometry_msgs::msg::PoseStamped> path;
        unsigned int msx = 0, msy = 0, mtx = 0, mty = 0;
        
        //三维坐标投影到二维栅格地图时报错
        if(!costmap_ros_->getCostmap()->worldToMap(start_point.pose.position.x, start_point.pose.position.y, msx, msy) && 
           !costmap_ros_->getCostmap()->worldToMap(target_point.pose.position.x, target_point.pose.position.y, mtx, mty))
        {
            std::cout << "worldToMap error" << std::endl;
            msg.data = false;
            accessable_pub_->publish(msg);
            std::cout << "test accessable failed." << std::endl;
            return;
        }
        
   
        if(costmap_ros_->getCostmap()->getCost(msx,msy) != nav2_costmap_2d::FREE_SPACE && costmap_ros_->getCostmap()->getCost(mtx,mty) != nav2_costmap_2d::FREE_SPACE)
        {
            std::cout << "point is unreachable." << std::endl;
            msg.data = false;
            accessable_pub_->publish(msg);
            std::cout << "test accessable failed." << std::endl;
            return;
        }

        std_msgs::msg::Int8 goalsuccess;
        goalsuccess.data = 5;
        goal_success_pub_->publish(goalsuccess);
        
        //进行RRT全局规划
        makePlan(start_point, target_point, path);
    }

    //目标点信息回调函数
    void RRTstarPlannerWithCostmap::poseCallback(const geometry_msgs::msg::PoseStamped::SharedPtr target)                                         
    {
        //获取目标点位姿
        goal.pose.position.x = target->pose.position.x;
        goal.pose.position.y = target->pose.position.y;
        goal.pose.orientation = target->pose.orientation;
        
        goal_pub_->publish(goal);
        std::vector<geometry_msgs::msg::PoseStamped> path;
        unsigned int mx = 0, my = 0;
        
        //三维坐标投影到二维栅格地图时报错
        if(!costmap_ros_->getCostmap()->worldToMap(goal.pose.position.x, goal.pose.position.y, mx, my))
        {

            std::cout << "worldToMap error: Goal point outside costmap bounds" << std::endl;
            safetystop.data = 1;  
            inner_pub_stop_->publish(safetystop);
            return;
        }
        
        if(costmap_ros_->getCostmap()->getCost(mx, my) >= nav2_costmap_2d::INSCRIBED_INFLATED_OBSTACLE)
        {
            std::cout << " The target point is unreachable." << std::endl;
            safetystop.data = 1;  
            inner_pub_stop_->publish(safetystop);
            return;
        }
           

        std_msgs::msg::Int8 goalsuccess;
        safetystop.data = 0;  
        inner_pub_stop_->publish(safetystop);
        goalsuccess.data = 5;
        goal_success_pub_->publish(goalsuccess);
        std::cout<<"接受手动导航目标成功，正在规划全局路径"<<std::endl;
        
        //进行RRT全局规划
        makePlan(robot_pose, goal, path);
    }
    
    //载体当前位姿信息回调函数
    void RRTstarPlannerWithCostmap::odometryCallback(const nav_msgs::msg::Odometry::SharedPtr odom)
    {
        robot_pose.header.stamp = odom->header.stamp;
        robot_pose.header.frame_id = "map";
        robot_pose.pose.position.x = odom->pose.pose.position.x;
        robot_pose.pose.position.y = odom->pose.pose.position.y;
        robot_pose.pose.position.z = odom->pose.pose.position.z;
        robot_pose.pose.orientation.x = odom->pose.pose.orientation.x;
        robot_pose.pose.orientation.y = odom->pose.pose.orientation.y;
        robot_pose.pose.orientation.z = odom->pose.pose.orientation.z;
        robot_pose.pose.orientation.w = odom->pose.pose.orientation.w;
    }
    
    void RRTstarPlannerWithCostmap::replanCallback(const std_msgs::msg::Int8::SharedPtr replan)
    {
        if (replan->data == 1)
        {
            std::cout << "收到重规划请求，开始检查..." << std::endl;

            // 检查数据有效性
            if (goal.pose.position.x == 0.0 && goal.pose.position.y == 0.0)
            {
                std::cout << "重规划失败: 目标点未设置或为原点" << std::endl;
                return;
            }

            if (robot_pose.pose.position.x == 0.0 && robot_pose.pose.position.y == 0.0)
            {
                std::cout << "重规划失败: 机器人位置未初始化或为原点" << std::endl;
                return;
            }

            std::vector<geometry_msgs::msg::PoseStamped> path;
            unsigned int mx = 0, my = 0;
            unsigned int rx = 0, ry = 0;

            // 检查目标点坐标转换
            if(!costmap_ros_->getCostmap()->worldToMap(goal.pose.position.x, goal.pose.position.y, mx, my))
            {
                std::cout << "重规划失败: 目标点坐标转换错误 (" << goal.pose.position.x << ", " << goal.pose.position.y << ")" << std::endl;
                safetystop.data = 1;
                inner_pub_stop_->publish(safetystop);
                return;
            }

            // 检查机器人位置坐标转换
            if(!costmap_ros_->getCostmap()->worldToMap(robot_pose.pose.position.x, robot_pose.pose.position.y, rx, ry))
            {
                std::cout << "重规划失败: 机器人位置坐标转换错误 (" << robot_pose.pose.position.x << ", " << robot_pose.pose.position.y << ")" << std::endl;
                safetystop.data = 1;
                inner_pub_stop_->publish(safetystop);
                return;
            }

            // 检查目标点代价值
            auto goal_cost = costmap_ros_->getCostmap()->getCost(mx, my);
            const unsigned char MAX_ACCEPTABLE_COST = 250;
            if(goal_cost > MAX_ACCEPTABLE_COST)
            {
                std::cout << "重规划失败: 目标点代价过高，代价值=" << (int)goal_cost << std::endl;
                safetystop.data = 1;
                inner_pub_stop_->publish(safetystop);
                return;
            }

            // 检查机器人位置代价值
            auto robot_cost = costmap_ros_->getCostmap()->getCost(rx, ry);
            if(robot_cost > MAX_ACCEPTABLE_COST)
            {
                std::cout << "重规划失败: 机器人位置代价过高，代价值=" << (int)robot_cost << std::endl;
                safetystop.data = 1;
                inner_pub_stop_->publish(safetystop);
                return;
            }
            
            std::cout << "重规划检查通过 - 目标点代价值=" << (int)goal_cost << ", 机器人位置代价值=" << (int)robot_cost << std::endl;

            std::cout << "重规划中... 起点:(" << robot_pose.pose.position.x << ", " << robot_pose.pose.position.y
                      << ") 终点:(" << goal.pose.position.x << ", " << goal.pose.position.y << ")" << std::endl;

            // 进行RRT全局规划并检查结果
            bool planSuccess = makePlan(robot_pose, goal, path);

            if (planSuccess && !path.empty())
            {
                std::cout << "重规划成功！生成路径点数: " << path.size() << std::endl;
                safetystop.data = 0;
                inner_pub_stop_->publish(safetystop);

                // 发布规划成功状态
                std_msgs::msg::Int8 goalsuccess;
                goalsuccess.data = 5;
                goal_success_pub_->publish(goalsuccess);
            }
            else
            {
                std::cout << "重规划失败: RRT*算法无法找到有效路径" << std::endl;
                if (path.empty())
                {
                    std::cout << "  原因: 生成的路径为空" << std::endl;
                }
                safetystop.data = 1;
                inner_pub_stop_->publish(safetystop);
            }
        }
        else
        {
            // 收到停止重规划信号
            static int stop_count = 0;
            stop_count++;
            if (stop_count % 1000 == 0)  // 每1000次输出一次，避免刷屏
            {
                std::cout << "收到停止重规划信号，计数: " << stop_count << std::endl;
            }
        }
    }
    
    //RRT规划节点初始化
    RRTstarPlannerWithCostmap::RRTstarPlannerWithCostmap() : LifecycleNode("rrt_star_planner")
    {
        RCLCPP_INFO(this->get_logger(), "RRT* planner node constructed");
    }
    
    rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
    RRTstarPlannerWithCostmap::on_configure(const rclcpp_lifecycle::State & state)
    {
        RCLCPP_INFO(this->get_logger(), "Configuring RRT* planner node");
        
        // Initialize TF2
        tf_buffer_ = std::make_shared<tf2_ros::Buffer>(this->get_clock());
        tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);
        
        // Initialize costmap
        costmap_ros_ = std::make_shared<nav2_costmap_2d::Costmap2DROS>("global_costmap");
        costmap_ros_->configure();
        
        // Wait for costmap to be ready
        auto start_time = this->get_clock()->now();
        while (!costmap_ros_->getCostmap() && (this->get_clock()->now() - start_time).seconds() < 10.0) {
            RCLCPP_INFO(this->get_logger(), "Waiting for costmap to be ready...");
            rclcpp::sleep_for(std::chrono::milliseconds(100));
        }
        
        if (!costmap_ros_->getCostmap()) {
            RCLCPP_ERROR(this->get_logger(), "Failed to initialize costmap within timeout");
            return rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn::FAILURE;
        }
        
        RCLCPP_INFO(this->get_logger(), "Costmap initialized successfully");
        
        // Wait for map to be available
        auto map_available = false;
        start_time = this->get_clock()->now();
        while (!map_available && (this->get_clock()->now() - start_time).seconds() < 10.0) {
            auto costmap = costmap_ros_->getCostmap();
            if (costmap && costmap->getSizeInCellsX() > 0 && costmap->getSizeInCellsY() > 0) {
                map_available = true;
                RCLCPP_INFO(this->get_logger(), "Map is available: %dx%d cells, origin: (%.2f, %.2f)", 
                           costmap->getSizeInCellsX(), costmap->getSizeInCellsY(), 
                           costmap->getOriginX(), costmap->getOriginY());
            } else {
                RCLCPP_INFO(this->get_logger(), "Waiting for map data...");
                rclcpp::sleep_for(std::chrono::milliseconds(500));
            }
        }
        
        if (!map_available) {
            RCLCPP_WARN(this->get_logger(), "Map data not available, but continuing with configuration");
        }
        
        // Initialize planner
        planner_ = std::make_unique<rrt_star_global_planner::RRTstarPlannerROS>();
        planner_->configure(this->weak_from_this(), "RRTstarPlannerROS", tf_buffer_, costmap_ros_);
        
        //订阅导航目标点及导航参数信息
        pose_sub_ = this->create_subscription<geometry_msgs::msg::PoseStamped>(
            "/goal_pose", 1, 
            std::bind(&RRTstarPlannerWithCostmap::poseCallback, this, std::placeholders::_1));
            
        //订阅云端目标点
        goal_pose_sub_ = this->create_subscription<geometry_msgs::msg::PoseStamped>(
            "/web_goal_pose", 1,
            std::bind(&RRTstarPlannerWithCostmap::webposeCallback, this, std::placeholders::_1));
            
        //订阅测试点
        test_pose_sub_ = this->create_subscription<geometry_msgs::msg::PoseArray>(
            "/test_poses", 1,
            std::bind(&RRTstarPlannerWithCostmap::testposeCallback, this, std::placeholders::_1));
            
        //订阅载体当前位姿信息
        odometry_sub_ = this->create_subscription<nav_msgs::msg::Odometry>(
            "/Odometry", 1,
            std::bind(&RRTstarPlannerWithCostmap::odometryCallback, this, std::placeholders::_1));
            
        //订阅重规划信息
        replan_sub_ = this->create_subscription<std_msgs::msg::Int8>(
            "/replan", 1,
            std::bind(&RRTstarPlannerWithCostmap::replanCallback, this, std::placeholders::_1));
            
        //发布导航目标点在rviz中的显示信息
        goal_pub_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("/target_goal", 1);
        
        //发布可达性测试结果
        accessable_pub_ = this->create_publisher<std_msgs::msg::Bool>("/ifAccessable", 1);
        
        //发布目标点下发成功信息
        goal_success_pub_ = this->create_publisher<std_msgs::msg::Int8>("/goal_success", 1);
        
        //发布停止信号
        inner_pub_stop_ = this->create_publisher<std_msgs::msg::Int8>("/istop", 1);
        
        RCLCPP_INFO(this->get_logger(), "RRT* planner node configured successfully");
        return rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn::SUCCESS;
    }
    
    rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
    RRTstarPlannerWithCostmap::on_activate(const rclcpp_lifecycle::State & state)
    {
        RCLCPP_INFO(this->get_logger(), "Activating RRT* planner node");
        
        // Activate costmap
        costmap_ros_->activate();
        
        // Activate planner
        planner_->activate();
        
        RCLCPP_INFO(this->get_logger(), "RRT* planner node activated successfully");
        return rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn::SUCCESS;
    }
    
    rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
    RRTstarPlannerWithCostmap::on_deactivate(const rclcpp_lifecycle::State & state)
    {
        RCLCPP_INFO(this->get_logger(), "Deactivating RRT* planner node");
        
        // Deactivate planner
        planner_->deactivate();
        
        // Deactivate costmap
        costmap_ros_->deactivate();
        
        return rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn::SUCCESS;
    }
    
    rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn
    RRTstarPlannerWithCostmap::on_cleanup(const rclcpp_lifecycle::State & state)
    {
        RCLCPP_INFO(this->get_logger(), "Cleaning up RRT* planner node");
        
        // Clean up planner
        planner_->cleanup();
        planner_.reset();
        
        // Clean up costmap
        costmap_ros_->cleanup();
        costmap_ros_.reset();
        
        // Clean up TF
        tf_listener_.reset();
        tf_buffer_.reset();
        
        // Reset subscribers and publishers
        pose_sub_.reset();
        goal_pose_sub_.reset();
        test_pose_sub_.reset();
        odometry_sub_.reset();
        replan_sub_.reset();
        goal_pub_.reset();
        accessable_pub_.reset();
        goal_success_pub_.reset();
        inner_pub_stop_.reset();
        
        return rclcpp_lifecycle::node_interfaces::LifecycleNodeInterface::CallbackReturn::SUCCESS;
    }
    
    // Helper method to call planner
    bool RRTstarPlannerWithCostmap::makePlan(const geometry_msgs::msg::PoseStamped& start, 
                                            const geometry_msgs::msg::PoseStamped& goal, 
                                            std::vector<geometry_msgs::msg::PoseStamped>& path)
    {
        try {
            nav_msgs::msg::Path nav_path = planner_->createPlan(start, goal);
            path = nav_path.poses;
            return !path.empty();
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Plan generation failed: %s", e.what());
            return false;
        }
    }
}

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<RRTstar_planner::RRTstarPlannerWithCostmap>();
    
    // Configure and activate the lifecycle node
    node->configure();
    node->activate();
    
    rclcpp::spin(node->get_node_base_interface());
    rclcpp::shutdown();
    return 0;
}