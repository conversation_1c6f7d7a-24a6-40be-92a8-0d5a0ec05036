#include <rrt_star_global_planner/rrt_star_ros.hpp>
#include <pluginlib/class_list_macros.hpp>
#include <iostream>
#include <tf2/utils.h>

PLUGINLIB_EXPORT_CLASS(rrt_star_global_planner::RRTstarPlannerROS, nav2_core::GlobalPlanner)

namespace rrt_star_global_planner
{

RRTstarPlannerROS::RRTstarPlannerROS()
: costmap_(nullptr), configured_(false), initialized_(false)
{
}

void RRTstarPlannerROS::configure(
  const rclcpp_lifecycle::LifecycleNode::WeakPtr & parent,
  std::string name, std::shared_ptr<tf2_ros::Buffer> tf,
  std::shared_ptr<nav2_costmap_2d::Costmap2DROS> costmap_ros)
{
  node_ = parent;
  name_ = name;
  tf_ = tf;
  costmap_ros_ = costmap_ros;
  costmap_ = costmap_ros_->getCostmap();
  global_frame_ = costmap_ros_->getGlobalFrameID();

  auto node = node_.lock();
  
  // Create publishers
  plan_pub_ = node->create_publisher<nav_msgs::msg::Path>(name + "/plan", 1);
  marker_pub_ = node->create_publisher<visualization_msgs::msg::Marker>(name + "/visualization_marker", 1);
  accessable_pub_ = node->create_publisher<std_msgs::msg::Bool>("/ifAccessable", 1);

  resolution_ = costmap_->getResolution();

  // Declare and get parameters
  node->declare_parameter(name + ".search_radius", 1.0);
  node->declare_parameter(name + ".goal_radius", 0.2);
  node->declare_parameter(name + ".epsilon_min", 0.001);
  node->declare_parameter(name + ".epsilon_max", 0.1);
  node->declare_parameter(name + ".max_nodes_num", 2000000000.0);
  node->declare_parameter(name + ".plan_time_out", 1.0);

  search_radius_ = node->get_parameter(name + ".search_radius").as_double();
  goal_radius_ = node->get_parameter(name + ".goal_radius").as_double();
  epsilon_min_ = node->get_parameter(name + ".epsilon_min").as_double();
  epsilon_max_ = node->get_parameter(name + ".epsilon_max").as_double();
  double max_nodes_num_tem = node->get_parameter(name + ".max_nodes_num").as_double();
  plan_time_out_ = node->get_parameter(name + ".plan_time_out").as_double();
  max_nodes_num_ = static_cast<size_t>(max_nodes_num_tem);

  // Path optimization parameters
  path_point_spacing_ = 0.25;
  angle_difference_ = M_PI/20;

  RCLCPP_INFO(node->get_logger(), "RRT* planner configured successfully");
  configured_ = true;
}

void RRTstarPlannerROS::cleanup()
{
  plan_pub_.reset();
  marker_pub_.reset();
  accessable_pub_.reset();
  configured_ = false;
}

void RRTstarPlannerROS::activate()
{
  RCLCPP_INFO(node_.lock()->get_logger(), "RRT* planner activated");
}

void RRTstarPlannerROS::deactivate()
{
  RCLCPP_INFO(node_.lock()->get_logger(), "RRT* planner deactivated");
}

double RRTstarPlannerROS::normalizeAngle(double val, double min, double max)
{
  double norm = 0.0;
  if (val >= min)
    norm = min + fmod((val - min), (max-min));
  else
    norm = max - fmod((min - val), (max-min));
  return norm;
}

void RRTstarPlannerROS::pubTreeMarker(rclcpp::Publisher<visualization_msgs::msg::Marker>::SharedPtr& marker_pub, 
                                      visualization_msgs::msg::Marker marker, int id)
{
  auto node = node_.lock();
  marker.header.frame_id = "map";
  marker.header.stamp = node->get_clock()->now();
  marker.ns = "marker_namespace";
  marker.id = id;
  marker.type = visualization_msgs::msg::Marker::LINE_LIST;
  marker.action = visualization_msgs::msg::Marker::ADD;
  marker.pose.position.x = 0;
  marker.pose.position.y = 0;
  marker.pose.position.z = 0;
  marker.pose.orientation.x = 0.0;
  marker.pose.orientation.y = 0.0;
  marker.pose.orientation.z = 0.0;
  marker.pose.orientation.w = 1.0;
  marker.scale.x = 0.01;
  marker.color.a = 1.0;
  marker.color.r = 1.0;
  marker.color.g = 0.0;
  marker.color.b = 0.0;
  marker.lifetime = rclcpp::Duration::from_seconds(0);
  marker.frame_locked = false;
  marker_pub->publish(marker);
}

nav_msgs::msg::Path RRTstarPlannerROS::createPlan(
  const geometry_msgs::msg::PoseStamped& start, 
  const geometry_msgs::msg::PoseStamped& goal)
{
  nav_msgs::msg::Path plan;
  plan.header.frame_id = global_frame_;
  plan.header.stamp = node_.lock()->get_clock()->now();
  
  std::vector<geometry_msgs::msg::PoseStamped> plan_poses;
  std_msgs::msg::Bool msg;

  if(this->collision(start.pose.position.x, start.pose.position.y))
  {
    msg.data = false;
    accessable_pub_->publish(msg);
    RCLCPP_WARN(node_.lock()->get_logger(), "RRT* start point is obstacle");
    return plan;
  }

  if(this->collision(goal.pose.position.x, goal.pose.position.y))
  {
    msg.data = false;
    accessable_pub_->publish(msg);
    RCLCPP_WARN(node_.lock()->get_logger(), "RRT* target point is obstacle");
    return plan;
  }

  this->marker_tree_.points.clear();
  this->marker_tree_2_.points.clear();
  plan_poses.clear();
  std::vector<std::pair<double, double>> path;

  std::vector<Node> nodes; // First tree
  Node start_node;
  start_node.x = start.pose.position.x;
  start_node.y = start.pose.position.y;
  start_node.node_id = 0;
  start_node.parent_id = -1; // No parent node
  start_node.cost = 0.0;
  nodes.push_back(start_node);

  std::vector<Node> nodes_2; // Second tree
  Node goal_node;
  goal_node.x = goal.pose.position.x;
  goal_node.y = goal.pose.position.y;
  goal_node.node_id = 0;
  goal_node.parent_id = -1; // No parent node
  goal_node.cost = 0.0;
  nodes_2.push_back(goal_node);

  std::pair<double, double> p_rand; // Random sampled feasible point
  std::pair<double, double> p_new; // New node for first tree
  std::pair<double, double> p_new_2; // New node for second tree
  Node connect_node_on_tree1; // Node on tree1 when tree2 connects to tree1
  Node connect_node_on_tree2; // Node on tree2 when tree1 connects to tree2
  bool is_connect_to_tree1 = false;
  bool is_connect_to_tree2 = false;

  Node node_nearest;

  unsigned int seed = 0;
  auto node = node_.lock();
  double start_time = node->get_clock()->now().seconds();
  
  while (rclcpp::ok() && nodes.size() + nodes_2.size() < max_nodes_num_)
  {
    if((node->get_clock()->now().seconds() - start_time) > plan_time_out_) // Timeout
    {
      RCLCPP_WARN(node->get_logger(), "Failed to get a path. Time out.");
      msg.data = false;
      accessable_pub_->publish(msg);
      return plan;
    }
    
    // First tree
    while (rclcpp::ok())
    {
      srand(node->get_clock()->now().nanoseconds() + seed++); // Change seed
      unsigned int rand_nu = rand() % 10;
      if(rand_nu > 1) // 0.8 probability using random sampling extension
      {
        p_rand = sampleFree(); // random point in the free space
      }
      else // 0.2 probability using heuristic extension
      {
        p_rand.first = goal.pose.position.x;
        p_rand.second = goal.pose.position.y;
      }

      node_nearest = getNearest(nodes, p_rand); // The nearest node of the random point
      p_new = steer(node_nearest.x, node_nearest.y, p_rand.first, p_rand.second); // new point and node candidate.
      if (obstacleFree(node_nearest, p_new.first, p_new.second))
      { // Tree branch collision-free
        Node newnode;
        newnode.x = p_new.first;
        newnode.y = p_new.second;
        newnode.node_id = nodes.size(); // index of the last element after the push_back below
        newnode.parent_id = node_nearest.node_id;
        newnode.cost = 0.0;

        // Optimization
        newnode = chooseParent(node_nearest, newnode, nodes); // Select the best parent
        nodes.push_back(newnode);
        rewire(nodes, newnode);

        geometry_msgs::msg::Point point_tem;
        point_tem.x = nodes[newnode.parent_id].x;
        point_tem.y = nodes[newnode.parent_id].y;
        point_tem.z = 0;
        this->marker_tree_.points.push_back(point_tem);

        point_tem.x = newnode.x;
        point_tem.y = newnode.y;
        point_tem.z = 0;
        this->marker_tree_.points.push_back(point_tem);

        if(nodes.size() % 10 == 0)
        {
          this->pubTreeMarker(this->marker_pub_, this->marker_tree_, 1);
        }

        if(this->isConnect(newnode, nodes_2, nodes, connect_node_on_tree2))
        {
          is_connect_to_tree2 = true;
        }

        break;
      }
    }

    // Two trees connected, first tree searched to a node on the second tree
    if(is_connect_to_tree2)
    {
      getPathFromTree(nodes, nodes_2, connect_node_on_tree2, plan_poses, GetPlanMode::CONNECT1TO2);

      plan_poses[0].pose.orientation = start.pose.orientation;
      plan_poses[plan_poses.size()-1].pose.orientation = goal.pose.orientation;

      plan.poses = plan_poses;
      plan_pub_->publish(plan);
      msg.data = true;
      accessable_pub_->publish(msg);
      return plan;
    }

    // First tree reached the goal point
    if (pointCircleCollision(p_new.first, p_new.second, goal.pose.position.x, goal.pose.position.y, goal_radius_))
    {
      getPathFromTree(nodes, nodes_2, nodes.back(), plan_poses, GetPlanMode::TREE1);

      plan_poses[0].pose.orientation = start.pose.orientation;
      plan_poses[plan_poses.size()-1].pose.orientation = goal.pose.orientation;

      plan.poses = plan_poses;
      plan_pub_->publish(plan);
      msg.data = true;
      accessable_pub_->publish(msg);
      return plan;
    }

    // Second tree
    p_rand.first = p_new.first;
    p_rand.second = p_new.second;
    while (rclcpp::ok())
    {
      node_nearest = getNearest(nodes_2, p_rand); // The nearest node of the random point
      p_new_2 = steer(node_nearest.x, node_nearest.y, p_rand.first, p_rand.second); // new point and node candidate.
      if (obstacleFree(node_nearest, p_new_2.first, p_new_2.second))
      {
        Node newnode;
        newnode.x = p_new_2.first;
        newnode.y = p_new_2.second;
        newnode.node_id = nodes_2.size(); // index of the last element after the push_back below
        newnode.parent_id = node_nearest.node_id;
        newnode.cost = 0.0;

        // Optimize
        newnode = chooseParent(node_nearest, newnode, nodes_2); // Select the best parent
        nodes_2.push_back(newnode);
        rewire(nodes_2, newnode);

        geometry_msgs::msg::Point point_tem;
        point_tem.x = nodes_2[newnode.parent_id].x;
        point_tem.y = nodes_2[newnode.parent_id].y;
        point_tem.z = 0;
        this->marker_tree_2_.points.push_back(point_tem);

        point_tem.x = newnode.x;
        point_tem.y = newnode.y;
        point_tem.z = 0;
        this->marker_tree_2_.points.push_back(point_tem);

        if(nodes_2.size() % 10 == 0)
        {
          pubTreeMarker(this->marker_pub_, this->marker_tree_2_, 2);
        }

        if(this->isConnect(newnode, nodes, nodes_2, connect_node_on_tree1))
        {
          is_connect_to_tree1 = true;
        }

        break;
      }
      else
      {
        srand(node->get_clock()->now().nanoseconds() + seed++); // Change seed
        unsigned int rand_nu = rand() % 10;
        if(rand_nu > 1) // 0.8 probability using random sampling extension
        {
          p_rand = sampleFree(); // random point in the free space
        }
        else // 0.2 probability using heuristic extension
        {
          p_rand.first = start.pose.position.x;
          p_rand.second = start.pose.position.y;
        }
      }
    }

    // Two trees connected, second tree searched to a node on the first tree
    if(is_connect_to_tree1)
    {
      getPathFromTree(nodes, nodes_2, connect_node_on_tree1, plan_poses, GetPlanMode::CONNECT2TO1);

      plan_poses[0].pose.orientation = start.pose.orientation;
      plan_poses[plan_poses.size()-1].pose.orientation = goal.pose.orientation;

      plan.poses = plan_poses;
      plan_pub_->publish(plan);
      msg.data = true;
      accessable_pub_->publish(msg);
      return plan;
    }

    // Second tree reached the goal point
    if (pointCircleCollision(p_new_2.first, p_new_2.second, start.pose.position.x, start.pose.position.y, goal_radius_))
    {
      getPathFromTree(nodes, nodes_2, nodes.front(), plan_poses, GetPlanMode::TREE2);

      plan_poses[0].pose.orientation = start.pose.orientation;
      plan_poses[plan_poses.size()-1].pose.orientation = goal.pose.orientation;

      plan.poses = plan_poses;
      plan_pub_->publish(plan);
      msg.data = true;
      accessable_pub_->publish(msg);
      return plan;
    }
  }
  
  RCLCPP_WARN(node->get_logger(), "Failed to get a path.");
  msg.data = false;
  accessable_pub_->publish(msg);
  return plan;
}

void RRTstarPlannerROS::getPathFromTree(std::vector<Node>& tree1, std::vector<Node>& tree2, Node& connect_node,
                                        std::vector<geometry_msgs::msg::PoseStamped>& plan, GetPlanMode mode)
{
  std::pair<double, double> point;
  std::vector<std::pair<double, double>> path;
  Node current_node;

  // First tree searched to a node on the second tree
  if(mode == GetPlanMode::CONNECT1TO2)
  {
    current_node = tree1.back();
  }

  // First tree reached the path point
  if(mode == GetPlanMode::TREE1)
  {
    current_node = tree1.back();
  }

  // Second tree searched to a node on the first tree
  if(mode == GetPlanMode::CONNECT2TO1)
  {
    current_node = connect_node;
  }

  // Second tree reached the path
  if(mode == GetPlanMode::TREE2)
  {
    current_node = tree1[0];
  }

  // Final Path
  while (current_node.parent_id != tree1[0].parent_id)
  {
    point.first = current_node.x;
    point.second = current_node.y;
    path.insert(path.begin(), point); // Insert element at the beginning
    current_node = tree1[current_node.parent_id];
  }

  if(mode == GetPlanMode::CONNECT1TO2) // 1->2
  {
    current_node = connect_node;
  }

  if(mode == GetPlanMode::TREE1) // 1->goal
  {
    current_node = tree2[0];
  }

  if(mode == GetPlanMode::TREE2) // 2->start
  {
    current_node = tree2.back();
  }

  if(mode == GetPlanMode::CONNECT2TO1) // 2->1
  {
    current_node = tree2.back();
  }

  while (current_node.parent_id != tree2[0].parent_id)
  {
    point.first = current_node.x;
    point.second = current_node.y;
    path.push_back(point);
    current_node = tree2[current_node.parent_id];
  }

  point.first = tree1[0].x;
  point.second = tree1[0].y;
  path.insert(path.begin(), point); // Start point
  point.first = tree2[0].x;
  point.second = tree2[0].y;
  path.push_back(point); // Goal point

  cutPathPoint(path);

  insertPointForPath(path, this->path_point_spacing_);

  optimizationPath(path, this->angle_difference_);

  // Convert the points to poses
  auto node = node_.lock();
  rclcpp::Time plan_time = node->get_clock()->now();
  geometry_msgs::msg::PoseStamped pose;
  pose.pose.position.z = 0.0;
  pose.pose.orientation.x = 0.0;
  pose.pose.orientation.y = 0.0;
  pose.pose.orientation.z = 0.0;
  pose.pose.orientation.w = 1.0;
  for (size_t i = 0; i < path.size(); i++)
  {
    pose.header.stamp = plan_time;
    pose.header.frame_id = this->global_frame_;
    pose.pose.position.x = path[i].first;
    pose.pose.position.y = path[i].second;
    plan.push_back(pose);
  }

  optimizationOrientation(plan);
}

void RRTstarPlannerROS::optimizationOrientation(std::vector<geometry_msgs::msg::PoseStamped>& plan)
{
  size_t num = plan.size() - 1;
  if(num < 1)
    return;
  for(size_t i = 0; i < num; i++)
  {
    double yaw = atan2(plan[i+1].pose.position.y - plan[i].pose.position.y,
                       plan[i+1].pose.position.x - plan[i].pose.position.x);
    tf2::Quaternion q;
    q.setRPY(0, 0, yaw);
    plan[i].pose.orientation = tf2::toMsg(q);
  }
}

bool RRTstarPlannerROS::pointCircleCollision(double x1, double y1, double x2, double y2, double goal_radius)
{
  double dist = distance(x1, y1, x2, y2);
  if (dist < goal_radius)
    return true;
  else
    return false;
}

void RRTstarPlannerROS::insertPointForPath(std::vector<std::pair<double, double>>& path_in, double param)
{
  std::vector<std::pair<double, double>> path_out;
  size_t size = path_in.size() - 1;
  std::pair<double, double> point;
  double pp_dist = param;
  for(size_t i = 0; i < size; i++)
  {
    double theta = atan2(path_in[i+1].second - path_in[i].second, path_in[i+1].first - path_in[i].first);
    size_t insert_size = static_cast<size_t>(this->distance(path_in[i+1].first, path_in[i+1].second, path_in[i].first, path_in[i].second) / pp_dist + 0.5);
    for(size_t j = 0; j < insert_size; j++)
    {
      point.first = path_in[i].first + j * pp_dist * cos(theta);
      point.second = path_in[i].second + j * pp_dist * sin(theta);
      path_out.push_back(point);
    }
  }
  path_out.push_back(path_in.back());
  path_in.clear();
  size = path_out.size();
  path_in.resize(size);
  for(size_t i = 0; i < size; i++)
  {
    path_in[i] = path_out[i];
  }
}

int RRTstarPlannerROS::optimizationPath(std::vector<std::pair<double, double>>& plan, double movement_angle_range)
{
  if(plan.empty())
    return 0;
  size_t point_size = plan.size() - 1;
  double px, py, cx, cy, nx, ny, a_p, a_n;
  bool is_run = false;
  int ci = 0;
  for(ci = 0; ci < 1000; ci++)
  {
    is_run = false;
    for(size_t i = 1; i < point_size; i++)
    {
      px = plan[i-1].first;
      py = plan[i-1].second;

      cx = plan[i].first;
      cy = plan[i].second;

      nx = plan[i+1].first;
      ny = plan[i+1].second;

      a_p = normalizeAngle(atan2(cy-py, cx-px), 0, 2*M_PI);
      a_n = normalizeAngle(atan2(ny-cy, nx-cx), 0, 2*M_PI);

      if(std::max(a_p, a_n) - std::min(a_p, a_n) > movement_angle_range)
      {
        plan[i].first = (px + nx) / 2;
        plan[i].second = (py + ny) / 2;
        is_run = true;
      }
    }
    if(!is_run)
      return ci;
  }
  return ci;
}

bool RRTstarPlannerROS::isLineFree(const std::pair<double, double> p1, const std::pair<double, double> p2)
{
  std::pair<double, double> ptmp;
  ptmp.first = 0.0;
  ptmp.second = 0.0;

  double dist = sqrt((p2.second-p1.second) * (p2.second-p1.second) +
                     (p2.first-p1.first) * (p2.first-p1.first));
  if (dist < this->resolution_)
  {
    return true;
  }
  else
  {
    int value = int(floor(dist/this->resolution_));
    double theta = atan2(p2.second - p1.second,
                         p2.first - p1.first);
    int n = 1;
    for (int i = 0; i < value; i++)
    {
      ptmp.first = p1.first + this->resolution_*cos(theta) * n;
      ptmp.second = p1.second + this->resolution_*sin(theta) * n;
      if (collision(ptmp.first, ptmp.second))
        return false;
      n++;
    }
    return true;
  }
}

void RRTstarPlannerROS::cutPathPoint(std::vector<std::pair<double, double>>& plan)
{
  size_t current_index = 0;
  size_t check_index = current_index + 2;
  while(rclcpp::ok())
  {
    if(current_index >= plan.size() - 2)
      return;
    if(this->isLineFree(plan[current_index], plan[check_index])) // No obstacles between points
    {
      std::vector<std::pair<double, double>>::iterator it = plan.begin() + static_cast<int>(current_index + 1);
      if(check_index - current_index - 1 == 1)
      {
        plan.erase(it);
      }
      else
      {
        plan.erase(it, it + static_cast<int>(check_index - current_index - 1));
        check_index = current_index + 2;
      }
    }
    else
    {
      if(check_index < plan.size() - 1)
        check_index++;
      else
      {
        current_index++;
        check_index = current_index + 2;
      }
    }
  }
}

double RRTstarPlannerROS::distance(double px1, double py1, double px2, double py2)
{
  return sqrt((px1 - px2)*(px1 - px2) + (py1 - py2)*(py1 - py2));
}

std::pair<double, double> RRTstarPlannerROS::sampleFree()
{
  std::pair<double, double> random_point;
  unsigned int mx = 0, my = 0;
  double wx = 0.0, wy = 0.0;
  unsigned int map_size_x = costmap_->getSizeInCellsX();
  unsigned int map_size_y = costmap_->getSizeInCellsY();
  unsigned int seed = 0;
  auto node = node_.lock();
  while(rclcpp::ok())
  {
    srand(node->get_clock()->now().nanoseconds() + seed++); // Change seed
    mx = rand() % map_size_x;
    srand(node->get_clock()->now().nanoseconds() + seed++); // Change seed
    my = rand() % map_size_y;
    if(this->costmap_->getCost(mx, my) < nav2_costmap_2d::INSCRIBED_INFLATED_OBSTACLE)
      break;
  }
  this->costmap_->mapToWorld(mx, my, wx, wy);
  random_point.first = wx;
  random_point.second = wy;
  return random_point;
}

bool RRTstarPlannerROS::collision(double x, double y)
{
  unsigned int mx, my;
  if(!this->costmap_->worldToMap(x, y, mx, my))
    return true;
  if ((mx >= costmap_->getSizeInCellsX()) || (my >= costmap_->getSizeInCellsY()))
    return true;
  if (costmap_->getCost(mx, my) >= nav2_costmap_2d::INSCRIBED_INFLATED_OBSTACLE)
    return true;
  return false;
}

bool RRTstarPlannerROS::isAroundFree(double wx, double wy)
{
  unsigned int mx, my;
  if(!this->costmap_->worldToMap(wx, wy, mx, my))
    return false;
  if(mx <= 1 || my <= 1 || mx >= this->costmap_->getSizeInCellsX()-1 || my >= this->costmap_->getSizeInCellsY()-1)
    return false;
  int x, y;
  for(int i = -1; i <= 1; i++)
  {
    for(int j = -1; j <= 1; j++)
    {
      x = static_cast<int>(mx) + i;
      y = static_cast<int>(my) + j;
      if(this->costmap_->getCost(static_cast<unsigned int>(x), static_cast<unsigned int>(y)) >= nav2_costmap_2d::INSCRIBED_INFLATED_OBSTACLE)
        return false;
    }
  }
  return true;
}

bool RRTstarPlannerROS::isConnect(Node new_node, std::vector<Node>& another_tree, std::vector<Node>& current_tree, Node& connect_node)
{
  size_t node_size = another_tree.size();
  double min_distance = 10000000.0;
  double distance = 0.0;
  size_t min_distance_index = 0;
  for(size_t i = 0; i < node_size; i++)
  {
    distance = this->distance(new_node.x, new_node.y, another_tree[i].x, another_tree[i].y);
    if(distance < min_distance)
    {
      min_distance = distance;
      min_distance_index = i;
    }
  }

  distance = this->distance(new_node.x, new_node.y, another_tree[min_distance_index].x, another_tree[min_distance_index].y);

  if(distance < this->goal_radius_)
  {
    connect_node = another_tree[min_distance_index];
    Node newnode = another_tree[min_distance_index];
    // Optimize
    newnode = chooseParent(current_tree.back(), newnode, current_tree); // Select the best parent
    current_tree.push_back(newnode);
    rewire(current_tree, newnode);
    return true;
  }
  return false;
}

Node RRTstarPlannerROS::getNearest(std::vector<Node> nodes, std::pair<double, double> p_rand)
{
  double dist_min = distance(nodes[0].x, nodes[0].y, p_rand.first, p_rand.second);
  double dist_curr = 0;
  size_t index_min = 0;
  for (size_t i = 1; i < nodes.size(); i++)
  {
    dist_curr = distance(nodes[i].x, nodes[i].y, p_rand.first, p_rand.second);
    if (dist_curr < dist_min)
    {
      dist_min = dist_curr;
      index_min = i;
    }
  }
  return nodes[index_min];
}

Node RRTstarPlannerROS::chooseParent(Node nn, Node newnode, std::vector<Node> nodes)
{
  double dist_nnn = distance(nn.x, nn.y, newnode.x, newnode.y);
  for (size_t i = 0; i < nodes.size(); i++)
  {
    if (distance(nodes[i].x, nodes[i].y, newnode.x, newnode.y) < search_radius_ &&
        nodes[i].cost + distance(nodes[i].x, nodes[i].y, newnode.x, newnode.y) < nn.cost + dist_nnn &&
        obstacleFree(nodes[i], newnode.x, newnode.y))
    {
      nn = nodes[i];
    }
  }
  newnode.cost = nn.cost + distance(nn.x, nn.y, newnode.x, newnode.y);
  if(!this->isAroundFree(newnode.x, newnode.y))
    newnode.cost += 0.3;
  newnode.parent_id = nn.node_id;
  return newnode;
}

void RRTstarPlannerROS::rewire(std::vector<Node>& nodes, Node newnode)
{
  for (size_t i = 0; i < nodes.size(); i++)
  {
    Node& node = nodes[i];
    if (node != nodes[newnode.parent_id] &&
        distance(node.x, node.y, newnode.x, newnode.y) < search_radius_ &&
        newnode.cost + distance(node.x, node.y, newnode.x, newnode.y) < node.cost &&
        obstacleFree(node, newnode.x, newnode.y))
    {
      node.parent_id = newnode.node_id;
      node.cost = newnode.cost + distance(node.x, node.y, newnode.x, newnode.y);
      if(!this->isAroundFree(node.x, node.y))
        node.cost += 0.3;
    }
  }
}

std::pair<double, double> RRTstarPlannerROS::steer(double x1, double y1, double x2, double y2)
{
  std::pair<double, double> p_new;
  double dist = distance(x1, y1, x2, y2);
  if (dist < epsilon_max_ && dist > epsilon_min_)
  {
    p_new.first = x2;
    p_new.second = y2;
  }
  else
  {
    double theta = atan2(y2-y1, x2-x1);
    p_new.first = x1 + epsilon_max_*cos(theta);
    p_new.second = y1 + epsilon_max_*sin(theta);
  }
  return p_new;
}

bool RRTstarPlannerROS::obstacleFree(Node node_nearest, double px, double py)
{
  std::pair<double, double> p_n;
  p_n.first = 0.0;
  p_n.second = 0.0;

  double dist = distance(node_nearest.x, node_nearest.y, px, py);
  if (dist < resolution_)
  {
    if (collision(px, py))
      return false;
    else
      return true;
  }
  else
  {
    int value = int(floor(dist/resolution_));
    double theta = atan2(py - node_nearest.y, px - node_nearest.x);
    int n = 1;
    for (int i = 0; i < value; i++)
    {
      p_n.first = node_nearest.x + n*resolution_*cos(theta);
      p_n.second = node_nearest.y + n*resolution_*sin(theta);
      if (collision(p_n.first, p_n.second))
        return false;
      n++;
    }
    return true;
  }
}

} // namespace rrt_star_global_planner