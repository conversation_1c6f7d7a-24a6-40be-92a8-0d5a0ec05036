#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    # Get package share directories
    rrt_star_share = FindPackageShare(package='rrt_star_global_planner').find('rrt_star_global_planner')
    global_traj_share = FindPackageShare(package='global_traj_generate').find('global_traj_generate')
    terrain_analysis_share = FindPackageShare(package='terrain_analysis').find('terrain_analysis')
    local_planner_share = FindPackageShare(package='local_planner').find('local_planner')
    velocity_smoother_share = FindPackageShare(package='yocs_velocity_smoother').find('yocs_velocity_smoother')

    # Include launch files
    rrt_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([rrt_star_share, '/launch/rrt_node_launch.py'])
    )
    
    global_traj_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([global_traj_share, '/launch/global_traj_generate_launch.py'])
    )
    
    terrain_analysis_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([terrain_analysis_share, '/launch/terrain_analysis_launch.py'])
    )
    
    local_planner_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([local_planner_share, '/launch/local_planner_launch.py'])
    )
    
    velocity_smoother_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource([velocity_smoother_share, '/launch/standalone_launch.py'])
    )

    return LaunchDescription([
        rrt_launch,
        global_traj_launch,
        terrain_analysis_launch,
        local_planner_launch,
        velocity_smoother_launch
    ])