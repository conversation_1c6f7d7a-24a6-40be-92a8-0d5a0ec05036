#ifndef RRT_STAR_ROS_HPP
#define RRT_STAR_ROS_HPP

#include <rclcpp/rclcpp.hpp>
#include <nav2_core/global_planner.hpp>
#include <nav2_costmap_2d/costmap_2d_ros.hpp>
#include <nav2_costmap_2d/costmap_2d.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <nav_msgs/msg/path.hpp>
#include <visualization_msgs/msg/marker.hpp>
#include <std_msgs/msg/bool.hpp>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <tf2_ros/buffer.h>
#include <cmath>
#include <vector>

struct Node
{
  double x;
  double y;
  int node_id;
  int parent_id;
  double cost;
  
  bool operator ==(const Node& node) 
  {
    return (fabs(x - node.x) < 0.0001) && (fabs(y - node.y) < 0.0001) && 
           (node_id == node.node_id) && (parent_id == node.parent_id) && 
           (fabs(cost - node.cost) < 0.0001);
  }

  bool operator !=(const Node& node) 
  {
    if((fabs(x - node.x) > 0.0001) || (fabs(y - node.y) > 0.0001) || 
       (node_id != node.node_id) || (parent_id != node.parent_id) || 
       (fabs(cost - node.cost) > 0.0001))
      return true;
    else
      return false;
  }
}; 

enum GetPlanMode
{
  TREE1 = 1,
  TREE2 = 2,
  CONNECT1TO2 = 3,
  CONNECT2TO1 = 4,
};

namespace rrt_star_global_planner 
{

class RRTstarPlannerROS : public nav2_core::GlobalPlanner 
{
public:
  RRTstarPlannerROS();
  ~RRTstarPlannerROS() = default;

  void configure(
    const rclcpp_lifecycle::LifecycleNode::WeakPtr & parent,
    std::string name, std::shared_ptr<tf2_ros::Buffer> tf,
    std::shared_ptr<nav2_costmap_2d::Costmap2DROS> costmap_ros) override;

  void cleanup() override;

  void activate() override;

  void deactivate() override;

  nav_msgs::msg::Path createPlan(
    const geometry_msgs::msg::PoseStamped & start,
    const geometry_msgs::msg::PoseStamped & goal) override;

  void getPathFromTree1ConnectTree2(std::vector<Node>& tree1,
                                    std::vector<Node>& tree2,
                                    Node& connect_node,
                                    std::vector<geometry_msgs::msg::PoseStamped>& plan);

  void getPathFromTree(std::vector<Node>& tree1,
                       std::vector<Node>& tree2,
                       Node& connect_node,
                       std::vector<geometry_msgs::msg::PoseStamped>& plan,
                       GetPlanMode mode);
  
  double distance(double px1, double py1, double px2, double py2);

  std::pair<double, double> sampleFree();

  bool collision(double x, double y);

  bool isAroundFree(double wx, double wy);

  bool isConnect(Node new_node, std::vector<Node>& another_tree, 
                 std::vector<Node>& current_tree, Node& connect_node);

  Node getNearest(std::vector<Node> nodes, std::pair<double, double> p_rand);

  Node chooseParent(Node nn, Node newnode, std::vector<Node> nodes);

  void rewire(std::vector<Node>& nodes, Node newnode);

  std::pair<double, double> steer(double x1, double y1, double x2, double y2);

  bool obstacleFree(Node node_nearest, double px, double py);

  bool pointCircleCollision(double x1, double y1, double x2, double y2, double radius);

  void optimizationOrientation(std::vector<geometry_msgs::msg::PoseStamped>& plan);

  void insertPointForPath(std::vector<std::pair<double, double>>& pathin, double param);

  int optimizationPath(std::vector<std::pair<double, double>>& plan, 
                       double movement_angle_range = M_PI/4);

  bool isLineFree(const std::pair<double, double> p1, 
                  const std::pair<double, double> p2);

  void cutPathPoint(std::vector<std::pair<double, double>>& plan);

  double inline normalizeAngle(double val, double min = -M_PI, double max = M_PI);

  void pubTreeMarker(rclcpp::Publisher<visualization_msgs::msg::Marker>::SharedPtr& marker_pub,
                     visualization_msgs::msg::Marker marker, int id);
       
protected:
  nav2_costmap_2d::Costmap2D* costmap_;
  std::shared_ptr<nav2_costmap_2d::Costmap2DROS> costmap_ros_;
  std::string global_frame_;
  rclcpp::Publisher<nav_msgs::msg::Path>::SharedPtr plan_pub_;
  rclcpp::Publisher<std_msgs::msg::Bool>::SharedPtr accessable_pub_;

private:
  rclcpp_lifecycle::LifecycleNode::WeakPtr node_;
  std::string name_;
  std::shared_ptr<tf2_ros::Buffer> tf_;
  
  visualization_msgs::msg::Marker marker_tree_;
  visualization_msgs::msg::Marker marker_tree_2_;
  rclcpp::Publisher<visualization_msgs::msg::Marker>::SharedPtr marker_pub_;
  
  size_t max_nodes_num_;
  double plan_time_out_;
  double search_radius_;
  double goal_radius_;
  double epsilon_min_;
  double epsilon_max_;

  double path_point_spacing_;
  double angle_difference_;

  double resolution_;
  bool configured_;
  bool initialized_;
};

} // namespace rrt_star_global_planner

#endif // RRT_STAR_ROS_HPP