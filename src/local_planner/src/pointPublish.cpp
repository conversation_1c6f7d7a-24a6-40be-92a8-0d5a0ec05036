#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/point.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <tf2/LinearMath/Transform.h>
#include <tf2_ros/transform_broadcaster.h>
// #include <local_planner/msg/navigation_result.hpp>
// #include <local_planner/msg/navigation_target.hpp>
#include <sstream>
#include <fstream>
#include <vector>
#include <termios.h>
#include <unistd.h>
#include <fcntl.h>
#include <std_msgs/msg/int8.hpp>
#include <iostream>
#include <thread>
#include <chrono>

using namespace std;

// Global variables (will be moved to class later)
int point_id, point_info, gait, speed, manner, obsmode, navmode, delayTime;
string file_name;
vector<geometry_msgs::msg::Point> points;
int mode = 0, num = 0;
bool init_flag = false;
double odomx = 0, odomy = 0, odomz = 0;
std_msgs::msg::Int8 floorStatus;
std_msgs::msg::Int8 ArriveStatus;

// ROS2 node class
class PointPublishNode : public rclcpp::Node
{
public:
    PointPublishNode() : Node("point_publish_node")
    {
        // Initialize publishers and subscribers
        pubTarget = this->create_publisher<geometry_msgs::msg::PoseStamped>("/waypoint_nav", 1);
        pubArriveStatus = this->create_publisher<std_msgs::msg::Int8>("/arrive_status", 1);
        
        subResult = this->create_subscription<std_msgs::msg::Int8>(
            "/navigation_result", 1, std::bind(&PointPublishNode::resultCallback, this, std::placeholders::_1));
        subOdometry = this->create_subscription<nav_msgs::msg::Odometry>(
            "/state_estimation", 1, std::bind(&PointPublishNode::odomCallback, this, std::placeholders::_1));
            
        RCLCPP_INFO(this->get_logger(), "PointPublish node initialized");
    }

private:
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pubTarget;
    rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr pubArriveStatus;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr subResult;
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr subOdometry;
    
    void resultCallback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        // Handle navigation result
        RCLCPP_INFO(this->get_logger(), "Received navigation result: %d", msg->data);
    }
    
    void odomCallback(const nav_msgs::msg::Odometry::SharedPtr msg)
    {
        odomx = msg->pose.pose.position.x;
        odomy = msg->pose.pose.position.y;
        odomz = msg->pose.pose.position.z;
    }
};

// Temporary simple main function for compilation
int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<PointPublishNode>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}