#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <rclcpp/rclcpp.hpp>
#include <message_filters/subscriber.h>
#include <message_filters/synchronizer.h>
#include <message_filters/sync_policies/approximate_time.h>
#include <std_msgs/msg/int8.hpp>
#include <std_msgs/msg/float32.hpp>
#include <std_msgs/msg/bool.hpp>
#include <nav_msgs/msg/path.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <geometry_msgs/msg/twist_stamped.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <sensor_msgs/msg/imu.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <sensor_msgs/msg/joy.hpp>
#include <tf2/LinearMath/Transform.h>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#include <tf2_ros/transform_broadcaster.h>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/kdtree/kdtree_flann.h>
#include <pcl/common/time.h>
#include <pcl/registration/icp.h>
#include <pcl/io/pcd_io.h>
#include <Eigen/Dense>
#include <iomanip>

using namespace std;

#define pi 3.1415926535

class PIDController
{
public:
    PIDController(double kp, double ki, double kd)
        : kp_(kp), ki_(ki), kd_(kd), prev_error_(0), integral_(0)
    {
    }
    
    void Parameter(double Kp, double Ki, double Kd)
    {
        kp_ = Kp;
        ki_ = Ki;
        kd_ = Kd;
    }
    
    double Control(double error, double dt, double actual, double err_max, double limit)
    {
        error = std::max(std::min(error, err_max), -err_max);
        double result = 0;
        integral_ += error * 0.01;
        double derivative = (error - prev_error_) / 0.01;
        prev_error_ = error;
        
        double integral_limit = limit / ki_;
        integral_ = std::max(std::min(integral_, integral_limit), -integral_limit);
        result = kp_ * error + ki_ * integral_ + kd_ * derivative;
        
        result = std::max(std::min(result, limit), -limit);
        
        const double deadzone = 0.001;
        if(fabs(result) < deadzone) {
            result = 0;
        }
        return result;
    }
    
private:
    double kp_;
    double ki_;
    double kd_;
    double prev_error_;
    double integral_;
};

class CalibrationNode : public rclcpp::Node
{
public:
    CalibrationNode() : Node("calibration_node")
    {
        // Initialize parameters
        declare_parameter("p_vel_yaw", 0.5);
        declare_parameter("i_vel_yaw", 0.1);
        declare_parameter("d_vel_yaw", 0.0);
        declare_parameter("p_vel_x", 0.5);
        declare_parameter("i_vel_x", 0.1);
        declare_parameter("d_vel_x", 0.0);
        declare_parameter("p_vel_y", 0.5);
        declare_parameter("i_vel_y", 0.1);
        declare_parameter("d_vel_y", 0.0);
        declare_parameter("errorYaw_max", 3.0);
        declare_parameter("errorYaw_min", 1.0);
        declare_parameter("errorX_max", 3.0);
        declare_parameter("errorY_max", 3.0);
        declare_parameter("X_max", 0.5);
        declare_parameter("Y_max", 0.1);
        declare_parameter("Yaw_max", 0.1);
        declare_parameter("set_yaw_precision", 0.3);
        declare_parameter("set_x_precision", 0.3);
        declare_parameter("set_y_precision", 0.3);
        
        // Get parameters
        get_parameter("p_vel_yaw", p_vel_yaw);
        get_parameter("i_vel_yaw", i_vel_yaw);
        get_parameter("d_vel_yaw", d_vel_yaw);
        get_parameter("p_vel_x", p_vel_x);
        get_parameter("i_vel_x", i_vel_x);
        get_parameter("d_vel_x", d_vel_x);
        get_parameter("p_vel_y", p_vel_y);
        get_parameter("i_vel_y", i_vel_y);
        get_parameter("d_vel_y", d_vel_y);
        get_parameter("errorYaw_max", errorYaw_max);
        get_parameter("errorYaw_min", errorYaw_min);
        get_parameter("errorX_max", errorX_max);
        get_parameter("errorY_max", errorY_max);
        get_parameter("X_max", X_max);
        get_parameter("Y_max", Y_max);
        get_parameter("Yaw_max", Yaw_max);
        get_parameter("set_yaw_precision", set_yaw_precision);
        get_parameter("set_x_precision", set_x_precision);
        get_parameter("set_y_precision", set_y_precision);
        
        // Initialize PID controllers
        pid_x = std::make_unique<PIDController>(p_vel_x, i_vel_x, d_vel_x);
        pid_y = std::make_unique<PIDController>(p_vel_y, i_vel_y, d_vel_y);
        pid_yaw = std::make_unique<PIDController>(p_vel_yaw, i_vel_yaw, d_vel_yaw);
        
        pid_x->Parameter(p_vel_x, i_vel_x, d_vel_x);
        pid_y->Parameter(p_vel_y, i_vel_y, d_vel_y);
        pid_yaw->Parameter(p_vel_yaw, i_vel_yaw, d_vel_yaw);
        
        // Initialize state variables
        vehicleX = vehicleY = vehicleZ = 0.0;
        vehicleRoll = vehiclePitch = vehicleYaw = 0.0;
        goalX = goalY = goalZ = goalYaw = 0.0;
        odom_time = 0.0;
        nav_start = 0;
        id = 0;
        last_angular_z = last_linear_x = last_linear_y = 0.0;
        newTerrainCloud = false;
        vehicleLength = 1.0;
        vehicleWidth = 0.5;
        odometryTime = 0.0;
        arrived = 1;
        adjustmode_data = false;
        posecalibration_data = false;
        start_time = end_time = start = 0.0;
        
        // Initialize point clouds
        terrainCloud = std::make_shared<pcl::PointCloud<pcl::PointXYZI>>();
        terrainCloudCrop = std::make_shared<pcl::PointCloud<pcl::PointXYZI>>();
        
        // Initialize subscribers
        subOdom = this->create_subscription<nav_msgs::msg::Odometry>(
            "/state_estimation", 1, std::bind(&CalibrationNode::odomHandler, this, std::placeholders::_1));
        subGoal = this->create_subscription<geometry_msgs::msg::PoseStamped>(
            "/move_base_simple/goal", 1, std::bind(&CalibrationNode::goalHandler, this, std::placeholders::_1));
        subWebGoal = this->create_subscription<geometry_msgs::msg::PoseStamped>(
            "web_goal_pose", 1, std::bind(&CalibrationNode::webgoalHandler, this, std::placeholders::_1));
        subMode = this->create_subscription<std_msgs::msg::Bool>(
            "/adjustmode", 1, std::bind(&CalibrationNode::modeHandler, this, std::placeholders::_1));
        subTerrainCloud = this->create_subscription<sensor_msgs::msg::PointCloud2>(
            "/terrain_map", 5, std::bind(&CalibrationNode::terrainCloudHandler, this, std::placeholders::_1));
        
        // Initialize publishers
        pubSpeed = this->create_publisher<geometry_msgs::msg::Twist>("/raw_cmd_vel", 1);
        pubStop = this->create_publisher<std_msgs::msg::Int8>("/stop", 1);
        innerpubStop = this->create_publisher<std_msgs::msg::Int8>("/istop", 1);
        pubMode = this->create_publisher<std_msgs::msg::Bool>("/adjustmode", 5);
        
        // Initialize timer for control loop
        timer = this->create_wall_timer(
            std::chrono::milliseconds(10), // 100Hz
            std::bind(&CalibrationNode::controlLoop, this));
        
        RCLCPP_INFO(this->get_logger(), "Calibration node initialized");
    }

private:
    // State variables
    float vehicleX, vehicleY, vehicleZ;
    float vehicleRoll, vehiclePitch, vehicleYaw;
    double odom_time;
    double goalX, goalY, goalZ, goalYaw;
    int nav_start, id;
    double last_angular_z, last_linear_x, last_linear_y;
    bool newTerrainCloud;
    double vehicleLength, vehicleWidth;
    double odometryTime;
    int arrived;
    bool adjustmode_data;
    bool posecalibration_data;
    double start_time, end_time, start;
    
    // PID parameters
    double p_vel_yaw, i_vel_yaw, d_vel_yaw;
    double p_vel_x, i_vel_x, d_vel_x;
    double p_vel_y, i_vel_y, d_vel_y;
    double errorYaw_max, errorX_max, errorY_max, errorYaw_min;
    double set_yaw_precision, set_x_precision, set_y_precision;
    double Yaw_max, X_max, Y_max;
    
    // PID controllers
    std::unique_ptr<PIDController> pid_x;
    std::unique_ptr<PIDController> pid_y;
    std::unique_ptr<PIDController> pid_yaw;
    
    // Point clouds
    std::shared_ptr<pcl::PointCloud<pcl::PointXYZI>> terrainCloud;
    std::shared_ptr<pcl::PointCloud<pcl::PointXYZI>> terrainCloudCrop;
    
    // ROS2 interfaces
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr subOdom;
    rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr subGoal;
    rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr subWebGoal;
    rclcpp::Subscription<std_msgs::msg::Bool>::SharedPtr subMode;
    rclcpp::Subscription<sensor_msgs::msg::PointCloud2>::SharedPtr subTerrainCloud;
    
    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr pubSpeed;
    rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr pubStop;
    rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr innerpubStop;
    rclcpp::Publisher<std_msgs::msg::Bool>::SharedPtr pubMode;
    
    rclcpp::TimerBase::SharedPtr timer;
    
    // Utility functions
    Eigen::Vector3d Quat2rpy(const Eigen::Quaterniond& quat)
    {
        Eigen::Vector3d atti;
        Eigen::Vector4d q(quat.w(), quat.x(), quat.y(), quat.z());
        q.normalize();
        double t0 = -2.0 * (q(2) * q(2) + q(3) * q(3)) + 1.0;
        double t1 = +2.0 * (q(1) * q(2) + q(0) * q(3));
        double t2 = -2.0 * (q(1) * q(3) - q(0) * q(2));
        double t3 = +2.0 * (q(2) * q(3) + q(0) * q(1));
        double t4 = -2.0 * (q(1) * q(1) + q(2) * q(2)) + 1.0;
        t2 = t2 > 1.0 ? 1.0 : t2;
        t2 = t2 < -1.0 ? -1.0 : t2;

        double yaw = atan2(t1, t0);
        double pitch = asin(t2);
        double roll = atan2(t3, t4);

        atti[0] = yaw;
        atti[1] = pitch;
        atti[2] = roll;
        return atti;
    }

    double normalizeAngle(double angle)
    {
        while (angle > M_PI) {
            angle -= 2. * M_PI;
        }
        while (angle < -M_PI) {
            angle += 2. * M_PI;
        }
        return angle;
    }
    
    // Callback functions
    void odomHandler(const nav_msgs::msg::Odometry::SharedPtr odomIn)
    {
        double roll, pitch, yaw;
        geometry_msgs::msg::Quaternion geoQuat = odomIn->pose.pose.orientation;
        
        auto q_ = Eigen::Quaterniond(geoQuat.w, geoQuat.x, geoQuat.y, geoQuat.z);
        auto euler = Quat2rpy(q_);
        yaw = normalizeAngle(euler[0]);
        
        vehicleRoll = roll;
        vehiclePitch = pitch;
        vehicleYaw = yaw;
        odom_time = odomIn->header.stamp.sec + odomIn->header.stamp.nanosec * 1e-9;
        vehicleX = odomIn->pose.pose.position.x;
        vehicleY = odomIn->pose.pose.position.y;
        vehicleZ = odomIn->pose.pose.position.z;
        odometryTime = pcl::getTime();
    }
    
    void goalHandler(const geometry_msgs::msg::PoseStamped::SharedPtr goal)
    {
        goalX = goal->pose.position.x;
        goalY = goal->pose.position.y;
        
        tf2::Quaternion quat;
        tf2::fromMsg(goal->pose.orientation, quat);
        double roll, pitch, yaw;
        tf2::Matrix3x3(quat).getRPY(roll, pitch, yaw);
        goalYaw = yaw;
        
        nav_start = true;
        arrived = 0;
        posecalibration_data = false;
    }
    
    void webgoalHandler(const geometry_msgs::msg::PoseStamped::SharedPtr goal)
    {
        goalX = goal->pose.position.x;
        goalY = goal->pose.position.y;
        
        RCLCPP_INFO(this->get_logger(), "Web target yaw: %f, unit:deg", goal->pose.orientation.z);
        
        goalYaw = (goal->pose.orientation.z * M_PI) / 180;
        nav_start = true;
        arrived = 0;
        posecalibration_data = false;
    }
    
    void modeHandler(const std_msgs::msg::Bool::SharedPtr mode)
    {
        adjustmode_data = mode->data;
        start = pcl::getTime();
    }
    
    void terrainCloudHandler(const sensor_msgs::msg::PointCloud2::SharedPtr terrainCloud2)
    {
        terrainCloud->clear();
        pcl::fromROSMsg(*terrainCloud2, *terrainCloud);
        pcl::PointXYZI point;
        terrainCloudCrop->clear();
        int terrainCloudSize = terrainCloud->points.size();
        for (int i = 0; i < terrainCloudSize; i++) 
        {
            point = terrainCloud->points[i];
            if (point.intensity > 0.1)
            {
                terrainCloudCrop->push_back(point);
            }
        }
        newTerrainCloud = true;
    }
    
    void controlLoop()
    {
        if (adjustmode_data && nav_start == 1 && newTerrainCloud)
        {
            newTerrainCloud = false;
            geometry_msgs::msg::Twist cmd_vel;
            double errorX = cos(vehicleYaw) * (goalX - vehicleX) + sin(vehicleYaw) * (goalY - vehicleY);
            double errorY = -sin(vehicleYaw) * (goalX - vehicleX) + cos(vehicleYaw) * (goalY - vehicleY);
            double errorYaw = goalYaw - vehicleYaw;
            double use_errorYaw = errorYaw;
            
            if (errorYaw > pi) errorYaw = errorYaw - 2 * pi;
            else if (errorYaw < -pi) errorYaw = errorYaw + 2 * pi;
            
            if (abs(errorX) >= set_x_precision && abs(errorY) >= set_y_precision && !posecalibration_data)
            {
                cmd_vel.linear.y = pid_y->Control(errorY, 0.01, last_linear_y, errorY_max, Y_max);
                last_linear_y = cmd_vel.linear.y;
                cmd_vel.linear.x = pid_x->Control(errorX, 0.01, last_linear_x, errorX_max, X_max);
                last_linear_x = cmd_vel.linear.x;
                
                RCLCPP_INFO(this->get_logger(), "errorX: %f, errorY: %f", errorX, errorY);
            }
            else
            {
                posecalibration_data = true;
                if(use_errorYaw < errorYaw_min) use_errorYaw = errorYaw_min;
                
                if(errorYaw >= 0)
                {
                    cmd_vel.angular.z = abs(pid_yaw->Control(use_errorYaw, 0.01, last_angular_z, errorYaw_max, Yaw_max));
                }
                else if(errorYaw < 0)
                {
                    cmd_vel.angular.z = -abs(pid_yaw->Control(use_errorYaw, 0.01, last_angular_z, errorYaw_max, Yaw_max));
                }
                last_angular_z = cmd_vel.angular.z;
                
                RCLCPP_INFO(this->get_logger(), "Web target yaw: %f, unit:rad", goalYaw);
                RCLCPP_INFO(this->get_logger(), "odom time: %f, odom X: %f, odom Y: %f, odom yaw: %f, unit:deg", 
                           odom_time, vehicleX, vehicleY, vehicleYaw * 57.3);
            }
            
            if (abs(errorYaw) <= set_yaw_precision)
            {
                auto safetystop = std_msgs::msg::Int8();
                end_time = pcl::getTime();
                if (end_time - start_time >= 1 && arrived == 0)
                {
                    cmd_vel.linear.x = 0;
                    cmd_vel.linear.y = 0;
                    cmd_vel.angular.z = 0;
                    adjustmode_data = false;
                    
                    auto adjustmode_msg = std_msgs::msg::Bool();
                    adjustmode_msg.data = adjustmode_data;
                    pubMode->publish(adjustmode_msg);
                    
                    safetystop.data = 1;
                    pubStop->publish(safetystop);
                    innerpubStop->publish(safetystop);
                    arrived = 1;
                    
                    RCLCPP_INFO(this->get_logger(), "***********已到达目标点***********");
                    RCLCPP_INFO(this->get_logger(), "X方向误差: %f, Y方向误差: %f, 角度误差: %f, unit:rad", 
                               errorX, errorY, errorYaw);
                    RCLCPP_INFO(this->get_logger(), "X: %f, Y: %f, 角度: %f, unit:rad", 
                               vehicleX, vehicleY, vehicleYaw);
                }
            }
            else
            {
                start_time = pcl::getTime();
            }
            
            pubSpeed->publish(cmd_vel);
        }
    }
};

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<CalibrationNode>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}