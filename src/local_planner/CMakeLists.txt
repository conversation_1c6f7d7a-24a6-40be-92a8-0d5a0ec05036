cmake_minimum_required(VERSION 3.8)
project(local_planner)

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

set(CMAKE_BUILD_TYPE Release)

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclpy REQUIRED)
find_package(std_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(pcl_conversions REQUIRED)
find_package(PCL REQUIRED)

# Generate interfaces (temporarily commented out)
# rosidl_generate_interfaces(${PROJECT_NAME}
#   "msg/NavigationTarget.msg"
#   "msg/NavigationResult.msg"
#   DEPENDENCIES std_msgs geometry_msgs
#   LIBRARY_NAME ${PROJECT_NAME}
# )

include_directories(
  include
  ${PCL_INCLUDE_DIRS}
)

# Create executables
add_executable(pathFollower src/pathFollower.cpp)
add_executable(localPlanner src/localPlanner.cpp)
add_executable(calibration src/calibration.cpp)
add_executable(pointPublish src/pointPublish.cpp)

# Set target dependencies
ament_target_dependencies(localPlanner
  rclcpp
  std_msgs
  sensor_msgs
  geometry_msgs
  nav_msgs
  tf2
  tf2_ros
  tf2_geometry_msgs
  pcl_conversions
)

ament_target_dependencies(pathFollower
  rclcpp
  std_msgs
  sensor_msgs
  geometry_msgs
  nav_msgs
  tf2
  tf2_ros
  tf2_geometry_msgs
  pcl_conversions
)

ament_target_dependencies(calibration
  rclcpp
  std_msgs
  sensor_msgs
  geometry_msgs
  nav_msgs
  tf2
  tf2_ros
  tf2_geometry_msgs
  pcl_conversions
)

ament_target_dependencies(pointPublish
  rclcpp
  std_msgs
  sensor_msgs
  geometry_msgs
  nav_msgs
  tf2
  tf2_ros
  tf2_geometry_msgs
  pcl_conversions
)

# Link PCL libraries
target_link_libraries(localPlanner ${PCL_LIBRARIES})
target_link_libraries(pathFollower ${PCL_LIBRARIES})
target_link_libraries(calibration ${PCL_LIBRARIES})
target_link_libraries(pointPublish ${PCL_LIBRARIES})

# Link the generated interfaces (temporarily commented out)
# rosidl_target_interfaces(localPlanner
#   ${PROJECT_NAME} "rosidl_typesupport_cpp")
# rosidl_target_interfaces(pathFollower
#   ${PROJECT_NAME} "rosidl_typesupport_cpp")
# rosidl_target_interfaces(calibration
#   ${PROJECT_NAME} "rosidl_typesupport_cpp")
# rosidl_target_interfaces(pointPublish
#   ${PROJECT_NAME} "rosidl_typesupport_cpp")
# rosidl_target_interfaces(obstacle_stop
#   ${PROJECT_NAME} "rosidl_typesupport_cpp")

# Install executables
install(TARGETS 
  localPlanner
  pathFollower
  calibration
  pointPublish
  DESTINATION lib/${PROJECT_NAME}
)

install(DIRECTORY include/
  DESTINATION include/
)

install(DIRECTORY launch
  DESTINATION share/${PROJECT_NAME}/
)

install(DIRECTORY config
  DESTINATION share/${PROJECT_NAME}/
)

install(DIRECTORY paths
  DESTINATION share/${PROJECT_NAME}/
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_export_dependencies(
  rclcpp
  std_msgs
  sensor_msgs
  geometry_msgs
  nav_msgs
  pcl_conversions
)

ament_package()
