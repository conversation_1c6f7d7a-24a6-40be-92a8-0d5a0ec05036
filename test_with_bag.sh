#!/bin/bash
# test_with_bag.sh - NR_Navigation系统bag包仿真测试脚本
# 
# 用法: ./test_with_bag.sh <bag_directory> [mode]
# 参数:
#   bag_directory: ROS2 bag包目录路径
#   mode: 导航模式 (indoor | outdoor)，默认为indoor
#
# 示例:
#   ./test_with_bag.sh ./my_robot_data indoor
#   ./test_with_bag.sh /path/to/bagfile outdoor

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    print_error "缺少参数"
    echo "用法: $0 <bag_directory> [mode]"
    echo "模式: indoor (默认) | outdoor"
    echo ""
    echo "示例:"
    echo "  $0 ./robot_data indoor"
    echo "  $0 /home/<USER>/bagfiles/test_run outdoor"
    exit 1
fi

BAG_DIR=$1
MODE=${2:-indoor}

# 验证bag包目录
if [ ! -d "$BAG_DIR" ]; then
    print_error "Bag包目录不存在: $BAG_DIR"
    exit 1
fi

# 设置环境变量
print_info "设置ROS2环境..."
if [ -f "/opt/ros/humble/setup.bash" ]; then
    source /opt/ros/humble/setup.bash
else
    print_error "未找到ROS2 Humble环境"
    exit 1
fi

if [ -f "install/setup.bash" ]; then
    source install/setup.bash
else
    print_warning "未找到本地workspace setup.bash，请确保已编译项目"
fi

# 检查NR_Navigation可用性
print_info "检查NR_Navigation系统可用性..."
if [ -f "./nr_navigation.bash" ]; then
    if ! ./nr_navigation.bash test > /dev/null 2>&1; then
        print_error "NR_Navigation系统测试失败"
        exit 1
    fi
    print_success "NR_Navigation系统检查通过"
else
    print_error "未找到nr_navigation.bash脚本"
    exit 1
fi

# 检查bag包内容
print_info "检查bag包内容..."
if command -v ros2 &> /dev/null; then
    if ros2 bag info "$BAG_DIR" > /dev/null 2>&1; then
        print_success "Bag包格式验证通过"
        
        # 显示bag包信息
        echo ""
        print_info "Bag包详细信息:"
        ros2 bag info "$BAG_DIR" | head -20
        echo ""
        
        # 检查关键话题
        print_info "检查关键导航话题..."
        TOPICS=$(ros2 bag info "$BAG_DIR" 2>/dev/null | grep "Topic:" | awk '{print $3}')
        
        for topic in "/scan" "/odom" "/tf" "/tf_static"; do
            if echo "$TOPICS" | grep -q "$topic"; then
                print_success "找到话题: $topic"
            else
                print_warning "未找到话题: $topic"
            fi
        done
    else
        print_error "无法读取bag包信息"
        exit 1
    fi
else
    print_error "ros2命令不可用"
    exit 1
fi

# 创建日志目录
LOG_DIR="test_logs/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$LOG_DIR"
print_info "测试日志将保存到: $LOG_DIR"

# 定义清理函数
cleanup() {
    print_info "正在清理进程..."
    if [ ! -z "$NAV_PID" ]; then
        kill $NAV_PID 2>/dev/null || true
        wait $NAV_PID 2>/dev/null || true
    fi
    if [ ! -z "$BAG_PID" ]; then
        kill $BAG_PID 2>/dev/null || true
        wait $BAG_PID 2>/dev/null || true
    fi
    if [ ! -z "$RECORD_PID" ]; then
        kill $RECORD_PID 2>/dev/null || true
        wait $RECORD_PID 2>/dev/null || true
    fi
    print_success "清理完成"
}

# 设置信号处理
trap cleanup EXIT INT TERM

print_info "启动NR_Navigation系统 ($MODE 模式)..."
./nr_navigation.bash $MODE > "$LOG_DIR/navigation.log" 2>&1 &
NAV_PID=$!

# 等待系统启动
print_info "等待导航系统启动 (10秒)..."
sleep 10

# 检查导航系统是否正常运行
if ! kill -0 $NAV_PID 2>/dev/null; then
    print_error "导航系统启动失败"
    print_info "检查日志: $LOG_DIR/navigation.log"
    exit 1
fi

print_success "导航系统启动成功"

# 开始记录测试数据
print_info "开始记录测试数据..."
ros2 bag record -o "$LOG_DIR/test_recording" \
    /plan /path /cmd_vel /scan /odom /tf /tf_static /diagnostics \
    > "$LOG_DIR/recording.log" 2>&1 &
RECORD_PID=$!

# 播放bag包
print_info "开始播放bag包: $BAG_DIR"
echo "播放选项:"
echo "  - 播放速度: 0.5倍速 (可通过修改脚本调整)"
echo "  - 模式: 正常播放"
echo ""

ros2 bag play "$BAG_DIR" --rate 0.5 > "$LOG_DIR/bag_play.log" 2>&1 &
BAG_PID=$!

print_success "测试运行中..."
print_info "监控信息:"
echo "  - 导航系统PID: $NAV_PID"
echo "  - Bag播放PID: $BAG_PID"  
echo "  - 数据记录PID: $RECORD_PID"
echo "  - 日志目录: $LOG_DIR"
echo ""
print_info "可以在另一个终端中运行以下命令进行监控:"
echo "  ros2 topic list"
echo "  ros2 topic echo /plan"
echo "  ros2 topic echo /cmd_vel"
echo "  rviz2"
echo ""
print_warning "按 Ctrl+C 停止测试"

# 等待bag包播放完成或用户中断
wait $BAG_PID 2>/dev/null || true

print_success "Bag包播放完成"

# 等待一段时间让系统处理完剩余数据
print_info "等待系统处理剩余数据 (5秒)..."
sleep 5

print_success "测试完成！"
print_info "测试结果保存在: $LOG_DIR"
print_info "查看测试数据:"
echo "  - 导航日志: $LOG_DIR/navigation.log"
echo "  - 播放日志: $LOG_DIR/bag_play.log" 
echo "  - 记录日志: $LOG_DIR/recording.log"
echo "  - 测试数据: $LOG_DIR/test_recording/"

# 生成简单的测试报告
print_info "生成测试报告..."
cat > "$LOG_DIR/test_report.txt" << EOF
NR_Navigation Bag包仿真测试报告
=====================================
测试时间: $(date)
Bag包路径: $BAG_DIR
导航模式: $MODE
日志目录: $LOG_DIR

测试配置:
- 播放速度: 0.5倍速
- 记录话题: /plan /path /cmd_vel /scan /odom /tf /tf_static /diagnostics

测试状态: 完成

注意事项:
1. 请检查日志文件确认无错误
2. 可使用以下命令分析记录的数据:
   ros2 bag info $LOG_DIR/test_recording/
3. 建议在RViz2中查看可视化结果
EOF

print_success "测试报告已生成: $LOG_DIR/test_report.txt"