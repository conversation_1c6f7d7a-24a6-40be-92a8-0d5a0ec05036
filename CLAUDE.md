# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a ROS2 robotics navigation system (nr_navigation) that has been migrated from ROS1 to ROS2 Humble. The system provides autonomous robot navigation capabilities including global path planning, local trajectory planning, terrain analysis, and velocity smoothing for both indoor and outdoor environments.

## Build System & Commands

### Essential Commands

**Build the entire system:**
```bash
./nr_navigation.bash build
# or directly:
./build_ros2.bash
```

**Clean rebuild:**
```bash
./nr_navigation.bash rebuild
# or:
./rebuild_ros2.bash
```

**Test system integrity:**
```bash
./nr_navigation.bash test
```

**Launch navigation systems:**
```bash
./nr_navigation.bash indoor    # Indoor navigation
./nr_navigation.bash outdoor   # Outdoor navigation
```

**Manual colcon commands:**
```bash
# Build specific package
colcon build --packages-select <package_name>

# Build with release optimizations
colcon build --cmake-args -DCMAKE_BUILD_TYPE=Release

# Source the workspace after building
source install/setup.bash
```

### Testing Commands

**Individual node testing:**
```bash
# Test individual components
ros2 run terrain_analysis terrain_analysis
ros2 run yocs_velocity_smoother velocity_smoother_node  
ros2 run global_traj_generate global_traj_generate_node
ros2 run local_planner localPlanner
ros2 run local_planner pathFollower
```

**System verification:**
```bash
# Check package availability
ros2 pkg list | grep -E "(global_traj|local_planner|rrt_star|terrain|yocs)"

# Monitor system topics
ros2 topic list | grep -E "(plan|path|goal|odom)"
ros2 topic echo /plan
```

## Architecture Overview

### Core Components

1. **rrt_star_global_planner** - RRT* algorithm-based global path planner, integrated as nav2 plugin
2. **global_traj_generate** - Global trajectory generator that processes planning requests
3. **local_planner** - Local trajectory planning with path following capabilities (localPlanner + pathFollower)
4. **terrain_analysis** - Point cloud terrain analysis using PatchWork++ algorithm
5. **yocs_velocity_smoother** - Velocity command smoothing for safe robot motion

### Package Dependencies & Structure

Each package follows ROS2 conventions:
- **Package format 3** (package.xml)
- **ament_cmake** build system
- **ROS2 message/service interfaces** (rosidl_default_generators)
- **rclcpp** for C++ nodes, **rclpy** for Python scripts

### Key Launch Files

- `launch/nr_navigation.launch.py` - Main system launcher (includes all components)
- `launch/complete_navigation.launch.py` - Complete navigation stack
- `launch/nr_navigation_test.launch.py` - Testing configuration

### Communication Architecture

The system uses custom message interfaces:
- **NavigationTarget.msg** - Target waypoint specification
- **NavigationResult.msg** - Planning result status

Main topics:
- `/plan` - Global path planning results
- `/cmd_vel` - Velocity commands (smoothed)
- `/scan` - Laser scan input
- `/odom` - Robot odometry
- `/tf` - Transform tree

### Environment-Specific Configuration

**Indoor setup:**
- Map file: `/root/DC200/nr_navigation/src/rrt_star_global_planner/maps/map.yaml`
- Optimized for structured environments

**Outdoor setup:**  
- Map file: `/home/<USER>/zhanting/NR_Navigation/src/rrt_star_global_planner/maps/cx_outdoor_rtk/map.yaml`
- Uses RTK GPS localization

## Migration Status & Known Issues

### Successfully Migrated (ROS1 → ROS2):
- ✅ All packages compile and install correctly
- ✅ Message interfaces converted to ROS2 format
- ✅ Launch system migrated to Python-based ros2 launch
- ✅ Navigation stack integrated with nav2_core
- ✅ TF system updated to tf2_ros

### Partially Migrated:
- ⚠️ rrt_star_plan_node.cpp contains legacy ROS1 code (disabled, uses nav2 plugin instead)
- ⚠️ Some parameter files may need ROS2 format updates (ros__parameters: root)

### Development Notes

- The system uses **colcon** for building (not catkin_make)
- **DDS communication** replaces roscore-based messaging
- **Component-based architecture** using rclcpp_components for better performance
- Launch files are in **Python** format, not XML
- Parameters follow **ROS2 parameter server** model

### File Organization

```
src/
├── global_traj_generate/     # Global trajectory planning
├── local_planner/           # Local planning & path following  
├── rrt_star_global_planner/ # RRT* global planner (nav2 plugin)
├── terrain_analysis/        # Point cloud terrain analysis
└── yocs_velocity_smoother/  # Velocity command smoothing
```

Each package contains standard ROS2 structure: `src/`, `include/`, `launch/`, `config/`, `msg/`, `package.xml`, `CMakeLists.txt`.

## Development Workflow

1. **Make changes** to source code
2. **Build incrementally**: `colcon build --packages-select <changed_package>`
3. **Source workspace**: `source install/setup.bash`  
4. **Test component**: `ros2 run <package> <executable>`
5. **Integration test**: `./nr_navigation.bash test`
6. **System test**: `./nr_navigation.bash indoor` or `outdoor`

Always verify the build succeeds before testing, as the system requires all packages to be properly built and sourced.