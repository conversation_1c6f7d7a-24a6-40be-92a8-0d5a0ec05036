#!/bin/bash

# ROS2 Build Script for NR_Navigation
# Author: Claude Code
# Description: Build all packages using colcon

set -e  # Exit on any error

echo "======================================="
echo "Building NR_Navigation ROS2 packages..."
echo "======================================="

# Check if we're in the correct directory
if [ ! -f "src/yocs_velocity_smoother/package.xml" ]; then
    echo "Error: Please run this script from the NR_Navigation root directory"
    exit 1
fi

# Set up ROS2 environment
if [ -f "/opt/ros/humble/setup.bash" ]; then
    source /opt/ros/humble/setup.bash
    echo "Sourced ROS2 Humble environment"
elif [ -f "/opt/ros/foxy/setup.bash" ]; then
    source /opt/ros/foxy/setup.bash
    echo "Sourced ROS2 Foxy environment"
else
    echo "Warning: No ROS2 environment found, assuming already sourced"
fi

# Install required dependencies
#echo "Installing required dependencies..."
#sudo apt update
#sudo apt install -y ros-humble-tf2-geometry-msgs ros-humble-nav2-core

# Build packages step by step
echo "Building packages with colcon..."

# First, build the basic packages that should work
echo "Step 1: Building yocs_velocity_smoother..."
colcon build --packages-select yocs_velocity_smoother --cmake-args -DCMAKE_BUILD_TYPE=Release

echo "Step 2: Building terrain_analysis..."
colcon build --packages-select terrain_analysis --cmake-args -DCMAKE_BUILD_TYPE=Release

echo "Step 3: Building global_traj_generate..."
colcon build --packages-select global_traj_generate --cmake-args -DCMAKE_BUILD_TYPE=Release

echo "Step 4: Building local_planner..."
colcon build --packages-select local_planner --cmake-args -DCMAKE_BUILD_TYPE=Release

echo "Step 5: Building rrt_star_global_planner..."
colcon build --packages-select rrt_star_global_planner --cmake-args -DCMAKE_BUILD_TYPE=Release

# Check build result
if [ $? -eq 0 ]; then
    echo "======================================="
    echo "Build completed successfully!"
    echo "======================================="
    echo "To use the built packages, run:"
    echo "source install/setup.bash"
    echo "======================================="
else
    echo "======================================="
    echo "Build failed!"
    echo "======================================="
    exit 1
fi