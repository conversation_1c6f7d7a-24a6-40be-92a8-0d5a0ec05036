#!/bin/bash

# ROS2 Outdoor Navigation Launch Script
# Author: Claude Code
# Description: Launch NR_Navigation system for outdoor environment

set -e  # Exit on any error

echo "======================================="
echo "Starting Outdoor Navigation System..."
echo "======================================="

# Check if we're in the correct directory
if [ ! -f "install/setup.bash" ]; then
    echo "Error: Please build the packages first using ./build_ros2.bash"
    exit 1
fi

# Source the workspace
source install/setup.bash
echo "Sourced workspace environment"

# Set outdoor-specific parameters
export OUTDOOR_MAP_FILE="/home/<USER>/zhanting/NR_Navigation/src/rrt_star_global_planner/maps/cx_outdoor_rtk/map.yaml"
export ROS_DOMAIN_ID=42  # Use domain ID to avoid conflicts

echo "Configuration:"
echo "  - Environment: Outdoor"
echo "  - Map file: $OUTDOOR_MAP_FILE"
echo "  - ROS Domain ID: $ROS_DOMAIN_ID"
echo "======================================="

# Launch the navigation system
echo "Launching outdoor navigation system..."
ros2 launch launch/nr_navigation.launch.py \
    map_file:="$OUTDOOR_MAP_FILE" \
    use_sim_time:=false

echo "======================================="
echo "Outdoor navigation system stopped."
echo "======================================="