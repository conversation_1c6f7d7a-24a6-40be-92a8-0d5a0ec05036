#!/bin/bash

# ROS2 Rebuild Script for NR_Navigation
# Author: Claude Code
# Description: Clean and rebuild all packages using colcon

set -e  # Exit on any error

echo "======================================="
echo "Cleaning and rebuilding NR_Navigation..."
echo "======================================="

# Check if we're in the correct directory
if [ ! -f "src/yocs_velocity_smoother/package.xml" ]; then
    echo "Error: Please run this script from the NR_Navigation root directory"
    exit 1
fi

# Clean build artifacts
echo "Cleaning previous build artifacts..."
rm -rf build/ install/ log/

# Set up ROS2 environment
if [ -f "/opt/ros/humble/setup.bash" ]; then
    source /opt/ros/humble/setup.bash
    echo "Sourced ROS2 Humble environment"
elif [ -f "/opt/ros/foxy/setup.bash" ]; then
    source /opt/ros/foxy/setup.bash
    echo "Sourced ROS2 Foxy environment"
else
    echo "Warning: No ROS2 environment found, assuming already sourced"
fi

# Rebuild packages with colcon
echo "Rebuilding packages with colcon..."
colcon build \
    --packages-select \
    yocs_velocity_smoother \
    terrain_analysis \
    global_traj_generate \
    rrt_star_global_planner \
    local_planner \
    --cmake-args -DCMAKE_BUILD_TYPE=Release \
    --parallel-workers 4

# Check build result
if [ $? -eq 0 ]; then
    echo "======================================="
    echo "Rebuild completed successfully!"
    echo "======================================="
    echo "To use the built packages, run:"
    echo "source install/setup.bash"
    echo "======================================="
else
    echo "======================================="
    echo "Rebuild failed!"
    echo "======================================="
    exit 1
fi